var $d=Object.defineProperty,Ud=Object.defineProperties;var zd=Object.getOwnPropertyDescriptors;var bn=Object.getOwnPropertySymbols;var pa=Object.prototype.hasOwnProperty,ha=Object.prototype.propertyIsEnumerable;var fa=(e,t,n)=>t in e?$d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,De=(e,t)=>{for(var n in t||={})pa.call(t,n)&&fa(e,n,t[n]);if(bn)for(var n of bn(t))ha.call(t,n)&&fa(e,n,t[n]);return e},Ee=(e,t)=>Ud(e,zd(t));var GD=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var WD=(e,t)=>{var n={};for(var r in e)pa.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&bn)for(var r of bn(e))t.indexOf(r)<0&&ha.call(e,r)&&(n[r]=e[r]);return n};var ga=(e,t,n)=>new Promise((r,o)=>{var i=u=>{try{a(n.next(u))}catch(c){o(c)}},s=u=>{try{a(n.throw(u))}catch(c){o(c)}},a=u=>u.done?r(u.value):Promise.resolve(u.value).then(i,s);a((n=n.apply(e,t)).next())});function No(e,t){return Object.is(e,t)}var H=null,_n=!1,xo=1,X=Symbol("SIGNAL");function M(e){let t=H;return H=e,t}function ma(){return H}var Jt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Tn(e){if(_n)throw new Error("");if(H===null)return;H.consumerOnSignalRead(e);let t=H.nextProducerIndex++;if(xn(H),t<H.producerNode.length&&H.producerNode[t]!==e&&Kt(H)){let n=H.producerNode[t];Nn(n,H.producerIndexOfThis[t])}H.producerNode[t]!==e&&(H.producerNode[t]=e,H.producerIndexOfThis[t]=Kt(H)?Da(e,H,t):0),H.producerLastReadVersion[t]=e.version}function Gd(){xo++}function Ao(e){if(!(Kt(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===xo)){if(!e.producerMustRecompute(e)&&!Ro(e)){So(e);return}e.producerRecomputeValue(e),So(e)}}function ya(e){if(e.liveConsumerNode===void 0)return;let t=_n;_n=!0;try{for(let n of e.liveConsumerNode)n.dirty||Wd(n)}finally{_n=t}}function va(){return H?.consumerAllowSignalWrites!==!1}function Wd(e){e.dirty=!0,ya(e),e.consumerMarkedDirty?.(e)}function So(e){e.dirty=!1,e.lastCleanEpoch=xo}function Sn(e){return e&&(e.nextProducerIndex=0),M(e)}function Oo(e,t){if(M(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Kt(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Nn(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Ro(e){xn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ao(n),r!==n.version))return!0}return!1}function Fo(e){if(xn(e),Kt(e))for(let t=0;t<e.producerNode.length;t++)Nn(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Da(e,t,n){if(Ea(e),e.liveConsumerNode.length===0&&Ia(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Da(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Nn(e,t){if(Ea(e),e.liveConsumerNode.length===1&&Ia(e))for(let r=0;r<e.producerNode.length;r++)Nn(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];xn(o),o.producerIndexOfThis[r]=t}}function Kt(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function xn(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Ea(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ia(e){return e.producerNode!==void 0}function wa(e){let t=Object.create(qd);t.computation=e;let n=()=>{if(Ao(t),Tn(t),t.value===Mn)throw t.error;return t.value};return n[X]=t,n}var Mo=Symbol("UNSET"),To=Symbol("COMPUTING"),Mn=Symbol("ERRORED"),qd=Ee(De({},Jt),{value:Mo,dirty:!0,error:null,equal:No,kind:"computed",producerMustRecompute(e){return e.value===Mo||e.value===To},producerRecomputeValue(e){if(e.value===To)throw new Error("Detected cycle in computations.");let t=e.value;e.value=To;let n=Sn(e),r,o=!1;try{r=e.computation(),M(null),o=t!==Mo&&t!==Mn&&r!==Mn&&e.equal(t,r)}catch(i){r=Mn,e.error=i}finally{Oo(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Zd(){throw new Error}var Ca=Zd;function ba(){Ca()}function _a(e){Ca=e}var Qd=null;function Ma(e){let t=Object.create(ko);t.value=e;let n=()=>(Tn(t),t.value);return n[X]=t,n}function An(e,t){va()||ba(),e.equal(e.value,t)||(e.value=t,Yd(e))}function Ta(e,t){va()||ba(),An(e,t(e.value))}var ko=Ee(De({},Jt),{equal:No,value:void 0,kind:"signal"});function Yd(e){e.version++,Gd(),ya(e),Qd?.()}function m(e){return typeof e=="function"}function Tt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var On=Tt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function nt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var j=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(m(r))try{r()}catch(i){t=i instanceof On?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Sa(i)}catch(s){t=t??[],s instanceof On?t=[...t,...s.errors]:t.push(s)}}if(t)throw new On(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Sa(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&nt(n,t)}remove(t){let{_finalizers:n}=this;n&&nt(n,t),t instanceof e&&t._removeParent(this)}};j.EMPTY=(()=>{let e=new j;return e.closed=!0,e})();var Po=j.EMPTY;function Rn(e){return e instanceof j||e&&"closed"in e&&m(e.remove)&&m(e.add)&&m(e.unsubscribe)}function Sa(e){m(e)?e():e.unsubscribe()}var fe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var St={setTimeout(e,t,...n){let{delegate:r}=St;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=St;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Fn(e){St.setTimeout(()=>{let{onUnhandledError:t}=fe;if(t)t(e);else throw e})}function Xt(){}var Na=Lo("C",void 0,void 0);function xa(e){return Lo("E",void 0,e)}function Aa(e){return Lo("N",e,void 0)}function Lo(e,t,n){return{kind:e,value:t,error:n}}var rt=null;function Nt(e){if(fe.useDeprecatedSynchronousErrorHandling){let t=!rt;if(t&&(rt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=rt;if(rt=null,n)throw r}}else e()}function Oa(e){fe.useDeprecatedSynchronousErrorHandling&&rt&&(rt.errorThrown=!0,rt.error=e)}var ot=class extends j{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Rn(t)&&t.add(this)):this.destination=Xd}static create(t,n,r){return new He(t,n,r)}next(t){this.isStopped?jo(Aa(t),this):this._next(t)}error(t){this.isStopped?jo(xa(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?jo(Na,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Kd=Function.prototype.bind;function Vo(e,t){return Kd.call(e,t)}var Bo=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){kn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){kn(r)}else kn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){kn(n)}}},He=class extends ot{constructor(t,n,r){super();let o;if(m(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&fe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Vo(t.next,i),error:t.error&&Vo(t.error,i),complete:t.complete&&Vo(t.complete,i)}):o=t}this.destination=new Bo(o)}};function kn(e){fe.useDeprecatedSynchronousErrorHandling?Oa(e):Fn(e)}function Jd(e){throw e}function jo(e,t){let{onStoppedNotification:n}=fe;n&&St.setTimeout(()=>n(e,t))}var Xd={closed:!0,next:Xt,error:Jd,complete:Xt};var xt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function G(e){return e}function ef(...e){return Ho(e)}function Ho(e){return e.length===0?G:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var T=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=nf(n)?n:new He(n,r,o);return Nt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ra(r),new r((o,i)=>{let s=new He({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[xt](){return this}pipe(...n){return Ho(n)(this)}toPromise(n){return n=Ra(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ra(e){var t;return(t=e??fe.Promise)!==null&&t!==void 0?t:Promise}function tf(e){return e&&m(e.next)&&m(e.error)&&m(e.complete)}function nf(e){return e&&e instanceof ot||tf(e)&&Rn(e)}function $o(e){return m(e?.lift)}function v(e){return t=>{if($o(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function y(e,t,n,r,o){return new Uo(e,t,n,r,o)}var Uo=class extends ot{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function zo(){return v((e,t)=>{let n=null;e._refCount++;let r=y(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Go=class extends T{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,$o(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new j;let n=this.getSubject();t.add(this.source.subscribe(y(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=j.EMPTY)}return t}refCount(){return zo()(this)}};var Fa=Tt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ie=(()=>{class e extends T{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Pn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Fa}next(n){Nt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Nt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Nt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Po:(this.currentObservers=null,i.push(n),new j(()=>{this.currentObservers=null,nt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new T;return n.source=this,n}}return e.create=(t,n)=>new Pn(t,n),e})(),Pn=class extends Ie{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Po}};var en=class extends Ie{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Wo={now(){return(Wo.delegate||Date).now()},delegate:void 0};var Ln=class extends j{constructor(t,n){super()}schedule(t,n=0){return this}};var tn={setInterval(e,t,...n){let{delegate:r}=tn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=tn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Vn=class extends Ln{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return tn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&tn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,nt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var At=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};At.now=Wo.now;var jn=class extends At{constructor(t,n=At.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var nn=new jn(Vn),ka=nn;var it=new T(e=>e.complete());function Bn(e){return e&&m(e.schedule)}function qo(e){return e[e.length-1]}function Hn(e){return m(qo(e))?e.pop():void 0}function we(e){return Bn(qo(e))?e.pop():void 0}function Pa(e,t){return typeof qo(e)=="number"?e.pop():t}function Va(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function La(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function st(e){return this instanceof st?(this.v=e,this):new st(e)}function ja(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(w){return new Promise(function(P,A){i.push([f,w,P,A])>1||u(f,w)})},h&&(o[f]=h(o[f])))}function u(f,h){try{c(r[f](h))}catch(w){p(i[0][3],w)}}function c(f){f.value instanceof st?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){u("next",f)}function d(f){u("throw",f)}function p(f,h){f(h),i.shift(),i.length&&u(i[0][0],i[0][1])}}function Ba(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof La=="function"?La(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var $n=e=>e&&typeof e.length=="number"&&typeof e!="function";function Un(e){return m(e?.then)}function zn(e){return m(e[xt])}function Gn(e){return Symbol.asyncIterator&&m(e?.[Symbol.asyncIterator])}function Wn(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function rf(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var qn=rf();function Zn(e){return m(e?.[qn])}function Qn(e){return ja(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield st(n.read());if(o)return yield st(void 0);yield yield st(r)}}finally{n.releaseLock()}})}function Yn(e){return m(e?.getReader)}function F(e){if(e instanceof T)return e;if(e!=null){if(zn(e))return of(e);if($n(e))return sf(e);if(Un(e))return af(e);if(Gn(e))return Ha(e);if(Zn(e))return uf(e);if(Yn(e))return cf(e)}throw Wn(e)}function of(e){return new T(t=>{let n=e[xt]();if(m(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function sf(e){return new T(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function af(e){return new T(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Fn)})}function uf(e){return new T(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Ha(e){return new T(t=>{lf(e,t).catch(n=>t.error(n))})}function cf(e){return Ha(Qn(e))}function lf(e,t){var n,r,o,i;return Va(this,void 0,void 0,function*(){try{for(n=Ba(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Y(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Kn(e,t=0){return v((n,r)=>{n.subscribe(y(r,o=>Y(r,e,()=>r.next(o),t),()=>Y(r,e,()=>r.complete(),t),o=>Y(r,e,()=>r.error(o),t)))})}function Jn(e,t=0){return v((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function $a(e,t){return F(e).pipe(Jn(t),Kn(t))}function Ua(e,t){return F(e).pipe(Jn(t),Kn(t))}function za(e,t){return new T(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Ga(e,t){return new T(n=>{let r;return Y(n,t,()=>{r=e[qn](),Y(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>m(r?.return)&&r.return()})}function Xn(e,t){if(!e)throw new Error("Iterable cannot be null");return new T(n=>{Y(n,t,()=>{let r=e[Symbol.asyncIterator]();Y(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Wa(e,t){return Xn(Qn(e),t)}function qa(e,t){if(e!=null){if(zn(e))return $a(e,t);if($n(e))return za(e,t);if(Un(e))return Ua(e,t);if(Gn(e))return Xn(e,t);if(Zn(e))return Ga(e,t);if(Yn(e))return Wa(e,t)}throw Wn(e)}function Ce(e,t){return t?qa(e,t):F(e)}function df(...e){let t=we(e);return Ce(e,t)}function ff(e,t){let n=m(e)?e:()=>e,r=o=>o.error(n());return new T(t?o=>t.schedule(r,0,o):r)}function pf(e){return!!e&&(e instanceof T||m(e.lift)&&m(e.subscribe))}var Ae=Tt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function hf(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new He({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new Ae)}});e.subscribe(i)})}function Za(e){return e instanceof Date&&!isNaN(e)}function at(e,t){return v((n,r)=>{let o=0;n.subscribe(y(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:gf}=Array;function mf(e,t){return gf(t)?e(...t):e(t)}function er(e){return at(t=>mf(e,t))}var{isArray:yf}=Array,{getPrototypeOf:vf,prototype:Df,keys:Ef}=Object;function tr(e){if(e.length===1){let t=e[0];if(yf(t))return{args:t,keys:null};if(If(t)){let n=Ef(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function If(e){return e&&typeof e=="object"&&vf(e)===Df}function nr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function wf(...e){let t=we(e),n=Hn(e),{args:r,keys:o}=tr(e);if(r.length===0)return Ce([],t);let i=new T(Cf(r,t,o?s=>nr(o,s):G));return n?i.pipe(er(n)):i}function Cf(e,t,n=G){return r=>{Qa(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)Qa(t,()=>{let c=Ce(e[u],t),l=!1;c.subscribe(y(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Qa(e,t,n){e?Y(n,e,t):t()}function Ya(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,p=()=>{d&&!u.length&&!c&&t.complete()},f=w=>c<r?h(w):u.push(w),h=w=>{i&&t.next(w),c++;let P=!1;F(n(w,l++)).subscribe(y(t,A=>{o?.(A),i?f(A):t.next(A)},()=>{P=!0},void 0,()=>{if(P)try{for(c--;u.length&&c<r;){let A=u.shift();s?Y(t,s,()=>h(A)):h(A)}p()}catch(A){t.error(A)}}))};return e.subscribe(y(t,f,()=>{d=!0,p()})),()=>{a?.()}}function ut(e,t,n=1/0){return m(t)?ut((r,o)=>at((i,s)=>t(r,i,o,s))(F(e(r,o))),n):(typeof t=="number"&&(n=t),v((r,o)=>Ya(r,o,e,n)))}function rn(e=1/0){return ut(G,e)}function Ka(){return rn(1)}function rr(...e){return Ka()(Ce(e,we(e)))}function bf(e){return new T(t=>{F(e()).subscribe(t)})}function _f(...e){let t=Hn(e),{args:n,keys:r}=tr(e),o=new T(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;F(n[l]).subscribe(y(i,p=>{d||(d=!0,c--),a[l]=p},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?nr(r,a):a),i.complete())}))}});return t?o.pipe(er(t)):o}function Ja(e=0,t,n=ka){let r=-1;return t!=null&&(Bn(t)?n=t:r=t),new T(o=>{let i=Za(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Mf(...e){let t=we(e),n=Pa(e,1/0),r=e;return r.length?r.length===1?F(r[0]):rn(n)(Ce(r,t)):it}function ct(e,t){return v((n,r)=>{let o=0;n.subscribe(y(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Xa(e){return v((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};t.subscribe(y(n,c=>{r=!0,o=c,i||F(e(c)).subscribe(i=y(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Tf(e,t=nn){return Xa(()=>Ja(e,t))}function eu(e){return v((t,n)=>{let r=null,o=!1,i;r=t.subscribe(y(n,void 0,void 0,s=>{i=F(e(s,eu(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function tu(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(y(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function Sf(e,t){return m(t)?ut(e,t,1):ut(e,1)}function Nf(e,t=nn){return v((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(y(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function on(e){return v((t,n)=>{let r=!1;t.subscribe(y(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Zo(e){return e<=0?()=>it:v((t,n)=>{let r=0;t.subscribe(y(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function xf(e,t=G){return e=e??Af,v((n,r)=>{let o,i=!0;n.subscribe(y(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Af(e,t){return e===t}function or(e=Of){return v((t,n)=>{let r=!1;t.subscribe(y(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Of(){return new Ae}function Rf(e){return v((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ff(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ct((o,i)=>e(o,i,r)):G,Zo(1),n?on(t):or(()=>new Ae))}function Qo(e){return e<=0?()=>it:v((t,n)=>{let r=[];t.subscribe(y(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function kf(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ct((o,i)=>e(o,i,r)):G,Qo(1),n?on(t):or(()=>new Ae))}function Pf(e,t){return v(tu(e,t,arguments.length>=2,!0))}function Lf(e){return ct((t,n)=>e<=n)}function Vf(...e){let t=we(e);return v((n,r)=>{(t?rr(e,n,t):rr(e,n)).subscribe(r)})}function jf(e,t){return v((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(y(r,u=>{o?.unsubscribe();let c=0,l=i++;F(e(u,l)).subscribe(o=y(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Bf(e){return v((t,n)=>{F(e).subscribe(y(n,()=>n.complete(),Xt)),!n.closed&&t.subscribe(n)})}function Hf(e,t,n){let r=m(e)||t||n?{next:e,error:t,complete:n}:e;return r?v((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(y(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):G}var Gu="https://g.co/ng/security#xss",b=class extends Error{code;constructor(t,n){super(Wu(t,n)),this.code=t}};function Wu(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var qu=Symbol("InputSignalNode#UNSET"),$f=Ee(De({},ko),{transformFn:void 0,applyValueToInputSignal(e,t){An(e,t)}});function Zu(e,t){let n=Object.create($f);n.value=e,n.transformFn=t?.transform;function r(){if(Tn(n),n.value===qu)throw new b(-950,!1);return n.value}return r[X]=n,r}function mn(e){return{toString:e}.toString()}var ir="__parameters__";function Uf(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Qu(e,t,n){return mn(()=>{let r=Uf(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(ir)?u[ir]:Object.defineProperty(u,ir,{value:[]})[ir];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ue=globalThis;function N(e){for(let t in e)if(e[t]===N)return t;throw Error("Could not find renamed property on target object.")}function zf(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function q(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(q).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function ci(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Gf=N({__forward_ref__:N});function Yu(e){return e.__forward_ref__=Yu,e.toString=function(){return q(this())},e}function W(e){return Ku(e)?e():e}function Ku(e){return typeof e=="function"&&e.hasOwnProperty(Gf)&&e.__forward_ref__===Yu}function R(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Ju(e){return{providers:e.providers||[],imports:e.imports||[]}}function zr(e){return nu(e,Xu)||nu(e,ec)}function FM(e){return zr(e)!==null}function nu(e,t){return e.hasOwnProperty(t)?e[t]:null}function Wf(e){let t=e&&(e[Xu]||e[ec]);return t||null}function ru(e){return e&&(e.hasOwnProperty(ou)||e.hasOwnProperty(qf))?e[ou]:null}var Xu=N({\u0275prov:N}),ou=N({\u0275inj:N}),ec=N({ngInjectableDef:N}),qf=N({ngInjectorDef:N}),S=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=R({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function tc(e){return e&&!!e.\u0275providers}var Zf=N({\u0275cmp:N}),Qf=N({\u0275dir:N}),Yf=N({\u0275pipe:N}),Kf=N({\u0275mod:N}),yr=N({\u0275fac:N}),cn=N({__NG_ELEMENT_ID__:N}),iu=N({__NG_ENV_ID__:N});function Gr(e){return typeof e=="string"?e:e==null?"":String(e)}function Jf(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Gr(e)}function Xf(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new b(-200,e)}function gs(e,t){throw new b(-201,!1)}var C=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(C||{}),li;function nc(){return li}function K(e){let t=li;return li=e,t}function rc(e,t,n){let r=zr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&C.Optional)return null;if(t!==void 0)return t;gs(e,"Injector")}var ep={},ln=ep,di="__NG_DI_FLAG__",vr="ngTempTokenPath",tp="ngTokenPath",np=/\n/gm,rp="\u0275",su="__source",Pt;function op(){return Pt}function $e(e){let t=Pt;return Pt=e,t}function ip(e,t=C.Default){if(Pt===void 0)throw new b(-203,!1);return Pt===null?rc(e,void 0,t):Pt.get(e,t&C.Optional?null:void 0,t)}function U(e,t=C.Default){return(nc()||ip)(W(e),t)}function D(e,t=C.Default){return U(e,Wr(t))}function Wr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function fi(e){let t=[];for(let n=0;n<e.length;n++){let r=W(e[n]);if(Array.isArray(r)){if(r.length===0)throw new b(900,!1);let o,i=C.Default;for(let s=0;s<r.length;s++){let a=r[s],u=sp(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(U(o,i))}else t.push(U(r))}return t}function oc(e,t){return e[di]=t,e.prototype[di]=t,e}function sp(e){return e[di]}function ap(e,t,n,r){let o=e[vr];throw t[su]&&o.unshift(t[su]),e.message=up(`
`+e.message,o,n,r),e[tp]=o,e[vr]=null,e}function up(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==rp?e.slice(2):e;let o=q(t);if(Array.isArray(t))o=t.map(q).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):q(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(np,`
  `)}`}var cp=oc(Qu("Optional"),8);var lp=oc(Qu("SkipSelf"),4);function ft(e,t){let n=e.hasOwnProperty(yr);return n?e[yr]:null}function dp(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function fp(e){return e.flat(Number.POSITIVE_INFINITY)}function ms(e,t){e.forEach(n=>Array.isArray(n)?ms(n,t):t(n))}function ic(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Dr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function pp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ys(e,t,n){let r=yn(e,t);return r>=0?e[r|1]=n:(r=~r,pp(e,r,t,n)),r}function Yo(e,t){let n=yn(e,t);if(n>=0)return e[n|1]}function yn(e,t){return hp(e,t,1)}function hp(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Vt={},J=[],Er=new S(""),sc=new S("",-1),ac=new S(""),Ir=class{get(t,n=ln){if(n===ln){let r=new Error(`NullInjectorError: No provider for ${q(t)}!`);throw r.name="NullInjectorError",r}return n}};function uc(e,t){let n=e[Kf]||null;if(!n&&t===!0)throw new Error(`Type ${q(e)} does not have '\u0275mod' property.`);return n}function Oe(e){return e[Zf]||null}function cc(e){return e[Qf]||null}function lc(e){return e[Yf]||null}function gp(e){let t=Oe(e)||cc(e)||lc(e);return t!==null&&t.standalone}function mp(e){return{\u0275providers:e}}function yp(...e){return{\u0275providers:dc(!0,e),\u0275fromNgModule:!0}}function dc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ms(t,s=>{let a=s;pi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&fc(o,i),n}function fc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];vs(o,i=>{t(i,r)})}}function pi(e,t,n,r){if(e=W(e),!e)return!1;let o=null,i=ru(e),s=!i&&Oe(e);if(!i&&!s){let u=e.ngModule;if(i=ru(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)pi(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{ms(i.imports,l=>{pi(l,t,n,r)&&(c||=[],c.push(l))})}finally{}c!==void 0&&fc(c,t)}if(!a){let c=ft(o)||(()=>new o);t({provide:o,useFactory:c,deps:J},o),t({provide:ac,useValue:o,multi:!0},o),t({provide:Er,useValue:()=>U(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;vs(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function vs(e,t){for(let n of e)tc(n)&&(n=n.\u0275providers),Array.isArray(n)?vs(n,t):t(n)}var vp=N({provide:String,useValue:N});function pc(e){return e!==null&&typeof e=="object"&&vp in e}function Dp(e){return!!(e&&e.useExisting)}function Ep(e){return!!(e&&e.useFactory)}function jt(e){return typeof e=="function"}function Ip(e){return!!e.useClass}var hc=new S(""),dr={},wp={},Ko;function qr(){return Ko===void 0&&(Ko=new Ir),Ko}var qe=class{},dn=class extends qe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,gi(t,s=>this.processProvider(s)),this.records.set(sc,Ot(void 0,this)),o.has("environment")&&this.records.set(qe,Ot(void 0,this));let i=this.records.get(hc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(ac,J,C.Self))}destroy(){an(this),this._destroyed=!0;let t=M(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),M(t)}}onDestroy(t){return an(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){an(this);let n=$e(this),r=K(void 0),o;try{return t()}finally{$e(n),K(r)}}get(t,n=ln,r=C.Default){if(an(this),t.hasOwnProperty(iu))return t[iu](this);r=Wr(r);let o,i=$e(this),s=K(void 0);try{if(!(r&C.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=Tp(t)&&zr(t);c&&this.injectableDefInScope(c)?u=Ot(hi(t),dr):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&C.Self?qr():this.parent;return n=r&C.Optional&&n===ln?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[vr]=a[vr]||[]).unshift(q(t)),i)throw a;return ap(a,t,"R3InjectorError",this.source)}else throw a}finally{K(s),$e(i)}}resolveInjectorInitializers(){let t=M(null),n=$e(this),r=K(void 0),o;try{let i=this.get(Er,J,C.Self);for(let s of i)s()}finally{$e(n),K(r),M(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(q(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=W(t);let n=jt(t)?t:W(t&&t.provide),r=bp(t);if(!jt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ot(void 0,dr,!0),o.factory=()=>fi(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=M(null);try{return n.value===dr&&(n.value=wp,n.value=n.factory()),typeof n.value=="object"&&n.value&&Mp(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{M(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=W(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function hi(e){let t=zr(e),n=t!==null?t.factory:ft(e);if(n!==null)return n;if(e instanceof S)throw new b(204,!1);if(e instanceof Function)return Cp(e);throw new b(204,!1)}function Cp(e){if(e.length>0)throw new b(204,!1);let n=Wf(e);return n!==null?()=>n.factory(e):()=>new e}function bp(e){if(pc(e))return Ot(void 0,e.useValue);{let t=gc(e);return Ot(t,dr)}}function gc(e,t,n){let r;if(jt(e)){let o=W(e);return ft(o)||hi(o)}else if(pc(e))r=()=>W(e.useValue);else if(Ep(e))r=()=>e.useFactory(...fi(e.deps||[]));else if(Dp(e))r=()=>U(W(e.useExisting));else{let o=W(e&&(e.useClass||e.provide));if(_p(e))r=()=>new o(...fi(e.deps));else return ft(o)||hi(o)}return r}function an(e){if(e.destroyed)throw new b(205,!1)}function Ot(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function _p(e){return!!e.deps}function Mp(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Tp(e){return typeof e=="function"||typeof e=="object"&&e instanceof S}function gi(e,t){for(let n of e)Array.isArray(n)?gi(n,t):n&&tc(n)?gi(n.\u0275providers,t):t(n)}function mc(e,t){e instanceof dn&&an(e);let n,r=$e(e),o=K(void 0);try{return t()}finally{$e(r),K(o)}}function yc(){return nc()!==void 0||op()!=null}function vc(e){if(!yc())throw new b(-203,!1)}function Sp(e){return typeof e=="function"}var Pe=0,E=1,g=2,z=3,ge=4,ye=5,wr=6,Cr=7,me=8,Bt=9,Re=10,k=11,fn=12,au=13,Zt=14,_e=15,pt=16,Rt=17,Fe=18,Zr=19,Dc=20,Ge=21,Jo=22,br=23,ee=24,te=25,Ec=1;var ht=7,_r=8,Ht=9,ne=10;function We(e){return Array.isArray(e)&&typeof e[Ec]=="object"}function Le(e){return Array.isArray(e)&&e[Ec]===!0}function Ds(e){return(e.flags&4)!==0}function Qr(e){return e.componentOffset>-1}function Yr(e){return(e.flags&1)===1}function Ze(e){return!!e.template}function mi(e){return(e[g]&512)!==0}function vn(e){return(e[g]&256)===256}var yi=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Ic(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Np=(()=>{let e=()=>wc;return e.ngInherit=!0,e})();function wc(e){return e.type.prototype.ngOnChanges&&(e.setInput=Ap),xp}function xp(){let e=bc(this),t=e?.current;if(t){let n=e.previous;if(n===Vt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Ap(e,t,n,r,o){let i=this.declaredInputs[r],s=bc(e)||Op(e,{previous:Vt,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new yi(c&&c.currentValue,n,u===Vt),Ic(e,t,o,n)}var Cc="__ngSimpleChanges__";function bc(e){return e[Cc]||null}function Op(e,t){return e[Cc]=t}var uu=null;var ae=function(e,t,n){uu?.(e,t,n)},_c="svg",Rp="math";function Me(e){for(;Array.isArray(e);)e=e[Pe];return e}function Mc(e,t){return Me(t[e])}function ue(e,t){return Me(t[e.index])}function Tc(e,t){return e.data[t]}function Sc(e,t){return e[t]}function Xe(e,t){let n=t[e];return We(n)?n:n[Pe]}function Fp(e){return(e[g]&4)===4}function Es(e){return(e[g]&128)===128}function kp(e){return Le(e[z])}function $t(e,t){return t==null?null:e[t]}function Nc(e){e[Rt]=0}function Is(e){e[g]&1024||(e[g]|=1024,Es(e)&&Jr(e))}function Pp(e,t){for(;e>0;)t=t[Zt],e--;return t}function Kr(e){return!!(e[g]&9216||e[ee]?.dirty)}function vi(e){e[Re].changeDetectionScheduler?.notify(9),e[g]&64&&(e[g]|=1024),Kr(e)&&Jr(e)}function Jr(e){e[Re].changeDetectionScheduler?.notify(0);let t=gt(e);for(;t!==null&&!(t[g]&8192||(t[g]|=8192,!Es(t)));)t=gt(t)}function xc(e,t){if(vn(e))throw new b(911,!1);e[Ge]===null&&(e[Ge]=[]),e[Ge].push(t)}function Lp(e,t){if(e[Ge]===null)return;let n=e[Ge].indexOf(t);n!==-1&&e[Ge].splice(n,1)}function gt(e){let t=e[z];return Le(t)?t[z]:t}function Ac(e){return e[Cr]??=[]}function Oc(e){return e.cleanup??=[]}function Vp(e,t,n,r){let o=Ac(t);o.push(n),e.firstCreatePass&&Oc(e).push(r,o.length-1)}var I={lFrame:Hc(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Di=!1;function jp(){return I.lFrame.elementDepthCount}function Bp(){I.lFrame.elementDepthCount++}function Hp(){I.lFrame.elementDepthCount--}function Rc(){return I.bindingsEnabled}function $p(){return I.skipHydrationRootTNode!==null}function Up(e){return I.skipHydrationRootTNode===e}function zp(){I.skipHydrationRootTNode=null}function _(){return I.lFrame.lView}function L(){return I.lFrame.tView}function kM(e){return I.lFrame.contextLView=e,e[me]}function PM(e){return I.lFrame.contextLView=null,e}function $(){let e=Fc();for(;e!==null&&e.type===64;)e=e.parent;return e}function Fc(){return I.lFrame.currentTNode}function Gp(){let e=I.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ct(e,t){let n=I.lFrame;n.currentTNode=e,n.isParent=t}function ws(){return I.lFrame.isParent}function kc(){I.lFrame.isParent=!1}function Wp(){return I.lFrame.contextLView}function Pc(){return Di}function cu(e){let t=Di;return Di=e,t}function Cs(){let e=I.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function qp(e){return I.lFrame.bindingIndex=e}function Dn(){return I.lFrame.bindingIndex++}function Lc(e){let t=I.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Zp(){return I.lFrame.inI18n}function Qp(e,t){let n=I.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ei(t)}function Yp(){return I.lFrame.currentDirectiveIndex}function Ei(e){I.lFrame.currentDirectiveIndex=e}function Kp(e){let t=I.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Vc(){return I.lFrame.currentQueryIndex}function bs(e){I.lFrame.currentQueryIndex=e}function Jp(e){let t=e[E];return t.type===2?t.declTNode:t.type===1?e[ye]:null}function jc(e,t,n){if(n&C.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&C.Host);)if(o=Jp(i),o===null||(i=i[Zt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=I.lFrame=Bc();return r.currentTNode=t,r.lView=e,!0}function _s(e){let t=Bc(),n=e[E];I.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Bc(){let e=I.lFrame,t=e===null?null:e.child;return t===null?Hc(e):t}function Hc(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function $c(){let e=I.lFrame;return I.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Uc=$c;function Ms(){let e=$c();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Xp(e){return(I.lFrame.contextLView=Pp(e,I.lFrame.contextLView))[me]}function bt(){return I.lFrame.selectedIndex}function mt(e){I.lFrame.selectedIndex=e}function Xr(){let e=I.lFrame;return Tc(e.tView,e.selectedIndex)}function LM(){I.lFrame.currentNamespace=_c}function eh(){return I.lFrame.currentNamespace}var zc=!0;function eo(){return zc}function to(e){zc=e}function th(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=wc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function no(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function fr(e,t,n){Gc(e,t,3,n)}function pr(e,t,n,r){(e[g]&3)===n&&Gc(e,t,n,r)}function Xo(e,t){let n=e[g];(n&3)===t&&(n&=16383,n+=1,e[g]=n)}function Gc(e,t,n,r){let o=r!==void 0?e[Rt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[Rt]+=65536),(a<i||i==-1)&&(nh(e,n,t,u),e[Rt]=(e[Rt]&**********)+u+2),u++}function lu(e,t){ae(4,e,t);let n=M(null);try{t.call(e)}finally{M(n),ae(5,e,t)}}function nh(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[g]>>14<e[Rt]>>16&&(e[g]&3)===t&&(e[g]+=16384,lu(a,i)):lu(a,i)}var Lt=-1,yt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function rh(e){return e instanceof yt}function oh(e){return(e.flags&8)!==0}function ih(e){return(e.flags&16)!==0}function sh(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];ah(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Wc(e){return e===3||e===4||e===6}function ah(e){return e.charCodeAt(0)===64}function Ut(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?du(e,n,o,null,t[++r]):du(e,n,o,null,null))}}return e}function du(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var ei={},Ii=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Wr(r);let o=this.injector.get(t,ei,r);return o!==ei||n===ei?o:this.parentInjector.get(t,n,r)}};function qc(e){return e!==Lt}function Mr(e){return e&32767}function uh(e){return e>>16}function Tr(e,t){let n=uh(e),r=t;for(;n>0;)r=r[Zt],n--;return r}var wi=!0;function Sr(e){let t=wi;return wi=e,t}var ch=256,Zc=ch-1,Qc=5,lh=0,be={};function dh(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(cn)&&(r=n[cn]),r==null&&(r=n[cn]=lh++);let o=r&Zc,i=1<<o;t.data[e+(o>>Qc)]|=i}function Nr(e,t){let n=Yc(e,t);if(n!==-1)return n;let r=t[E];r.firstCreatePass&&(e.injectorIndex=t.length,ti(r.data,e),ti(t,null),ti(r.blueprint,null));let o=Ts(e,t),i=e.injectorIndex;if(qc(o)){let s=Mr(o),a=Tr(o,t),u=a[E].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function ti(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Yc(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ts(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=tl(o),r===null)return Lt;if(n++,o=o[Zt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Lt}function Ci(e,t,n){dh(e,t,n)}function fh(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Wc(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Kc(e,t,n){if(n&C.Optional||e!==void 0)return e;gs(t,"NodeInjector")}function Jc(e,t,n,r){if(n&C.Optional&&r===void 0&&(r=null),!(n&(C.Self|C.Host))){let o=e[Bt],i=K(void 0);try{return o?o.get(t,r,n&C.Optional):rc(t,r,n&C.Optional)}finally{K(i)}}return Kc(r,t,n)}function Xc(e,t,n,r=C.Default,o){if(e!==null){if(t[g]&2048&&!(r&C.Self)){let s=mh(e,t,n,r,be);if(s!==be)return s}let i=el(e,t,n,r,be);if(i!==be)return i}return Jc(t,n,r,o)}function el(e,t,n,r,o){let i=hh(n);if(typeof i=="function"){if(!jc(t,e,r))return r&C.Host?Kc(o,n,r):Jc(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&C.Optional))gs(n);else return s}finally{Uc()}}else if(typeof i=="number"){let s=null,a=Yc(e,t),u=Lt,c=r&C.Host?t[_e][ye]:null;for((a===-1||r&C.SkipSelf)&&(u=a===-1?Ts(e,t):t[a+8],u===Lt||!pu(r,!1)?a=-1:(s=t[E],a=Mr(u),t=Tr(u,t)));a!==-1;){let l=t[E];if(fu(i,a,l.data)){let d=ph(a,t,n,s,r,c);if(d!==be)return d}u=t[a+8],u!==Lt&&pu(r,t[E].data[a+8]===c)&&fu(i,a,t)?(s=l,a=Mr(u),t=Tr(u,t)):a=-1}}return o}function ph(e,t,n,r,o,i){let s=t[E],a=s.data[e+8],u=r==null?Qr(a)&&wi:r!=s&&(a.type&3)!==0,c=o&C.Host&&i===a,l=hr(a,s,n,u,c);return l!==null?vt(t,s,l,a):be}function hr(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:c;for(let f=d;f<p;f++){let h=s[f];if(f<u&&n===h||f>=u&&h.type===n)return f}if(o){let f=s[u];if(f&&Ze(f)&&f.type===n)return u}return null}function vt(e,t,n,r){let o=e[n],i=t.data;if(rh(o)){let s=o;s.resolving&&Xf(Jf(i[n]));let a=Sr(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?K(s.injectImpl):null,l=jc(e,r,C.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&th(n,i[n],t)}finally{c!==null&&K(c),Sr(a),s.resolving=!1,Uc()}}return o}function hh(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(cn)?e[cn]:void 0;return typeof t=="number"?t>=0?t&Zc:gh:t}function fu(e,t,n){let r=1<<e;return!!(n[t+(e>>Qc)]&r)}function pu(e,t){return!(e&C.Self)&&!(e&C.Host&&t)}var dt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Xc(this._tNode,this._lView,t,Wr(r),n)}};function gh(){return new dt($(),_())}function VM(e){return mn(()=>{let t=e.prototype.constructor,n=t[yr]||bi(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[yr]||bi(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function bi(e){return Ku(e)?()=>{let t=bi(W(e));return t&&t()}:ft(e)}function mh(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[g]&2048&&!(s[g]&512);){let a=el(i,s,n,r|C.Self,be);if(a!==be)return a;let u=i.parent;if(!u){let c=s[Dc];if(c){let l=c.get(n,be,r);if(l!==be)return l}u=tl(s),s=s[Zt]}i=u}return o}function tl(e){let t=e[E],n=t.type;return n===2?t.declTNode:n===1?e[ye]:null}function yh(e){return fh($(),e)}function hu(e,t=null,n=null,r){let o=nl(e,t,n,r);return o.resolveInjectorInitializers(),o}function nl(e,t=null,n=null,r,o=new Set){let i=[n||J,yp(e)];return r=r||(typeof e=="object"?void 0:q(e)),new dn(i,t||qr(),r||null,o)}var Qe=class e{static THROW_IF_NOT_FOUND=ln;static NULL=new Ir;static create(t,n){if(Array.isArray(t))return hu({name:""},n,t,"");{let r=t.name??"";return hu({name:r},t.parent,t.providers,r)}}static \u0275prov=R({token:e,providedIn:"any",factory:()=>U(sc)});static __NG_ELEMENT_ID__=-1};var vh=new S("");vh.__NG_ELEMENT_ID__=e=>{let t=$();if(t===null)throw new b(204,!1);if(t.type&2)return t.value;if(e&C.Optional)return null;throw new b(204,!1)};var rl=!1,Ss=(()=>{class e{static __NG_ELEMENT_ID__=Dh;static __NG_ENV_ID__=n=>n}return e})(),_i=class extends Ss{_lView;constructor(t){super(),this._lView=t}onDestroy(t){return xc(this._lView,t),()=>Lp(this._lView,t)}};function Dh(){return new _i(_())}var zt=class{},Ns=new S("",{providedIn:"root",factory:()=>!1});var ol=new S(""),il=new S(""),ro=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new en(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=R({token:e,providedIn:"root",factory:()=>new e})}return e})();var Mi=class extends Ie{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,yc()&&(this.destroyRef=D(Ss,{optional:!0})??void 0,this.pendingTasks=D(ro,{optional:!0})??void 0)}emit(t){let n=M(null);try{super.next(t)}finally{M(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof j&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},ze=Mi;function xr(...e){}function sl(e){let t,n;function r(){e=xr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function gu(e){return queueMicrotask(()=>e()),()=>{e=xr}}var xs="isAngularZone",Ar=xs+"_ID",Eh=0,re=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ze(!1);onMicrotaskEmpty=new ze(!1);onStable=new ze(!1);onError=new ze(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=rl}=t;if(typeof Zone>"u")throw new b(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Ch(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(xs)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new b(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new b(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Ih,xr,xr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Ih={};function As(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function wh(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){sl(()=>{e.callbackScheduled=!1,Ti(e),e.isCheckStableRunning=!0,As(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Ti(e)}function Ch(e){let t=()=>{wh(e)},n=Eh++;e._inner=e._inner.fork({name:"angular",properties:{[xs]:!0,[Ar]:n,[Ar+n]:!0},onInvokeTask:(r,o,i,s,a,u)=>{if(bh(u))return r.invokeTask(i,s,a,u);try{return mu(e),r.invokeTask(i,s,a,u)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),yu(e)}},onInvoke:(r,o,i,s,a,u,c)=>{try{return mu(e),r.invoke(i,s,a,u,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!_h(u)&&t(),yu(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Ti(e),As(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Ti(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function mu(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function yu(e){e._nesting--,As(e)}var Si=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ze;onMicrotaskEmpty=new ze;onStable=new ze;onError=new ze;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function bh(e){return al(e,"__ignore_ng_zone__")}function _h(e){return al(e,"__scheduler_tick__")}function al(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Gt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Mh=new S("",{providedIn:"root",factory:()=>{let e=D(re),t=D(Gt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function vu(e,t){return Zu(e,t)}function Th(e){return Zu(qu,e)}var jM=(vu.required=Th,vu);function Sh(){return Qt($(),_())}function Qt(e,t){return new _t(ue(e,t))}var _t=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Sh}return e})();function Nh(e){return e instanceof _t?e.nativeElement:e}var Du=new Set;function Mt(e){Du.has(e)||(Du.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function xh(e){return typeof e=="function"&&e[X]!==void 0}function BM(e,t){Mt("NgSignals");let n=Ma(e),r=n[X];return t?.equal&&(r.equal=t.equal),n.set=o=>An(r,o),n.update=o=>Ta(r,o),n.asReadonly=Ah.bind(n),n}function Ah(){let e=this[X];if(e.readonlyFn===void 0){let t=()=>this();t[X]=e,e.readonlyFn=t}return e.readonlyFn}function ul(e){return xh(e)&&typeof e.set=="function"}function Oh(){return this._results[Symbol.iterator]()}var Ni=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new Ie}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=fp(t);(this._changesDetected=!dp(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Oh};function cl(e){return(e.flags&128)===128}var ll=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ll||{}),dl=new Map,Rh=0;function Fh(){return Rh++}function kh(e){dl.set(e[Zr],e)}function xi(e){dl.delete(e[Zr])}var Eu="__ngContext__";function Ye(e,t){We(t)?(e[Eu]=t[Zr],kh(t)):e[Eu]=t}function fl(e){return hl(e[fn])}function pl(e){return hl(e[ge])}function hl(e){for(;e!==null&&!Le(e);)e=e[ge];return e}var Ai;function HM(e){Ai=e}function Ph(){if(Ai!==void 0)return Ai;if(typeof document<"u")return document;throw new b(210,!1)}var $M=new S("",{providedIn:"root",factory:()=>Lh}),Lh="ng",Vh=new S(""),jh=new S("",{providedIn:"platform",factory:()=>"unknown"});var UM=new S(""),zM=new S("",{providedIn:"root",factory:()=>Ph().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Bh="h",Hh="b";var gl=!1,$h=new S("",{providedIn:"root",factory:()=>gl});var Os=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Os||{}),oo=new S("");var Ft=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Ft||{}),ml=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=R({token:e,providedIn:"root",factory:()=>new e})}return e})(),Uh=[Ft.EarlyRead,Ft.Write,Ft.MixedReadWrite,Ft.Read],zh=(()=>{class e{ngZone=D(re);scheduler=D(zt);errorHandler=D(Gt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){D(oo,{optional:!0})}execute(){this.executing=!0;for(let n of Uh)for(let r of this.sequences)if(!(r.erroredOrDestroyed||!r.hooks[n]))try{r.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>r.hooks[n](r.pipelinedValue),r.snapshot))}catch(o){r.erroredOrDestroyed=!0,this.errorHandler?.handleError(o)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(8),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(7))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Os.AFTER_NEXT_RENDER,n):n()}static \u0275prov=R({token:e,providedIn:"root",factory:()=>new e})}return e})(),Oi=class{impl;hooks;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i=null){this.impl=t,this.hooks=n,this.once=r,this.snapshot=i,this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function Gh(e,t){!t?.injector&&vc(Gh);let n=t?.injector??D(Qe);return Mt("NgAfterRender"),yl(e,n,t,!1)}function Wh(e,t){!t?.injector&&vc(Wh);let n=t?.injector??D(Qe);return Mt("NgAfterNextRender"),yl(e,n,t,!0)}function qh(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function yl(e,t,n,r){let o=t.get(ml);o.impl??=t.get(zh);let i=t.get(oo,null,{optional:!0}),s=n?.phase??Ft.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Ss):null,u=new Oi(o.impl,qh(e,s),r,a,i?.snapshot(null));return o.impl.register(u),u}var Zh=()=>null;function Rs(e,t,n=!1){return Zh(e,t,n)}function vl(e,t){let n=e.contentQueries;if(n!==null){let r=M(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];bs(i),a.contentQueries(2,t[s],s)}}}finally{M(r)}}}function Ri(e,t,n){bs(0);let r=M(null);try{t(e,n)}finally{M(r)}}function Fs(e,t,n){if(Ds(t)){let r=M(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{M(r)}}}var pn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(pn||{}),sr;function Qh(){if(sr===void 0&&(sr=null,Ue.trustedTypes))try{sr=Ue.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return sr}function io(e){return Qh()?.createHTML(e)||e}var ar;function Yh(){if(ar===void 0&&(ar=null,Ue.trustedTypes))try{ar=Ue.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ar}function Iu(e){return Yh()?.createScriptURL(e)||e}var ke=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Gu})`}},Fi=class extends ke{getTypeName(){return"HTML"}},ki=class extends ke{getTypeName(){return"Style"}},Pi=class extends ke{getTypeName(){return"Script"}},Li=class extends ke{getTypeName(){return"URL"}},Vi=class extends ke{getTypeName(){return"ResourceURL"}};function En(e){return e instanceof ke?e.changingThisBreaksApplicationSecurity:e}function Dl(e,t){let n=Kh(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Gu})`)}return n===t}function Kh(e){return e instanceof ke&&e.getTypeName()||null}function GM(e){return new Fi(e)}function WM(e){return new ki(e)}function qM(e){return new Pi(e)}function ZM(e){return new Li(e)}function QM(e){return new Vi(e)}function Jh(e){let t=new Bi(e);return Xh()?new ji(t):t}var ji=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(io(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Bi=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=io(t),n}};function Xh(){try{return!!new window.DOMParser().parseFromString(io(""),"text/html")}catch{return!1}}var eg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function El(e){return e=String(e),e.match(eg)?e:"unsafe:"+e}function Ve(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function In(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Il=Ve("area,br,col,hr,img,wbr"),wl=Ve("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Cl=Ve("rp,rt"),tg=In(Cl,wl),ng=In(wl,Ve("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),rg=In(Cl,Ve("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),wu=In(Il,ng,rg,tg),bl=Ve("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),og=Ve("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),ig=Ve("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),sg=In(bl,og,ig),ag=Ve("script,style,template"),Hi=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=lg(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=cg(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Cu(t).toLowerCase();if(!wu.hasOwnProperty(n))return this.sanitizedSomething=!0,!ag.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!sg.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;bl[a]&&(u=El(u)),this.buf.push(" ",s,'="',bu(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=Cu(t).toLowerCase();wu.hasOwnProperty(n)&&!Il.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(bu(t))}};function ug(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function cg(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw _l(t);return t}function lg(e){let t=e.firstChild;if(t&&ug(e,t))throw _l(t);return t}function Cu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function _l(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var dg=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,fg=/([^\#-~ |!])/g;function bu(e){return e.replace(/&/g,"&amp;").replace(dg,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(fg,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ur;function YM(e,t){let n=null;try{ur=ur||Jh(e);let r=t?String(t):"";n=ur.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ur.getInertBodyElement(r)}while(r!==i);let a=new Hi().sanitizeChildren(_u(n)||n);return io(a)}finally{if(n){let r=_u(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function _u(e){return"content"in e&&pg(e)?e.content:null}function pg(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ks=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ks||{});function hg(e){let t=Ml();return t?t.sanitize(ks.URL,e)||"":Dl(e,"URL")?En(e):El(Gr(e))}function gg(e){let t=Ml();if(t)return Iu(t.sanitize(ks.RESOURCE_URL,e)||"");if(Dl(e,"ResourceURL"))return Iu(En(e));throw new b(904,!1)}function mg(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?gg:hg}function KM(e,t,n){return mg(t,n)(e)}function Ml(){let e=_();return e&&e[Re].sanitizer}var yg=/^>|^->|<!--|-->|--!>|<!-$/g,vg=/(<|>)/g,Dg="\u200B$1\u200B";function Eg(e){return e.replace(yg,t=>t.replace(vg,Dg))}function JM(e){return e.ownerDocument}function Tl(e){return e instanceof Function?e():e}var Ke=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ke||{});function Ig(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Sl="ng-template";function wg(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Ig(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Ps(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Ps(e){return e.type===4&&e.value!==Sl}function Cg(e,t,n){let r=e.type===4&&!n?Sl:e.value;return t===r}function bg(e,t,n){let r=4,o=e.attrs,i=o!==null?Tg(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!pe(r)&&!pe(u))return!1;if(s&&pe(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!Cg(e,u,n)||u===""&&t.length===1){if(pe(r))return!1;s=!0}}else if(r&8){if(o===null||!wg(e,o,u,n)){if(pe(r))return!1;s=!0}}else{let c=t[++a],l=_g(u,o,Ps(e),n);if(l===-1){if(pe(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(pe(r))return!1;s=!0}}}}return pe(r)||s}function pe(e){return(e&1)===0}function _g(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Sg(t,e)}function Mg(e,t,n=!1){for(let r=0;r<t.length;r++)if(bg(e,t[r],n))return!0;return!1}function Tg(e){for(let t=0;t<e.length;t++){let n=e[t];if(Wc(n))return t}return e.length}function Sg(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Mu(e,t){return e?":not("+t.trim()+")":t}function Ng(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!pe(s)&&(t+=Mu(i,o),o=""),r=s,i=i||!pe(r);n++}return o!==""&&(t+=Mu(i,o)),t}function xg(e){return e.map(Ng).join(",")}function Ag(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!pe(o))break;o=i}r++}return{attrs:t,classes:n}}var Se={};function XM(e=1){Nl(L(),_(),bt()+e,!1)}function Nl(e,t,n,r){if(!r)if((t[g]&3)===3){let i=e.preOrderCheckHooks;i!==null&&fr(t,i,n)}else{let i=e.preOrderHooks;i!==null&&pr(t,i,0,n)}mt(n)}function Z(e,t=C.Default){let n=_();if(n===null)return U(e,t);let r=$();return Xc(r,n,W(e),t)}function eT(){let e="invalid";throw new Error(e)}function xl(e,t,n,r,o,i){let s=M(null);try{let a=null;o&Ke.SignalBased&&(a=t[r][X]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ke.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Ic(t,a,r,i)}finally{M(s)}}function Og(e,t){return e.createText(t)}function Rg(e,t,n){e.setValue(t,n)}function Fg(e,t){return e.createComment(Eg(t))}function Al(e,t,n){return e.createElement(t,n)}function Or(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Ol(e,t,n){e.appendChild(t,n)}function Tu(e,t,n,r,o){r!==null?Or(e,t,n,r,o):Ol(e,t,n)}function kg(e,t,n){e.removeChild(null,t,n)}function Pg(e,t,n){e.setAttribute(t,"style",n)}function Lg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Rl(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&sh(e,t,r),o!==null&&Lg(e,t,o),i!==null&&Pg(e,t,i)}function so(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[Pe]=o,d[g]=r|4|128|8|64|1024,(c!==null||e&&e[g]&2048)&&(d[g]|=2048),Nc(d),d[z]=d[Zt]=e,d[me]=n,d[Re]=s||e&&e[Re],d[k]=a||e&&e[k],d[Bt]=u||e&&e[Bt]||null,d[ye]=i,d[Zr]=Fh(),d[wr]=l,d[Dc]=c,d[_e]=t.type==2?e[_e]:d,d}function Fl(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function kl(e,t,n,r,o){let i=bt(),s=r&2;try{mt(-1),s&&t.length>te&&Nl(e,t,te,!1),ae(s?2:0,o),n(r,o)}finally{mt(i),ae(s?3:1,o)}}function Ls(e,t,n){Rc()&&(qg(e,t,n,ue(n,t)),(n.flags&64)===64&&Vl(e,t,n))}function Vs(e,t,n=ue){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Pl(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=js(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function js(e,t,n,r,o,i,s,a,u,c,l){let d=te+r,p=d+o,f=Vg(d,p),h=typeof c=="function"?c():c;return f[E]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:h,incompleteFirstPass:!1,ssrId:l}}function Vg(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Se);return n}function jg(e,t,n,r){let i=r.get($h,gl)||n===pn.ShadowDom,s=e.selectRootElement(t,i);return Bg(s),s}function Bg(e){Hg(e)}var Hg=()=>null;function Su(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,u=Ke.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?Nu(r,n,c,a,u):Nu(r,n,c,a)}return r}function Nu(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function $g(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;u=Su(0,d.inputs,l,u,f),c=Su(1,d.outputs,l,c,h);let w=u!==null&&s!==null&&!Ps(t)?om(u,l,s):null;a.push(w)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function Ug(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Bs(e,t,n,r,o,i,s,a){let u=ue(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?($s(e,n,l,r,o),Qr(t)&&zg(n,t.index)):t.type&3?(r=Ug(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function zg(e,t){let n=Xe(t,e);n[g]&16||(n[g]|=64)}function Hs(e,t,n,r){if(Rc()){let o=r===null?null:{"":-1},i=Qg(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&Ll(e,t,n,s,o,a),o&&Yg(n,r,o)}n.mergedAttrs=Ut(n.mergedAttrs,n.attrs)}function Ll(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Ci(Nr(n,t),e,r[c].type);Jg(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=Fl(e,t,r.length,null);for(let c=0;c<r.length;c++){let l=r[c];n.mergedAttrs=Ut(n.mergedAttrs,l.hostAttrs),Xg(e,n,t,u,l),Kg(u,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}$g(e,n,i)}function Gg(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Wg(s)!=a&&s.push(a),s.push(n,r,i)}}function Wg(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function qg(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;Qr(n)&&em(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Nr(n,t),Ye(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=vt(t,e,a,n);if(Ye(c,t),s!==null&&rm(t,a-o,c,u,n,s),Ze(u)){let l=Xe(n.index,t);l[me]=vt(t,e,a,n)}}}function Vl(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Yp();try{mt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];Ei(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&Zg(u,c)}}finally{mt(-1),Ei(s)}}function Zg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Qg(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(Mg(t,s.selectors,!1))if(r||(r=[]),Ze(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let u=a.length;$i(e,t,u)}else r.unshift(s),$i(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function $i(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Yg(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new b(-301,!1);r.push(t[o],i)}}}function Kg(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ze(t)&&(n[""]=e)}}function Jg(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Xg(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=ft(o.type,!0)),s=new yt(i,Ze(o),Z);e.blueprint[r]=s,n[r]=s,Gg(e,t,r,Fl(e,n,o.hostVars,Se),o)}function jl(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function em(e,t,n){let r=ue(t,e),o=Pl(n),i=e[Re].rendererFactory,s=ao(e,so(e,o,null,jl(n),r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=s}function tm(e,t,n,r,o,i){let s=ue(e,t);nm(t[k],s,i,e.value,n,r,o)}function nm(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Gr(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function rm(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];xl(r,n,u,c,l,d)}}function om(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function Bl(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function ao(e,t){return e[fn]?e[au][ge]=t:e[fn]=t,e[au]=t,t}function Hl(e,t){let n=e[Bt],r=n?n.get(Gt,null):null;r&&r.handleError(t)}function $s(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];xl(l,c,r,a,u,o)}}function im(e,t){let n=Xe(t,e),r=n[E];sm(r,n);let o=n[Pe];o!==null&&n[wr]===null&&(n[wr]=Rs(o,n[Bt])),Us(r,n,n[me])}function sm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Us(e,t,n){_s(t);try{let r=e.viewQuery;r!==null&&Ri(1,r,n);let o=e.template;o!==null&&kl(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Fe]?.finishViewCreation(e),e.staticContentQueries&&vl(e,t),e.staticViewQueries&&Ri(2,e.viewQuery,n);let i=e.components;i!==null&&am(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[g]&=-5,Ms()}}function am(e,t){for(let n=0;n<t.length;n++)im(e,t[n])}var Rr=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Rr||{}),um;function zs(e,t){return um(e,t)}function kt(e,t,n,r,o){if(r!=null){let i,s=!1;Le(r)?i=r:We(r)&&(s=!0,r=r[Pe]);let a=Me(r);e===0&&n!==null?o==null?Ol(t,n,a):Or(t,n,a,o||null,!0):e===1&&n!==null?Or(t,n,a,o||null,!0):e===2?kg(t,a,s):e===3&&t.destroyNode(a),i!=null&&Im(t,e,i,n,o)}}function cm(e,t){$l(e,t),t[Pe]=null,t[ye]=null}function lm(e,t,n,r,o,i){r[Pe]=o,r[ye]=t,co(e,r,n,1,o,i)}function $l(e,t){t[Re].changeDetectionScheduler?.notify(10),co(e,t,t[k],2,null,null)}function dm(e){let t=e[fn];if(!t)return ni(e[E],e);for(;t;){let n=null;if(We(t))n=t[fn];else{let r=t[ne];r&&(n=r)}if(!n){for(;t&&!t[ge]&&t!==e;)We(t)&&ni(t[E],t),t=t[z];t===null&&(t=e),We(t)&&ni(t[E],t),n=t&&t[ge]}t=n}}function fm(e,t,n,r){let o=ne+r,i=n.length;r>0&&(n[o-1][ge]=t),r<i-ne?(t[ge]=n[o],ic(n,ne+r,t)):(n.push(t),t[ge]=null),t[z]=n;let s=t[pt];s!==null&&n!==s&&Ul(s,t);let a=t[Fe];a!==null&&a.insertView(e),vi(t),t[g]|=128}function Ul(e,t){let n=e[Ht],r=t[z];if(We(r))e[g]|=2;else{let o=r[z][_e];t[_e]!==o&&(e[g]|=2)}n===null?e[Ht]=[t]:n.push(t)}function Gs(e,t){let n=e[Ht],r=n.indexOf(t);n.splice(r,1)}function Ui(e,t){if(e.length<=ne)return;let n=ne+t,r=e[n];if(r){let o=r[pt];o!==null&&o!==e&&Gs(o,r),t>0&&(e[n-1][ge]=r[ge]);let i=Dr(e,ne+t);cm(r[E],r);let s=i[Fe];s!==null&&s.detachView(i[E]),r[z]=null,r[ge]=null,r[g]&=-129}return r}function zl(e,t){if(vn(t))return;let n=t[k];n.destroyNode&&co(e,t,n,3,null,null),dm(t)}function ni(e,t){if(vn(t))return;let n=M(null);try{t[g]&=-129,t[g]|=256,t[ee]&&Fo(t[ee]),hm(e,t),pm(e,t),t[E].type===1&&t[k].destroy();let r=t[pt];if(r!==null&&Le(t[z])){r!==t[z]&&Gs(r,t);let o=t[Fe];o!==null&&o.detachView(e)}xi(t)}finally{M(n)}}function pm(e,t){let n=e.cleanup,r=t[Cr];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Cr]=null);let o=t[Ge];if(o!==null){t[Ge]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[br];if(i!==null){t[br]=null;for(let s of i)s.destroy()}}function hm(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof yt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];ae(4,a,u);try{u.call(a)}finally{ae(5,a,u)}}else{ae(4,o,i);try{i.call(o)}finally{ae(5,o,i)}}}}}function gm(e,t,n){return mm(e,t.parent,n)}function mm(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Pe];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===pn.None||i===pn.Emulated)return null}return ue(r,n)}}function ym(e,t,n){return Dm(e,t,n)}function vm(e,t,n){return e.type&40?ue(e,n):null}var Dm=vm,xu;function uo(e,t,n,r){let o=gm(e,r,t),i=t[k],s=r.parent||t[ye],a=ym(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)Tu(i,o,n[u],a,!1);else Tu(i,o,n,a,!1);xu!==void 0&&xu(i,r,t,n,o)}function un(e,t){if(t!==null){let n=t.type;if(n&3)return ue(t,e);if(n&4)return zi(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return un(e,r);{let o=e[t.index];return Le(o)?zi(-1,o):Me(o)}}else{if(n&128)return un(e,t.next);if(n&32)return zs(t,e)()||Me(e[t.index]);{let r=Gl(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=gt(e[_e]);return un(o,r)}else return un(e,t.next)}}}return null}function Gl(e,t){if(t!==null){let r=e[_e][ye],o=t.projection;return r.projection[o]}return null}function zi(e,t){let n=ne+e+1;if(n<t.length){let r=t[n],o=r[E].firstChild;if(o!==null)return un(r,o)}return t[ht]}function Ws(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],u=n.type;if(s&&t===0&&(a&&Ye(Me(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)Ws(e,t,n.child,r,o,i,!1),kt(t,e,o,a,i);else if(u&32){let c=zs(n,r),l;for(;l=c();)kt(t,e,o,l,i);kt(t,e,o,a,i)}else u&16?Em(e,t,r,n,o,i):kt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function co(e,t,n,r,o,i){Ws(n,r,e.firstChild,t,o,i,!1)}function Em(e,t,n,r,o,i){let s=n[_e],u=s[ye].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];kt(t,e,o,l,i)}else{let c=u,l=s[z];cl(r)&&(c.flags|=128),Ws(e,t,c,l,o,i,!0)}}function Im(e,t,n,r,o){let i=n[ht],s=Me(n);i!==s&&kt(t,e,r,i,o);for(let a=ne;a<n.length;a++){let u=n[a];co(u[E],u,e,t,r,i)}}function wm(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Rr.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Rr.Important),e.setStyle(n,r,o,i))}}function Cm(e,t,n,r){let o=M(null);try{let i=t.tView,a=e[g]&4096?4096:16,u=so(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];u[pt]=c;let l=e[Fe];return l!==null&&(u[Fe]=l.createEmbeddedView(i)),Us(i,u,n),u}finally{M(o)}}function Au(e,t){return!t||t.firstChild===null||cl(e)}function bm(e,t,n,r=!0){let o=t[E];if(fm(o,t,e,n),r){let s=zi(n,e),a=t[k],u=a.parentNode(e[ht]);u!==null&&lm(o,e[ye],a,t,u,s)}let i=t[wr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Fr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Me(i)),Le(i)&&_m(i,r);let s=n.type;if(s&8)Fr(e,t,n.child,r);else if(s&32){let a=zs(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=Gl(t,n);if(Array.isArray(a))r.push(...a);else{let u=gt(t[_e]);Fr(u[E],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function _m(e,t){for(let n=ne;n<e.length;n++){let r=e[n],o=r[E].firstChild;o!==null&&Fr(r[E],r,o,t)}e[ht]!==e[Pe]&&t.push(e[ht])}var Wl=[];function Mm(e){return e[ee]??Tm(e)}function Tm(e){let t=Wl.pop()??Object.create(Nm);return t.lView=e,t}function Sm(e){e.lView[ee]!==e&&(e.lView=null,Wl.push(e))}var Nm=Ee(De({},Jt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Jr(e.lView)},consumerOnSignalRead(){this.lView[ee]=this}});function xm(e){let t=e[ee]??Object.create(Am);return t.lView=e,t}var Am=Ee(De({},Jt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=gt(e.lView);for(;t&&!ql(t[E]);)t=gt(t);t&&Is(t)},consumerOnSignalRead(){this.lView[ee]=this}});function ql(e){return e.type!==2}function Zl(e){if(e[br]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[br])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[g]&8192)}}var Om=100;function Ql(e,t=!0,n=0){let o=e[Re].rendererFactory,i=!1;i||o.begin?.();try{Rm(e,n)}catch(s){throw t&&Hl(e,s),s}finally{i||o.end?.()}}function Rm(e,t){let n=Pc();try{cu(!0),Gi(e,t);let r=0;for(;Kr(e);){if(r===Om)throw new b(103,!1);r++,Gi(e,1)}}finally{cu(n)}}function Fm(e,t,n,r){if(vn(t))return;let o=t[g],i=!1,s=!1;_s(t);let a=!0,u=null,c=null;i||(ql(e)?(c=Mm(t),u=Sn(c)):ma()===null?(a=!1,c=xm(t),u=Sn(c)):t[ee]&&(Fo(t[ee]),t[ee]=null));try{Nc(t),qp(e.bindingStartIndex),n!==null&&kl(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&fr(t,f,null)}else{let f=e.preOrderHooks;f!==null&&pr(t,f,0,null),Xo(t,0)}if(s||km(t),Zl(t),Yl(t,0),e.contentQueries!==null&&vl(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&fr(t,f)}else{let f=e.contentHooks;f!==null&&pr(t,f,1),Xo(t,1)}Lm(e,t);let d=e.components;d!==null&&Jl(t,d,0);let p=e.viewQuery;if(p!==null&&Ri(2,p,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&fr(t,f)}else{let f=e.viewHooks;f!==null&&pr(t,f,2),Xo(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Jo]){for(let f of t[Jo])f();t[Jo]=null}i||(t[g]&=-73)}catch(l){throw i||Jr(t),l}finally{c!==null&&(Oo(c,u),a&&Sm(c)),Ms()}}function Yl(e,t){for(let n=fl(e);n!==null;n=pl(n))for(let r=ne;r<n.length;r++){let o=n[r];Kl(o,t)}}function km(e){for(let t=fl(e);t!==null;t=pl(t)){if(!(t[g]&2))continue;let n=t[Ht];for(let r=0;r<n.length;r++){let o=n[r];Is(o)}}}function Pm(e,t,n){let r=Xe(t,e);Kl(r,n)}function Kl(e,t){Es(e)&&Gi(e,t)}function Gi(e,t){let r=e[E],o=e[g],i=e[ee],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Ro(i)),s||=!1,i&&(i.dirty=!1),e[g]&=-9217,s)Fm(r,e,r.template,e[me]);else if(o&8192){Zl(e),Yl(e,1);let a=r.components;a!==null&&Jl(e,a,1)}}function Jl(e,t,n){for(let r=0;r<t.length;r++)Pm(e,t[r],n)}function Lm(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)mt(~o);else{let i=o,s=n[++r],a=n[++r];Qp(s,i);let u=t[i];ae(24,u),a(2,u),ae(25,u)}}}finally{mt(-1)}}function qs(e,t){let n=Pc()?64:1088;for(e[Re].changeDetectionScheduler?.notify(t);e;){e[g]|=n;let r=gt(e);if(mi(e)&&!r)return e;e=r}return null}var Dt=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[E];return Fr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[me]}get dirty(){return!!(this._lView[g]&9280)||!!this._lView[ee]?.dirty}set context(t){this._lView[me]=t}get destroyed(){return vn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[z];if(Le(t)){let n=t[_r],r=n?n.indexOf(this):-1;r>-1&&(Ui(t,r),Dr(n,r))}this._attachedToViewContainer=!1}zl(this._lView[E],this._lView)}onDestroy(t){xc(this._lView,t)}markForCheck(){qs(this._cdRefInjectingView||this._lView,4)}markForRefresh(){Is(this._cdRefInjectingView||this._lView)}detach(){this._lView[g]&=-129}reattach(){vi(this._lView),this._lView[g]|=128}detectChanges(){this._lView[g]|=1024,Ql(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new b(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=mi(this._lView),n=this._lView[pt];n!==null&&!t&&Gs(n,this._lView),$l(this._lView[E],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new b(902,!1);this._appRef=t;let n=mi(this._lView),r=this._lView[pt];r!==null&&!n&&Ul(r,this._lView),vi(this._lView)}},Et=(()=>{class e{static __NG_ELEMENT_ID__=Bm}return e})(),Vm=Et,jm=class extends Vm{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Cm(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Dt(o)}};function Bm(){return lo($(),_())}function lo(e,t){return e.type&4?new jm(t,e,Qt(e,t)):null}function wn(e,t,n,r,o){let i=e.data[t];if(i===null)i=Hm(e,t,n,r,o),Zp()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Gp();i.injectorIndex=s===null?-1:s.injectorIndex}return Ct(i,!0),i}function Hm(e,t,n,r,o){let i=Fc(),s=ws(),a=s?i:i&&i.parent,u=e.data[t]=Um(e,a,n,t,r,o);return $m(e,u,i,s),u}function $m(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Um(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return $p()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var nT=new RegExp(`^(\\d+)*(${Hh}|${Bh})*(.*)`);var zm=()=>null;function Ou(e,t){return zm(e,t)}var Wi=class{},kr=class{},qi=class{resolveComponentFactory(t){throw Error(`No component factory found for ${q(t)}.`)}},Wt=class{static NULL=new qi},Pr=class{},Zs=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Gm()}return e})();function Gm(){let e=_(),t=$(),n=Xe(t.index,e);return(We(n)?n:e)[k]}var Wm=(()=>{class e{static \u0275prov=R({token:e,providedIn:"root",factory:()=>null})}return e})();function Lr(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=ci(o,a);else if(i==2){let u=a,c=t[++s];r=ci(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var Vr=class extends Wt{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Oe(t);return new It(n,this.ngModule)}};function Ru(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:Ke.None;t?n.push({propName:s,templateName:r,isSignal:(a&Ke.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function qm(e){let t=e.toLowerCase();return t==="svg"?_c:t==="math"?Rp:null}function Zm(e){let{attrs:t,classes:n}=Ag(e),r=t;return n.length&&r.push(1,...n),r}var It=class extends kr{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;get inputs(){let t=this.componentDef,n=t.inputTransforms,r=Ru(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Ru(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=xg(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=M(null);try{o=o||this.ngModule;let s=o instanceof qe?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Ii(t,s):t,u=a.get(Pr,null);if(u===null)throw new b(407,!1);let c=a.get(Wm,null),l=a.get(zt,null),d={rendererFactory:u,sanitizer:c,changeDetectionScheduler:l},p=u.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",h=r?jg(p,r,this.componentDef.encapsulation,a):Al(p,f,qm(f)),w=512;this.componentDef.signals?w|=4096:this.componentDef.onPush||(w|=16);let P=null;h!==null&&(P=Rs(h,a,!0));let A=js(0,null,null,1,0,null,null,null,null,null,null),ie=so(null,A,null,w,null,null,d,p,a,null,P);ie[te]=h,_s(ie);let Ne,de=null;try{let se=this.componentDef,tt,_o=null;se.findHostDirectiveDefs?(tt=[],_o=new Map,se.findHostDirectiveDefs(se,tt,_o),tt.push(se)):tt=[se];let da=r?["ng-version","19.1.3"]:Zm(this.componentDef.selectors[0]),xe=wn(A,te,2,"#host",da);for(let Hd of tt)xe.mergedAttrs=Ut(xe.mergedAttrs,Hd.hostAttrs);xe.mergedAttrs=Ut(xe.mergedAttrs,da),Lr(xe,xe.mergedAttrs,!0),h&&Rl(p,h,xe),de=Qm(xe,h,se,tt,ie,d),n!==void 0&&Km(xe,this.ngContentSelectors,n),Ne=Ym(de,se,tt,_o,ie,[Jm]),Us(A,ie,null)}catch(se){throw de!==null&&xi(de),xi(ie),se}finally{Ms()}let Cn=Tc(A,te);return new Zi(this.componentType,Ne,Qt(Cn,ie),ie,Cn)}finally{M(i)}}},Zi=class extends Wi{location;_rootLView;_tNode;instance;hostView;changeDetectorRef;componentType;previousInputValues=null;constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.instance=n,this.hostView=this.changeDetectorRef=new Dt(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;$s(i[E],i,o,t,n),this.previousInputValues.set(t,n);let s=Xe(this._tNode.index,i);qs(s,1)}}get injector(){return new dt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Qm(e,t,n,r,o,i){let s=o[E],a=null;t!==null&&(a=Rs(t,o[Bt]));let u=i.rendererFactory.createRenderer(t,n),c=so(o,Pl(n),null,jl(n),o[e.index],e,i,u,null,null,a);return s.firstCreatePass&&$i(s,e,r.length-1),ao(o,c),o[e.index]=c}function Ym(e,t,n,r,o,i){let s=$(),a=o[E],u=ue(s,o);Ll(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,p=vt(o,a,d,s);Ye(p,o)}Vl(a,o,s),u&&Ye(u,o);let c=vt(o,a,s.directiveStart+s.componentOffset,s);if(e[me]=o[me]=c,i!==null)for(let l of i)l(c,t);return Fs(a,s,o),c}function Km(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}function Jm(){let e=$();no(_()[E],e)}var Yt=(()=>{class e{static __NG_ELEMENT_ID__=Xm}return e})();function Xm(){let e=$();return ed(e,_())}var ey=Yt,Xl=class extends ey{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Qt(this._hostTNode,this._hostLView)}get injector(){return new dt(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ts(this._hostTNode,this._hostLView);if(qc(t)){let n=Tr(t,this._hostLView),r=Mr(t),o=n[E].data[r+8];return new dt(o,n)}else return new dt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Fu(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ne}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Ou(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Au(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Sp(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let u=s?t:new It(Oe(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let w=(s?c:this.parentInjector).get(qe,null);w&&(i=w)}let l=Oe(u.componentType??{}),d=Ou(this._lContainer,l?.id??null),p=d?.firstChild??null,f=u.create(c,o,p,i);return this.insertImpl(f.hostView,a,Au(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(kp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[z],c=new Xl(u,u[ye],u[z]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return bm(s,o,i,r),t.attachToViewContainerRef(),ic(ri(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Fu(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Ui(this._lContainer,n);r&&(Dr(ri(this._lContainer),n),zl(r[E],r))}detach(t){let n=this._adjustIndex(t,-1),r=Ui(this._lContainer,n);return r&&Dr(ri(this._lContainer),n)!=null?new Dt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Fu(e){return e[_r]}function ri(e){return e[_r]||(e[_r]=[])}function ed(e,t){let n,r=t[e.index];return Le(r)?n=r:(n=Bl(r,t,null,e),t[e.index]=n,ao(t,n)),ny(n,t,e,r),new Xl(n,e,t)}function ty(e,t){let n=e[k],r=n.createComment(""),o=ue(t,e),i=n.parentNode(o);return Or(n,i,r,n.nextSibling(o),!1),r}var ny=iy,ry=()=>!1;function oy(e,t,n){return ry(e,t,n)}function iy(e,t,n,r){if(e[ht])return;let o;n.type&8?o=Me(r):o=ty(t,n),e[ht]=o}var Qi=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Yi=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Qs(t,n).matches!==null&&this.queries[n].setDirty()}},jr=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=py(t):this.predicate=t}},Ki=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Ji=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,sy(n,i)),this.matchTNodeWithReadOption(t,n,hr(n,t,i,!1,!1))}else r===Et?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,hr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===_t||o===Yt||o===Et&&n.type&4)this.addMatch(n.index,-2);else{let i=hr(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function sy(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function ay(e,t){return e.type&11?Qt(e,t):e.type&4?lo(e,t):null}function uy(e,t,n,r){return n===-1?ay(t,e):n===-2?cy(e,t,r):vt(e,e[E],n,t)}function cy(e,t,n){if(n===_t)return Qt(t,e);if(n===Et)return lo(t,e);if(n===Yt)return ed(t,e)}function td(e,t,n,r){let o=t[Fe].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push(uy(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function Xi(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=td(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=ne;d<l.length;d++){let p=l[d];p[pt]===p[z]&&Xi(p[E],p,c,r)}if(l[Ht]!==null){let d=l[Ht];for(let p=0;p<d.length;p++){let f=d[p];Xi(f[E],f,c,r)}}}}}return r}function ly(e,t){return e[Fe].queries[t].queryList}function nd(e,t,n){let r=new Ni((n&4)===4);return Vp(e,t,r,r.destroy),(t[Fe]??=new Yi).queries.push(new Qi(r))-1}function dy(e,t,n){let r=L();return r.firstCreatePass&&(rd(r,new jr(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),nd(r,_(),t)}function fy(e,t,n,r){let o=L();if(o.firstCreatePass){let i=$();rd(o,new jr(t,n,r),i.index),hy(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return nd(o,_(),n)}function py(e){return e.split(",").map(t=>t.trim())}function rd(e,t,n){e.queries===null&&(e.queries=new Ki),e.queries.track(new Ji(t,n))}function hy(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Qs(e,t){return e.queries.getByIndex(t)}function gy(e,t){let n=e[E],r=Qs(n,t);return r.crossesNgTemplate?Xi(n,e,t,[]):td(n,e,r,t)}var Je=class{},es=class{};var ts=class extends Je{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Vr(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=uc(t);this._bootstrapComponents=Tl(i.bootstrap),this._r3Injector=nl(t,n,[{provide:Je,useValue:this},{provide:Wt,useValue:this.componentFactoryResolver},...r],q(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ns=class extends es{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new ts(this.moduleType,t,[])}};var Br=class extends Je{injector;componentFactoryResolver=new Vr(this);instance=null;constructor(t){super();let n=new dn([...t.providers,{provide:Je,useValue:this},{provide:Wt,useValue:this.componentFactoryResolver}],t.parent||qr(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function my(e,t,n=null){return new Br({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var yy=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=dc(!1,n.type),o=r.length>0?my([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=R({token:e,providedIn:"environment",factory:()=>new e(U(qe))})}return e})();function oT(e){return mn(()=>{let t=sd(e),n=Ee(De({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ll.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(yy).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||pn.Emulated,styles:e.styles||J,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Mt("NgStandalone"),ad(n);let r=e.dependencies;return n.directiveDefs=Pu(r,!1),n.pipeDefs=Pu(r,!0),n.id=Ey(n),n})}function vy(e){return Oe(e)||cc(e)}function Dy(e){return e!==null}function od(e){return mn(()=>({type:e.type,bootstrap:e.bootstrap||J,declarations:e.declarations||J,imports:e.imports||J,exports:e.exports||J,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ku(e,t){if(e==null)return Vt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ke.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ke.None?[r,a]:r,t[i]=s):n[i]=r}return n}function fo(e){return mn(()=>{let t=sd(e);return ad(t),t})}function id(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function sd(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Vt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||J,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ku(e.inputs,t),outputs:ku(e.outputs),debugInfo:null}}function ad(e){e.features?.forEach(t=>t(e))}function Pu(e,t){if(!e)return null;let n=t?lc:vy;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Dy)}function Ey(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Iy(e){return Object.getPrototypeOf(e.prototype).constructor}function wy(e){let t=Iy(e.type),n=!0,r=[e];for(;t;){let o;if(Ze(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new b(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=cr(e.inputs),s.inputTransforms=cr(e.inputTransforms),s.declaredInputs=cr(e.declaredInputs),s.outputs=cr(e.outputs);let a=o.hostBindings;a&&Ty(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&_y(e,u),c&&My(e,c),Cy(e,o),zf(e.outputs,o.outputs),Ze(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===wy&&(n=!1)}}t=Object.getPrototypeOf(t)}by(r)}function Cy(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function by(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Ut(o.hostAttrs,n=Ut(n,o.hostAttrs))}}function cr(e){return e===Vt?{}:e===J?[]:e}function _y(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function My(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Ty(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Sy(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}function ud(e){return xy(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Ny(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function xy(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Ys(e,t,n){return e[t]=n}function Ay(e,t){return e[t]}function Te(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Oy(e,t,n,r){let o=Te(e,t,n);return Te(e,t+1,r)||o}function Ry(e){return(e.flags&32)===32}function Fy(e,t,n,r,o,i,s,a,u){let c=t.consts,l=wn(t,e,4,s||null,a||null);Hs(t,n,l,$t(c,u)),no(t,l);let d=l.tView=js(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function ky(e,t,n,r,o,i,s,a,u,c){let l=n+te,d=t.firstCreatePass?Fy(l,t,e,r,o,i,s,a,u):t.data[l];Ct(d,!1);let p=Ly(t,e,d,n);eo()&&uo(t,e,p,d),Ye(p,e);let f=Bl(p,e,p,d);return e[l]=f,ao(e,f),oy(f,d,e),Yr(d)&&Ls(t,e,d),u!=null&&Vs(e,d,c),d}function Py(e,t,n,r,o,i,s,a){let u=_(),c=L(),l=$t(c.consts,i);return ky(u,c,e,t,n,r,o,l,s,a),Py}var Ly=Vy;function Vy(e,t,n,r){return to(!0),t[k].createComment("")}var iT=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var jy=new S("");function Ks(e){return!!e&&typeof e.then=="function"}function cd(e){return!!e&&typeof e.subscribe=="function"}var By=new S("");var ld=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=D(By,{optional:!0})??[];injector=D(Qe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=mc(this.injector,o);if(Ks(i))n.push(i);else if(cd(i)){let s=new Promise((a,u)=>{i.subscribe({complete:a,error:u})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Hy=(()=>{class e{static \u0275prov=R({token:e,providedIn:"root",factory:()=>new rs})}return e})(),rs=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}},$y=new S("");function Uy(){_a(()=>{throw new b(600,!1)})}function zy(e){return e.isBoundToModule}var Gy=10;function Wy(e,t,n){try{let r=n();return Ks(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var hn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=D(Mh);afterRenderManager=D(ml);zonelessEnabled=D(Ns);rootEffectScheduler=D(Hy);dirtyFlags=0;deferredDirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Ie;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=D(ro).hasPendingTasks.pipe(at(n=>!n));constructor(){D(oo,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=D(qe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){let o=n instanceof kr;if(!this._injector.get(ld).done){let p=!o&&gp(n),f=!1;throw new b(405,f)}let s;o?s=n:s=this._injector.get(Wt).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=zy(s)?void 0:this._injector.get(Je),u=r||s.selector,c=s.create(Qe.NULL,[],u,a),l=c.location.nativeElement,d=c.injector.get(jy,null);return d?.registerApplication(l),c.onDestroy(()=>{this.detachView(c.hostView),gr(this.components,c),d?.unregisterApplication(l)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick=()=>{if(this.tracingSnapshot!==null){let r=this.tracingSnapshot;this.tracingSnapshot=null,r.run(Os.CHANGE_DETECTION,this._tick),r.dispose();return}if(this._runningTick)throw new b(101,!1);let n=M(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,M(n),this.afterTick.next()}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Pr,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let n=0;for(;this.dirtyFlags!==0&&n++<Gy;)this.synchronizeOnce()}synchronizeOnce(){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)qy(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Kr(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;gr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get($y,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>gr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new b(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function gr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function qy(e,t,n,r){if(!n&&!Kr(e))return;Ql(e,t,n&&!r?0:1)}function Zy(e,t,n,r){let o=_(),i=Dn();if(Te(o,i,t)){let s=L(),a=Xr();tm(a,o,e,t,n,r)}return Zy}function Qy(e,t,n,r){return Te(e,Dn(),n)?t+Gr(n)+r:Se}function lr(e,t){return e<<17|t<<2}function wt(e){return e>>17&32767}function Yy(e){return(e&2)==2}function Ky(e,t){return e&131071|t<<17}function os(e){return e|2}function qt(e){return(e&131068)>>2}function oi(e,t){return e&-131069|t<<2}function Jy(e){return(e&1)===1}function is(e){return e|1}function Xy(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=wt(s),u=qt(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||yn(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let p=wt(e[a+1]);e[r+1]=lr(p,a),p!==0&&(e[p+1]=oi(e[p+1],r)),e[a+1]=Ky(e[a+1],r)}else e[r+1]=lr(a,0),a!==0&&(e[a+1]=oi(e[a+1],r)),a=r;else e[r+1]=lr(u,0),a===0?a=r:e[u+1]=oi(e[u+1],r),u=r;c&&(e[r+1]=os(e[r+1])),Lu(e,l,r,!0),Lu(e,l,r,!1),ev(t,l,e,r,i),s=lr(a,u),i?t.classBindings=s:t.styleBindings=s}function ev(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&yn(i,t)>=0&&(n[r+1]=is(n[r+1]))}function Lu(e,t,n,r){let o=e[n+1],i=t===null,s=r?wt(o):qt(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];tv(u,t)&&(a=!0,e[s+1]=r?is(c):os(c)),s=r?wt(c):qt(c)}a&&(e[n+1]=r?os(o):is(o))}function tv(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?yn(e,t)>=0:!1}var he={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function nv(e){return e.substring(he.key,he.keyEnd)}function rv(e){return ov(e),dd(e,fd(e,0,he.textEnd))}function dd(e,t){let n=he.textEnd;return n===t?-1:(t=he.keyEnd=iv(e,he.key=t,n),fd(e,t,n))}function ov(e){he.key=0,he.keyEnd=0,he.value=0,he.valueEnd=0,he.textEnd=e.length}function fd(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function iv(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function sv(e,t,n){let r=_(),o=Dn();if(Te(r,o,t)){let i=L(),s=Xr();Bs(i,s,r,e,t,r[k],n,!1)}return sv}function ss(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";$s(e,n,i[s],s,r)}function av(e,t){return cv(e,t,null,!0),av}function sT(e){lv(mv,uv,e,!0)}function uv(e,t){for(let n=rv(t);n>=0;n=dd(t,n))ys(e,nv(t),!0)}function cv(e,t,n,r){let o=_(),i=L(),s=Lc(2);if(i.firstUpdatePass&&hd(i,e,s,r),t!==Se&&Te(o,s,t)){let a=i.data[bt()];gd(i,a,o,o[k],e,o[s+1]=vv(t,n),r,s)}}function lv(e,t,n,r){let o=L(),i=Lc(2);o.firstUpdatePass&&hd(o,null,i,r);let s=_();if(n!==Se&&Te(s,i,n)){let a=o.data[bt()];if(md(a,r)&&!pd(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=ci(u,n||"")),ss(o,a,s,n,r)}else yv(o,a,s,s[k],s[i+1],s[i+1]=gv(e,t,n),r,i)}}function pd(e,t){return t>=e.expandoStartIndex}function hd(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[bt()],s=pd(e,n);md(i,r)&&t===null&&!s&&(t=!1),t=dv(o,i,t,r),Xy(o,i,t,n,s,r)}}function dv(e,t,n,r){let o=Kp(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=ii(null,e,t,n,r),n=gn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=ii(o,e,t,n,r),i===null){let u=fv(e,t,r);u!==void 0&&Array.isArray(u)&&(u=ii(null,e,t,u[1],r),u=gn(u,t.attrs,r),pv(e,t,r,u))}else i=hv(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function fv(e,t,n){let r=n?t.classBindings:t.styleBindings;if(qt(r)!==0)return e[wt(r)]}function pv(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[wt(o)]=r}function hv(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=gn(r,s,n)}return gn(r,t.attrs,n)}function ii(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=gn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function gn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ys(e,s,n?!0:t[++i]))}return e===void 0?null:e}function gv(e,t,n){if(n==null||n==="")return J;let r=[],o=En(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function mv(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ys(e,r,n)}function yv(e,t,n,r,o,i,s,a){o===Se&&(o=J);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=u<o.length?o[u+1]:void 0,f=c<i.length?i[c+1]:void 0,h=null,w;l===d?(u+=2,c+=2,p!==f&&(h=d,w=f)):d===null||l!==null&&l<d?(u+=2,h=l):(c+=2,h=d,w=f),h!==null&&gd(e,t,n,r,h,w,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function gd(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=Jy(c)?Vu(u,t,n,o,qt(c),s):void 0;if(!Hr(l)){Hr(i)||Yy(c)&&(i=Vu(u,null,n,o,a,s));let d=Mc(bt(),n);wm(r,s,d,o,i)}}function Vu(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,p=n[o+1];p===Se&&(p=d?J:void 0);let f=d?Yo(p,r):l===r?p:void 0;if(c&&!Hr(f)&&(f=Yo(u,r)),Hr(f)&&(a=f,s))return a;let h=e[o+1];o=s?wt(h):qt(h)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=Yo(u,r))}return a}function Hr(e){return e!==void 0}function vv(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=q(En(e)))),e}function md(e,t){return(e.flags&(t?8:16))!==0}function Dv(e,t,n,r,o,i){let s=t.consts,a=$t(s,o),u=wn(t,e,2,r,a);return Hs(t,n,u,$t(s,i)),u.attrs!==null&&Lr(u,u.attrs,!1),u.mergedAttrs!==null&&Lr(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function yd(e,t,n,r){let o=_(),i=L(),s=te+e,a=o[k],u=i.firstCreatePass?Dv(s,i,o,t,n,r):i.data[s],c=Iv(i,o,u,a,t,e);o[s]=c;let l=Yr(u);return Ct(u,!0),Rl(a,c,u),!Ry(u)&&eo()&&uo(i,o,c,u),jp()===0&&Ye(c,o),Bp(),l&&(Ls(i,o,u),Fs(i,u,o)),r!==null&&Vs(o,u),yd}function vd(){let e=$();ws()?kc():(e=e.parent,Ct(e,!1));let t=e;Up(t)&&zp(),Hp();let n=L();return n.firstCreatePass&&(no(n,e),Ds(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&oh(t)&&ss(n,t,_(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&ih(t)&&ss(n,t,_(),t.stylesWithoutHost,!1),vd}function Ev(e,t,n,r){return yd(e,t,n,r),vd(),Ev}var Iv=(e,t,n,r,o,i)=>(to(!0),Al(r,o,eh()));function wv(e,t,n,r,o){let i=t.consts,s=$t(i,r),a=wn(t,e,8,"ng-container",s);s!==null&&Lr(a,s,!0);let u=$t(i,o);return Hs(t,n,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function Cv(e,t,n){let r=_(),o=L(),i=e+te,s=o.firstCreatePass?wv(i,o,r,t,n):o.data[i];Ct(s,!0);let a=_v(o,r,s,e);return r[i]=a,eo()&&uo(o,r,a,s),Ye(a,r),Yr(s)&&(Ls(o,r,s),Fs(o,s,r)),n!=null&&Vs(r,s),Cv}function bv(){let e=$(),t=L();return ws()?kc():(e=e.parent,Ct(e,!1)),t.firstCreatePass&&(no(t,e),Ds(e)&&t.queries.elementEnd(e)),bv}var _v=(e,t,n,r)=>(to(!0),Fg(t[k],""));function aT(){return _()}function Mv(e,t,n){let r=_(),o=Dn();if(Te(r,o,t)){let i=L(),s=Xr();Bs(i,s,r,e,t,r[k],n,!0)}return Mv}var lt=void 0;function Tv(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var Sv=["en",[["a","p"],["AM","PM"],lt],[["AM","PM"],lt,lt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],lt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],lt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",lt,"{1} 'at' {0}",lt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Tv],si={};function ce(e){let t=Nv(e),n=ju(t);if(n)return n;let r=t.split("-")[0];if(n=ju(r),n)return n;if(r==="en")return Sv;throw new b(701,!1)}function ju(e){return e in si||(si[e]=Ue.ng&&Ue.ng.common&&Ue.ng.common.locales&&Ue.ng.common.locales[e]),si[e]}var V=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(V||{});function Nv(e){return e.toLowerCase().replace(/_/g,"-")}var $r="en-US";var xv=$r;function Av(e){typeof e=="string"&&(xv=e.toLowerCase().replace(/_/g,"-"))}var Ov=(e,t,n)=>{};function Rv(e,t,n,r){let o=_(),i=L(),s=$();return Dd(i,o,o[k],s,e,t,r),Rv}function Fv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Cr],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function Dd(e,t,n,r,o,i,s){let a=Yr(r),c=e.firstCreatePass&&Oc(e),l=t[me],d=Ac(t),p=!0;if(r.type&3||s){let w=ue(r,t),P=s?s(w):w,A=d.length,ie=s?de=>s(Me(de[r.index])):r.index,Ne=null;if(!s&&a&&(Ne=Fv(e,t,o,r.index)),Ne!==null){let de=Ne.__ngLastListenerFn__||Ne;de.__ngNextListenerFn__=i,Ne.__ngLastListenerFn__=i,p=!1}else{i=Hu(r,t,l,i),Ov(w,o,i);let de=n.listen(P,o,i);d.push(i,de),c&&c.push(o,ie,A,A+1)}}else i=Hu(r,t,l,i);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let w=h.length;if(w)for(let P=0;P<w;P+=2){let A=h[P],ie=h[P+1],Cn=t[A][ie].subscribe(i),se=d.length;d.push(i,Cn),c&&c.push(o,r.index,se,-(se+1))}}}function Bu(e,t,n,r){let o=M(null);try{return ae(6,t,n),n(r)!==!1}catch(i){return Hl(e,i),!1}finally{ae(7,t,n),M(o)}}function Hu(e,t,n,r){return function o(i){if(i===Function)return r;let s=e.componentOffset>-1?Xe(e.index,t):t;qs(s,5);let a=Bu(t,n,r,i),u=o.__ngNextListenerFn__;for(;u;)a=Bu(t,n,u,i)&&a,u=u.__ngNextListenerFn__;return a}}function uT(e=1){return Xp(e)}function cT(e,t,n,r){fy(e,t,n,r)}function lT(e,t,n){dy(e,t,n)}function dT(e){let t=_(),n=L(),r=Vc();bs(r+1);let o=Qs(n,r);if(e.dirty&&Fp(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=gy(t,r);e.reset(i,Nh),e.notifyOnChanges()}return!0}return!1}function fT(){return ly(_(),Vc())}function kv(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function pT(e){let t=Wp();return Sc(t,te+e)}function hT(e,t=""){let n=_(),r=L(),o=e+te,i=r.firstCreatePass?wn(r,o,1,t,null):r.data[o],s=Pv(r,n,i,t,e);n[o]=s,eo()&&uo(r,n,s,i),Ct(i,!1)}var Pv=(e,t,n,r,o)=>(to(!0),Og(t[k],r));function Lv(e){return Ed("",e,""),Lv}function Ed(e,t,n){let r=_(),o=Qy(r,e,t,n);return o!==Se&&Vv(r,bt(),o),Ed}function Vv(e,t,n){let r=Mc(t,e);Rg(e[k],r,n)}function jv(e,t,n){ul(t)&&(t=t());let r=_(),o=Dn();if(Te(r,o,t)){let i=L(),s=Xr();Bs(i,s,r,e,t,r[k],n,!1)}return jv}function gT(e,t){let n=ul(e);return n&&e.set(t),n}function Bv(e,t){let n=_(),r=L(),o=$();return Dd(r,n,n[k],o,e,t),Bv}function Hv(e,t,n){let r=L();if(r.firstCreatePass){let o=Ze(e);as(n,r.data,r.blueprint,o,!0),as(t,r.data,r.blueprint,o,!1)}}function as(e,t,n,r,o){if(e=W(e),Array.isArray(e))for(let i=0;i<e.length;i++)as(e[i],t,n,r,o);else{let i=L(),s=_(),a=$(),u=jt(e)?e:W(e.provide),c=gc(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(jt(e)||!e.multi){let f=new yt(c,o,Z),h=ui(u,t,o?l:l+p,d);h===-1?(Ci(Nr(a,s),i,u),ai(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=ui(u,t,l+p,d),h=ui(u,t,l,l+p),w=f>=0&&n[f],P=h>=0&&n[h];if(o&&!P||!o&&!w){Ci(Nr(a,s),i,u);let A=zv(o?Uv:$v,n.length,o,r,c);!o&&P&&(n[h].providerFactory=A),ai(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(A),s.push(A)}else{let A=Id(n[o?h:f],c,!o&&r);ai(i,e,f>-1?f:h,A)}!o&&r&&P&&n[h].componentProviders++}}}function ai(e,t,n,r){let o=jt(t),i=Ip(t);if(o||i){let u=(i?W(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function Id(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ui(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function $v(e,t,n,r){return us(this.multi,[])}function Uv(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=vt(n,n[E],this.providerFactory.index,r);i=a.slice(0,s),us(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],us(o,i);return i}function us(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function zv(e,t,n,r,o){let i=new yt(e,n,Z);return i.multi=[],i.index=t,i.componentProviders=0,Id(i,o,r&&!n),i}function mT(e,t=[]){return n=>{n.providersResolver=(r,o)=>Hv(r,o?o(e):e,t)}}function yT(e,t,n){let r=Cs()+e,o=_();return o[r]===Se?Ys(o,r,n?t.call(n):t()):Ay(o,r)}function vT(e,t,n,r){return Gv(_(),Cs(),e,t,n,r)}function wd(e,t){let n=e[t];return n===Se?void 0:n}function Gv(e,t,n,r,o,i){let s=t+n;return Te(e,s,o)?Ys(e,s+1,i?r.call(i,o):r(o)):wd(e,s+1)}function Wv(e,t,n,r,o,i,s){let a=t+n;return Oy(e,a,o,i)?Ys(e,a+2,s?r.call(s,o,i):r(o,i)):wd(e,a+2)}function DT(e,t){let n=L(),r,o=e+te;n.firstCreatePass?(r=qv(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=ft(r.type,!0)),s,a=K(Z);try{let u=Sr(!1),c=i();return Sr(u),kv(n,_(),o,c),c}finally{K(a)}}function qv(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function ET(e,t,n,r){let o=e+te,i=_(),s=Sc(i,o);return Zv(i,o)?Wv(i,Cs(),t,s.transform,n,r,s):s.transform(n,r)}function Zv(e,t){return e[E].data[t].pure}function IT(e,t){return lo(e,t)}var cs=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},wT=(()=>{class e{compileModuleSync(n){return new ns(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=uc(n),i=Tl(o.declarations).reduce((s,a)=>{let u=Oe(a);return u&&s.push(new It(u)),s},[]);return new cs(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Qv=(()=>{class e{zone=D(re);changeDetectionScheduler=D(zt);applicationRef=D(hn);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Yv=new S("",{factory:()=>!1});function Cd({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new re(Ee(De({},bd()),{scheduleInRootZone:n})),[{provide:re,useFactory:e},{provide:Er,multi:!0,useFactory:()=>{let r=D(Qv,{optional:!0});return()=>r.initialize()}},{provide:Er,multi:!0,useFactory:()=>{let r=D(Kv);return()=>{r.initialize()}}},t===!0?{provide:ol,useValue:!0}:[],{provide:il,useValue:n??rl}]}function CT(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Cd({ngZoneFactory:()=>{let o=bd(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Mt("NgZone_CoalesceEvent"),new re(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return mp([{provide:Yv,useValue:!0},{provide:Ns,useValue:!1},r])}function bd(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Kv=(()=>{class e{subscription=new j;initialized=!1;zone=D(re);pendingTasks=D(ro);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{re.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{re.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Jv=(()=>{class e{appRef=D(hn);taskService=D(ro);ngZone=D(re);zonelessEnabled=D(Ns);tracing=D(oo,{optional:!0});disableScheduling=D(ol,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new j;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ar):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(D(il,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Si||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 8:{this.appRef.deferredDirtyFlags|=8;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 13:{this.appRef.dirtyFlags|=16,r=!0;break}case 14:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{r=!0;break}case 10:case 9:case 7:case 11:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?gu:sl;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ar+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,gu(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Xv(){return typeof $localize<"u"&&$localize.locale||$r}var po=new S("",{providedIn:"root",factory:()=>D(po,C.Optional|C.SkipSelf)||Xv()});var ls=new S(""),eD=new S("");function sn(e){return!e.moduleRef}function tD(e){let t=sn(e)?e.r3Injector:e.moduleRef.injector,n=t.get(re);return n.run(()=>{sn(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Gt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),sn(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ls);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ls);s.add(i),e.moduleRef.onDestroy(()=>{gr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Wy(r,n,()=>{let i=t.get(ld);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(po,$r);if(Av(s||$r),!t.get(eD,!0))return sn(e)?t.get(hn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(sn(e)){let u=t.get(hn);return e.rootComponent!==void 0&&u.bootstrap(e.rootComponent),u}else return nD(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function nD(e,t){let n=e.injector.get(hn);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new b(-403,!1);t.push(e)}var mr=null;function rD(e=[],t){return Qe.create({name:t,providers:[{provide:hc,useValue:"platform"},{provide:ls,useValue:new Set([()=>mr=null])},...e]})}function oD(e=[]){if(mr)return mr;let t=rD(e);return mr=t,Uy(),iD(t),t}function iD(e){let t=e.get(Vh,null);mc(e,()=>{t?.forEach(n=>n())})}var _d=(()=>{class e{static __NG_ELEMENT_ID__=sD}return e})();function sD(e){return aD($(),_(),(e&16)===16)}function aD(e,t,n){if(Qr(e)&&!n){let r=Xe(e.index,t);return new Dt(r,r)}else if(e.type&175){let r=t[_e];return new Dt(r,t)}return null}var ds=class{constructor(){}supports(t){return ud(t)}create(t){return new fs(t)}},uD=(e,t)=>t,fs=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||uD}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<$u(r,o,i)?n:r,a=$u(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let p=0;p<c;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;l<=h&&h<c&&(i[p]=f+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!ud(t))throw new b(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Ny(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ps(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Ur),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ur),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ps=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},hs=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Ur=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new hs,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function $u(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Uu(){return new Js([new ds])}var Js=(()=>{class e{factories;static \u0275prov=R({token:e,providedIn:"root",factory:Uu});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Uu()),deps:[[e,new lp,new cp]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new b(901,!1)}}return e})();function bT(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=oD(r),i=[Cd({}),{provide:zt,useExisting:Jv},...n||[]],s=new Br({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return tD({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}}function cD(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function _T(e,t){Mt("NgSignals");let n=wa(e);return t?.equal&&(n[X].equal=t.equal),n}function lD(e){let t=M(null);try{return e()}finally{M(t)}}var zu=class{[X];constructor(t){this[X]=t}destroy(){this[X].destroy()}};function MT(e,t){let n=Oe(e),r=t.elementInjector||qr();return new It(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function TT(e){let t=Oe(e);if(!t)return null;let n=new It(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var Fd=null;function Xs(){return Fd}function eS(e){Fd??=e}var Md=class{};var aa=new S(""),ua=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>D(fD),providedIn:"platform"})}return e})(),tS=new S(""),fD=(()=>{class e extends ua{_location;_history;_doc=D(aa);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Xs().getBaseHref(this._doc)}onPopState(n){let r=Xs().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Xs().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function ca(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function Td(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function Be(e){return e&&e[0]!=="?"?"?"+e:e}var Co=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>D(pD),providedIn:"root"})}return e})(),kd=new S(""),pD=(()=>{class e extends Co{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??D(aa).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return ca(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Be(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Be(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Be(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(U(ua),U(kd,8))};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),nS=(()=>{class e extends Co{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=ca(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Be(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Be(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(U(ua),U(kd,8))};static \u0275prov=R({token:e,factory:e.\u0275fac})}return e})(),hD=(()=>{class e{_subject=new Ie;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=yD(Td(Sd(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Be(r))}normalize(n){return e.stripTrailingSlash(mD(this._basePath,Sd(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Be(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Be(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Be;static joinWithSlash=ca;static stripTrailingSlash=Td;static \u0275fac=function(r){return new(r||e)(U(Co))};static \u0275prov=R({token:e,factory:()=>gD(),providedIn:"root"})}return e})();function gD(){return new hD(U(Co))}function mD(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Sd(e){return e.replace(/\/index.html$/,"")}function yD(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Q=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Q||{}),x=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(x||{}),oe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(oe||{}),et={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function vD(e){return ce(e)[V.LocaleId]}function DD(e,t,n){let r=ce(e),o=[r[V.DayPeriodsFormat],r[V.DayPeriodsStandalone]],i=le(o,t);return le(i,n)}function ED(e,t,n){let r=ce(e),o=[r[V.DaysFormat],r[V.DaysStandalone]],i=le(o,t);return le(i,n)}function ID(e,t,n){let r=ce(e),o=[r[V.MonthsFormat],r[V.MonthsStandalone]],i=le(o,t);return le(i,n)}function wD(e,t){let r=ce(e)[V.Eras];return le(r,t)}function ho(e,t){let n=ce(e);return le(n[V.DateFormat],t)}function go(e,t){let n=ce(e);return le(n[V.TimeFormat],t)}function mo(e,t){let r=ce(e)[V.DateTimeFormat];return le(r,t)}function bo(e,t){let n=ce(e),r=n[V.NumberSymbols][t];if(typeof r>"u"){if(t===et.CurrencyDecimal)return n[V.NumberSymbols][et.Decimal];if(t===et.CurrencyGroup)return n[V.NumberSymbols][et.Group]}return r}function Pd(e){if(!e[V.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[V.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function CD(e){let t=ce(e);return Pd(t),(t[V.ExtraData][2]||[]).map(r=>typeof r=="string"?ea(r):[ea(r[0]),ea(r[1])])}function bD(e,t,n){let r=ce(e);Pd(r);let o=[r[V.ExtraData][0],r[V.ExtraData][1]],i=le(o,t)||[];return le(i,n)||[]}function le(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function ea(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var _D=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,yo={},MD=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function TD(e,t,n,r){let o=PD(e);t=je(n,t)||t;let s=[],a;for(;t;)if(a=MD.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();r&&(u=Vd(r,u),o=kD(o,r,!0));let c="";return s.forEach(l=>{let d=RD(l);c+=d?d(o,n,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function wo(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function je(e,t){let n=vD(e);if(yo[n]??={},yo[n][t])return yo[n][t];let r="";switch(t){case"shortDate":r=ho(e,oe.Short);break;case"mediumDate":r=ho(e,oe.Medium);break;case"longDate":r=ho(e,oe.Long);break;case"fullDate":r=ho(e,oe.Full);break;case"shortTime":r=go(e,oe.Short);break;case"mediumTime":r=go(e,oe.Medium);break;case"longTime":r=go(e,oe.Long);break;case"fullTime":r=go(e,oe.Full);break;case"short":let o=je(e,"shortTime"),i=je(e,"shortDate");r=vo(mo(e,oe.Short),[o,i]);break;case"medium":let s=je(e,"mediumTime"),a=je(e,"mediumDate");r=vo(mo(e,oe.Medium),[s,a]);break;case"long":let u=je(e,"longTime"),c=je(e,"longDate");r=vo(mo(e,oe.Long),[u,c]);break;case"full":let l=je(e,"fullTime"),d=je(e,"fullDate");r=vo(mo(e,oe.Full),[l,d]);break}return r&&(yo[n][t]=r),r}function vo(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function ve(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function SD(e,t){return ve(e,3).substring(0,t)}function B(e,t,n=0,r=!1,o=!1){return function(i,s){let a=ND(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return SD(a,t);let u=bo(s,et.MinusSign);return ve(a,t,u,r,o)}}function ND(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function O(e,t,n=Q.Format,r=!1){return function(o,i){return xD(o,i,e,t,n,r)}}function xD(e,t,n,r,o,i){switch(n){case 2:return ID(t,o,r)[e.getMonth()];case 1:return ED(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let c=CD(t),l=bD(t,o,r),d=c.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,w=s>=f.hours&&a>=f.minutes,P=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(w&&P)return!0}else if(w||P)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return DD(t,o,r)[s<12?0:1];case 3:return wD(t,r)[e.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function Do(e){return function(t,n,r){let o=-1*r,i=bo(n,et.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+ve(s,2,i)+ve(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+ve(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+ve(s,2,i)+":"+ve(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+ve(s,2,i)+":"+ve(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var AD=0,Io=4;function OD(e){let t=wo(e,AD,1).getDay();return wo(e,0,1+(t<=Io?Io:Io+7)-t)}function Ld(e){let t=e.getDay(),n=t===0?-3:Io-t;return wo(e.getFullYear(),e.getMonth(),e.getDate()+n)}function ta(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Ld(n),s=OD(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return ve(o,e,bo(r,et.MinusSign))}}function Eo(e,t=!1){return function(n,r){let i=Ld(n).getFullYear();return ve(i,e,bo(r,et.MinusSign),t)}}var na={};function RD(e){if(na[e])return na[e];let t;switch(e){case"G":case"GG":case"GGG":t=O(3,x.Abbreviated);break;case"GGGG":t=O(3,x.Wide);break;case"GGGGG":t=O(3,x.Narrow);break;case"y":t=B(0,1,0,!1,!0);break;case"yy":t=B(0,2,0,!0,!0);break;case"yyy":t=B(0,3,0,!1,!0);break;case"yyyy":t=B(0,4,0,!1,!0);break;case"Y":t=Eo(1);break;case"YY":t=Eo(2,!0);break;case"YYY":t=Eo(3);break;case"YYYY":t=Eo(4);break;case"M":case"L":t=B(1,1,1);break;case"MM":case"LL":t=B(1,2,1);break;case"MMM":t=O(2,x.Abbreviated);break;case"MMMM":t=O(2,x.Wide);break;case"MMMMM":t=O(2,x.Narrow);break;case"LLL":t=O(2,x.Abbreviated,Q.Standalone);break;case"LLLL":t=O(2,x.Wide,Q.Standalone);break;case"LLLLL":t=O(2,x.Narrow,Q.Standalone);break;case"w":t=ta(1);break;case"ww":t=ta(2);break;case"W":t=ta(1,!0);break;case"d":t=B(2,1);break;case"dd":t=B(2,2);break;case"c":case"cc":t=B(7,1);break;case"ccc":t=O(1,x.Abbreviated,Q.Standalone);break;case"cccc":t=O(1,x.Wide,Q.Standalone);break;case"ccccc":t=O(1,x.Narrow,Q.Standalone);break;case"cccccc":t=O(1,x.Short,Q.Standalone);break;case"E":case"EE":case"EEE":t=O(1,x.Abbreviated);break;case"EEEE":t=O(1,x.Wide);break;case"EEEEE":t=O(1,x.Narrow);break;case"EEEEEE":t=O(1,x.Short);break;case"a":case"aa":case"aaa":t=O(0,x.Abbreviated);break;case"aaaa":t=O(0,x.Wide);break;case"aaaaa":t=O(0,x.Narrow);break;case"b":case"bb":case"bbb":t=O(0,x.Abbreviated,Q.Standalone,!0);break;case"bbbb":t=O(0,x.Wide,Q.Standalone,!0);break;case"bbbbb":t=O(0,x.Narrow,Q.Standalone,!0);break;case"B":case"BB":case"BBB":t=O(0,x.Abbreviated,Q.Format,!0);break;case"BBBB":t=O(0,x.Wide,Q.Format,!0);break;case"BBBBB":t=O(0,x.Narrow,Q.Format,!0);break;case"h":t=B(3,1,-12);break;case"hh":t=B(3,2,-12);break;case"H":t=B(3,1);break;case"HH":t=B(3,2);break;case"m":t=B(4,1);break;case"mm":t=B(4,2);break;case"s":t=B(5,1);break;case"ss":t=B(5,2);break;case"S":t=B(6,1);break;case"SS":t=B(6,2);break;case"SSS":t=B(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Do(0);break;case"ZZZZZ":t=Do(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Do(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Do(2);break;default:return null}return na[e]=t,t}function Vd(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function FD(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function kD(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=Vd(t,o);return FD(e,r*(i-o))}function PD(e){if(Nd(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return wo(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(_D))return LD(r)}let t=new Date(e);if(!Nd(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function LD(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function Nd(e){return e instanceof Date&&!isNaN(e.valueOf())}function rS(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ra=/\s+/,xd=[],oS=(()=>{class e{_ngEl;_renderer;initialClasses=xd;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(ra):xd}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(ra):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(ra).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(Z(_t),Z(Zs))};static \u0275dir=fo({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var oa=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},iS=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new oa(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Ad(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Ad(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Z(Yt),Z(Et),Z(Js))};static \u0275dir=fo({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Ad(e,t){e.context.$implicit=t.item}var sS=(()=>{class e{_viewContainer;_context=new ia;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Od("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Od("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Z(Yt),Z(Et))};static \u0275dir=fo({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),ia=class{$implicit=null;ngIf=null};function Od(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${q(t)}'.`)}function VD(e,t){return new b(2100,!1)}var jD="mediumDate",BD=new S(""),HD=new S(""),aS=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??jD,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return TD(n,s,i||this.locale,a)}catch(s){throw VD(e,s.message)}}static \u0275fac=function(r){return new(r||e)(Z(po,16),Z(BD,24),Z(HD,24))};static \u0275pipe=id({name:"date",type:e,pure:!0})}return e})();var uS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=od({type:e});static \u0275inj=Ju({})}return e})(),$D="browser",UD="server";function cS(e){return e===$D}function lS(e){return e===UD}var dS=(()=>{class e{static \u0275prov=R({token:e,providedIn:"root",factory:()=>new sa(D(aa),window)})}return e})(),sa=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=zD(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function zD(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Rd=class{};var la=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(la||{}),hS="*";function gS(e,t=null){return{type:la.Sequence,steps:e,options:t}}function mS(e){return{type:la.Style,styles:e,offset:null}}var jd=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,n=0){this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Bd=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},yS="!";export{De as a,Ee as b,GD as c,WD as d,ga as e,j as f,ef as g,T as h,zo as i,Go as j,Ie as k,en as l,it as m,Ce as n,df as o,ff as p,pf as q,Ae as r,hf as s,at as t,wf as u,ut as v,rn as w,rr as x,bf as y,_f as z,Mf as A,ct as B,Tf as C,eu as D,Sf as E,Nf as F,on as G,Zo as H,xf as I,Rf as J,Ff as K,Qo as L,kf as M,Pf as N,Lf as O,Vf as P,jf as Q,Bf as R,Hf as S,b as T,Yu as U,R as V,Ju as W,FM as X,S as Y,C as Z,U as _,D as $,mp as aa,hc as ba,qe as ca,mc as da,Np as ea,kM as fa,PM as ga,LM as ha,VM as ia,yh as ja,Qe as ka,zt as la,ro as ma,ze as na,re as oa,Gt as pa,jM as qa,_t as ra,Mt as sa,BM as ta,HM as ua,$M as va,Vh as wa,jh as xa,UM as ya,zM as za,oo as Aa,Gh as Ba,Wh as Ca,pn as Da,En as Ea,Dl as Fa,GM as Ga,WM as Ha,qM as Ia,ZM as Ja,QM as Ka,El as La,YM as Ma,ks as Na,gg as Oa,KM as Pa,JM as Qa,XM as Ra,Z as Sa,eT as Ta,Rr as Ua,Et as Va,Pr as Wa,Zs as Xa,Yt as Ya,Je as Za,es as _a,my as $a,oT as ab,od as bb,fo as cb,id as db,wy as eb,Sy as fb,Py as gb,iT as hb,Ks as ib,By as jb,$y as kb,hn as lb,Zy as mb,sv as nb,av as ob,sT as pb,yd as qb,vd as rb,Ev as sb,Cv as tb,bv as ub,aT as vb,Mv as wb,Rv as xb,uT as yb,cT as zb,lT as Ab,dT as Bb,fT as Cb,pT as Db,hT as Eb,Lv as Fb,Ed as Gb,jv as Hb,gT as Ib,Bv as Jb,mT as Kb,yT as Lb,vT as Mb,DT as Nb,ET as Ob,IT as Pb,wT as Qb,CT as Rb,_d as Sb,bT as Tb,cD as Ub,_T as Vb,lD as Wb,MT as Xb,TT as Yb,Xs as Zb,eS as _b,Md as $b,aa as ac,tS as bc,Co as cc,pD as dc,nS as ec,hD as fc,rS as gc,oS as hc,iS as ic,sS as jc,aS as kc,uS as lc,$D as mc,cS as nc,lS as oc,dS as pc,Rd as qc,la as rc,hS as sc,gS as tc,mS as uc,jd as vc,Bd as wc,yS as xc};
