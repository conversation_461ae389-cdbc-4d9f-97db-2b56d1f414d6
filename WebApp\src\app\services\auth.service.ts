import { Injectable, inject } from '@angular/core';
import { StorageService } from './storage.service';
import { PlatformService } from './platform.service';
import { BehaviorSubject, Observable } from 'rxjs';

interface DecodedToken {
  email: string;
  nameid: string;
  nbf: number;
  exp: number;
  iat: number;
}

export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  username: string | null;
  email: string | null;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USERNAME_KEY = 'username';
  private readonly EMAIL_KEY = 'user_email';

  private storageService = inject(StorageService);
  private platformService = inject(PlatformService);

  // Auth state management
  private authStateSubject = new BehaviorSubject<AuthState>({
    isAuthenticated: false,
    token: null,
    username: null,
    email: null
  });

  public authState$ = this.authStateSubject.asObservable();

  constructor() {
    if (this.platformService.isServer) {
      console.log('[AuthService] Initialized for server-side rendering');
    } else {
      console.log('[AuthService] Initialized for client-side');
      // Only initialize auth state on client-side
      this.initializeAuthState();
    }
  }

  private initializeAuthState(): void {
    if (this.platformService.isBrowser) {
      const token = this.getToken();
      const username = this.getUsername();
      const email = this.getEmail();

      const isAuthenticated = this.isTokenValid(token);

      this.authStateSubject.next({
        isAuthenticated,
        token,
        username,
        email
      });

      console.log(`[AuthService] Initial auth state: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);
    }
  }

  setToken(token: string): boolean {
    try {
      const success = this.storageService.setItem(this.TOKEN_KEY, token, 60 * 24); // 24 hours expiry
      if (success) {
        // Extract user info from token
        const decodedToken = this.decodeToken(token);
        if (decodedToken?.email) {
          this.setUsername(decodedToken.email);
          this.setEmail(decodedToken.email);
        }

        // Update auth state
        this.updateAuthState();
        console.log('[AuthService] Token set successfully');
        return true;
      }
    } catch (error) {
      console.error('[AuthService] Error setting token:', error);
    }
    return false;
  }

  getToken(): string | null {
    if (this.platformService.isServer) {
      console.log('[AuthService] Token access on server-side, returning null');
      return null;
    }
    return this.storageService.getItem(this.TOKEN_KEY);
  }

  setEmail(email: string): boolean {
    return this.storageService.setItem(this.EMAIL_KEY, email);
  }

  getEmail(): string | null {
    if (this.platformService.isServer) {
      return null;
    }
    return this.storageService.getItem(this.EMAIL_KEY);
  }

  private isTokenValid(token: string | null): boolean {
    if (!token) {
      return false;
    }

    try {
      const decodedToken = this.decodeToken(token);
      if (!decodedToken) {
        return false;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const isValid = decodedToken.exp > currentTime;

      if (!isValid) {
        console.log('[AuthService] Token expired');
        this.removeToken();
      }

      return isValid;
    } catch (error) {
      console.error('[AuthService] Error validating token:', error);
      return false;
    }
  }

  private updateAuthState(): void {
    if (this.platformService.isBrowser) {
      const token = this.getToken();
      const username = this.getUsername();
      const email = this.getEmail();
      const isAuthenticated = this.isTokenValid(token);

      this.authStateSubject.next({
        isAuthenticated,
        token,
        username,
        email
      });
    }
  }

  removeToken(): boolean {
    try {
      const tokenRemoved = this.storageService.removeItem(this.TOKEN_KEY);
      const usernameRemoved = this.storageService.removeItem(this.USERNAME_KEY);
      const emailRemoved = this.storageService.removeItem(this.EMAIL_KEY);

      if (tokenRemoved || usernameRemoved || emailRemoved) {
        this.updateAuthState();
        console.log('[AuthService] Auth data cleared');
        return true;
      }
    } catch (error) {
      console.error('[AuthService] Error removing token:', error);
    }
    return false;
  }

  private decodeToken(token: string): DecodedToken | null {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

      let jsonPayload: string;
      if (this.platformService.isBrowser) {
        // Use atob in browser
        jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
            .join('')
        );
      } else {
        // Use Buffer on server (Node.js)
        jsonPayload = Buffer.from(base64, 'base64').toString('utf-8');
      }

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  setUsername(username: string): void {
    this.storageService.setItem(this.USERNAME_KEY, username);
  }

  getUsername(): string | null {
    const token = this.getToken();
    if (token) {
      const decodedToken = this.decodeToken(token);
      if (decodedToken?.email) {
        return decodedToken.email;
      }
    }
    return this.storageService.getItem(this.USERNAME_KEY);
  }

  login(token: string): void {
    this.setToken(token);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    const decodedToken = this.decodeToken(token);
    if (!decodedToken) return false;

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    return decodedToken.exp > currentTime;
  }

  clearToken(): void {
    this.storageService.removeItem(this.TOKEN_KEY);
    this.storageService.removeItem(this.USERNAME_KEY);
  }

  logout(): void {
    this.clearToken();
  }

  isLoggedIn(): boolean {
    // Check if user has valid auth token
    const token = this.storageService.getItem(this.TOKEN_KEY);
    return !!token; // Returns true if token exists, false otherwise
  }
}
