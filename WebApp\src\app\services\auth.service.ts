import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { PlatformService } from './platform.service';

interface DecodedToken {
  email: string;
  nameid: string;
  nbf: number;
  exp: number;
  iat: number;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USERNAME_KEY = 'username';

  constructor(
    private storageService: StorageService,
    private platformService: PlatformService
  ) {}

  setToken(token: string): void {
    this.storageService.setItem(this.TOKEN_KEY, token);
    // Extract email from token and save as username
    const decodedToken = this.decodeToken(token);
    if (decodedToken?.email) {
      this.setUsername(decodedToken.email);
    }
  }

  getToken(): string | null {
    return this.storageService.getItem(this.TOKEN_KEY);
  }

  private decodeToken(token: string): DecodedToken | null {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

      let jsonPayload: string;
      if (this.platformService.isBrowser) {
        // Use atob in browser
        jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
            .join('')
        );
      } else {
        // Use Buffer on server (Node.js)
        jsonPayload = Buffer.from(base64, 'base64').toString('utf-8');
      }

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  setUsername(username: string): void {
    this.storageService.setItem(this.USERNAME_KEY, username);
  }

  getUsername(): string | null {
    const token = this.getToken();
    if (token) {
      const decodedToken = this.decodeToken(token);
      if (decodedToken?.email) {
        return decodedToken.email;
      }
    }
    return this.storageService.getItem(this.USERNAME_KEY);
  }

  login(token: string): void {
    this.setToken(token);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    const decodedToken = this.decodeToken(token);
    if (!decodedToken) return false;

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    return decodedToken.exp > currentTime;
  }

  clearToken(): void {
    this.storageService.removeItem(this.TOKEN_KEY);
    this.storageService.removeItem(this.USERNAME_KEY);
  }

  logout(): void {
    this.clearToken();
  }

  isLoggedIn(): boolean {
    // Check if user has valid auth token
    const token = this.storageService.getItem(this.TOKEN_KEY);
    return !!token; // Returns true if token exists, false otherwise
  }
}
