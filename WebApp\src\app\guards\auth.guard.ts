import { inject } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { PlatformService } from '../services/platform.service';
import { Observable, of } from 'rxjs';

export const authGuard = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | boolean => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const platformService = inject(PlatformService);

  // On server-side, always allow rendering for SEO
  if (platformService.isServer) {
    console.log(`[SSR] Allowing access to ${state.url} for server-side rendering`);
    return true;
  }

  // On client-side, check authentication
  console.log(`[Client] Checking authentication for ${state.url}`);

  if (authService.isAuthenticated()) {
    console.log('[Client] User is authenticated');
    return true;
  }

  console.log('[Client] User not authenticated, redirecting to login');
  router.navigate(['/login'], {
    queryParams: { returnUrl: state.url },
    replaceUrl: true
  });
  return false;
};
