import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { PlatformService } from '../services/platform.service';

export const authGuard = () => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const platformService = inject(PlatformService);

  // On server-side, allow access to render the page structure
  // The client-side will handle the actual authentication check
  if (platformService.isServer) {
    return true;
  }

  // On client-side, perform normal authentication check
  if (authService.isAuthenticated()) {
    return true;
  }

  router.navigate(['/login']);
  return false;
};
