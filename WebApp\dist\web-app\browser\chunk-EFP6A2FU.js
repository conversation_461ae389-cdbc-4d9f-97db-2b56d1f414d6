import{T as S,V as dt,a as le,d as ft,rc as E,sc as B,tc as mt,uc as Ne,vc as x,wc as Me,xc as ue}from"./chunk-PSHLULH3.js";function pt(i){return new S(3e3,!1)}function jt(){return new S(3100,!1)}function Wt(){return new S(3101,!1)}function Gt(i){return new S(3001,!1)}function Ht(i){return new S(3003,!1)}function Yt(i){return new S(3004,!1)}function Xt(i,e){return new S(3005,!1)}function Zt(){return new S(3006,!1)}function Jt(){return new S(3007,!1)}function xt(i,e){return new S(3008,!1)}function es(i){return new S(3002,!1)}function ts(i,e,t,s,n){return new S(3010,!1)}function ss(){return new S(3011,!1)}function ns(){return new S(3012,!1)}function is(){return new S(3200,!1)}function rs(){return new S(3202,!1)}function as(){return new S(3013,!1)}function os(i){return new S(3014,!1)}function ls(i){return new S(3015,!1)}function us(i){return new S(3016,!1)}function cs(i){return new S(3500,!1)}function hs(i){return new S(3501,!1)}function fs(i,e){return new S(3404,!1)}function ds(i){return new S(3502,!1)}function ms(i){return new S(3503,!1)}function ps(){return new S(3300,!1)}function gs(i){return new S(3504,!1)}function ys(i){return new S(3301,!1)}function _s(i,e){return new S(3302,!1)}function Ss(i){return new S(3303,!1)}function Es(i,e){return new S(3400,!1)}function Ts(i){return new S(3401,!1)}function vs(i){return new S(3402,!1)}function ws(i,e){return new S(3505,!1)}var bs=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function U(i){switch(i.length){case 0:return new x;case 1:return i[0];default:return new Me(i)}}function Ot(i,e,t=new Map,s=new Map){let n=[],r=[],a=-1,o=null;if(e.forEach(l=>{let u=l.get("offset"),h=u==a,c=h&&o||new Map;l.forEach((_,y)=>{let d=y,g=_;if(y!=="offset")switch(d=i.normalizePropertyName(d,n),g){case ue:g=t.get(y);break;case B:g=s.get(y);break;default:g=i.normalizeStyleValue(y,d,g,n);break}c.set(d,g)}),h||r.push(c),o=c,a=u}),n.length)throw ds(n);return r}function xe(i,e,t,s){switch(e){case"start":i.onStart(()=>s(t&&Ce(t,"start",i)));break;case"done":i.onDone(()=>s(t&&Ce(t,"done",i)));break;case"destroy":i.onDestroy(()=>s(t&&Ce(t,"destroy",i)));break}}function Ce(i,e,t){let s=t.totalTime,n=!!t.disabled,r=et(i.element,i.triggerName,i.fromState,i.toState,e||i.phaseName,s??i.totalTime,n),a=i._data;return a!=null&&(r._data=a),r}function et(i,e,t,s,n="",r=0,a){return{element:i,triggerName:e,fromState:t,toState:s,phaseName:n,totalTime:r,disabled:!!a}}function L(i,e,t){let s=i.get(e);return s||i.set(e,s=t),s}function gt(i){let e=i.indexOf(":"),t=i.substring(1,e),s=i.slice(e+1);return[t,s]}var As=typeof document>"u"?null:document.documentElement;function tt(i){let e=i.parentNode||i.host||null;return e===As?null:e}function Ps(i){return i.substring(1,6)=="ebkit"}var H=null,yt=!1;function Ns(i){H||(H=Ms()||{},yt=H.style?"WebkitAppearance"in H.style:!1);let e=!0;return H.style&&!Ps(i)&&(e=i in H.style,!e&&yt&&(e="Webkit"+i.charAt(0).toUpperCase()+i.slice(1)in H.style)),e}function bn(i){return bs.has(i)}function Ms(){return typeof document<"u"?document.body:null}function Lt(i,e){for(;e;){if(e===i)return!0;e=tt(e)}return!1}function Ft(i,e,t){if(t)return Array.from(i.querySelectorAll(e));let s=i.querySelector(e);return s?[s]:[]}var It=(()=>{class i{validateStyleProperty(t){return Ns(t)}containsElement(t,s){return Lt(t,s)}getParentElement(t){return tt(t)}query(t,s,n){return Ft(t,s,n)}computeStyle(t,s,n){return n||""}animate(t,s,n,r,a,o=[],l){return new x(n,r)}static \u0275fac=function(s){return new(s||i)};static \u0275prov=dt({token:i,factory:i.\u0275fac})}return i})(),_t=class{static NOOP=new It},Fe=class{},Ie=class{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,n){return s}},Cs=1e3,zt="{{",ks="}}",st="ng-enter",pe="ng-leave",ce="ng-trigger",ge=".ng-trigger",St="ng-animating",ze=".ng-animating";function $(i){if(typeof i=="number")return i;let e=i.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:Ke(parseFloat(e[1]),e[2])}function Ke(i,e){switch(e){case"s":return i*Cs;default:return i}}function ye(i,e,t){return i.hasOwnProperty("duration")?i:Ds(i,e,t)}function Ds(i,e,t){let s=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,n,r=0,a="";if(typeof i=="string"){let o=i.match(s);if(o===null)return e.push(pt(i)),{duration:0,delay:0,easing:""};n=Ke(parseFloat(o[1]),o[2]);let l=o[3];l!=null&&(r=Ke(parseFloat(l),o[4]));let u=o[5];u&&(a=u)}else n=i;if(!t){let o=!1,l=e.length;n<0&&(e.push(jt()),o=!0),r<0&&(e.push(Wt()),o=!0),o&&e.splice(l,0,pt(i))}return{duration:n,delay:r,easing:a}}function Rs(i){return i.length?i[0]instanceof Map?i:i.map(e=>new Map(Object.entries(e))):[]}function Et(i){return Array.isArray(i)?new Map(...i):new Map(i)}function Q(i,e,t){e.forEach((s,n)=>{let r=nt(n);t&&!t.has(n)&&t.set(n,i.style[r]),i.style[r]=s})}function X(i,e){e.forEach((t,s)=>{let n=nt(s);i.style[n]=""})}function se(i){return Array.isArray(i)?i.length==1?i[0]:mt(i):i}function Os(i,e,t){let s=e.params||{},n=Kt(i);n.length&&n.forEach(r=>{s.hasOwnProperty(r)||t.push(Gt(r))})}var qe=new RegExp(`${zt}\\s*(.+?)\\s*${ks}`,"g");function Kt(i){let e=[];if(typeof i=="string"){let t;for(;t=qe.exec(i);)e.push(t[1]);qe.lastIndex=0}return e}function ie(i,e,t){let s=`${i}`,n=s.replace(qe,(r,a)=>{let o=e[a];return o==null&&(t.push(Ht(a)),o=""),o.toString()});return n==s?i:n}var Ls=/-+([a-z0-9])/g;function nt(i){return i.replace(Ls,(...e)=>e[1].toUpperCase())}function An(i){return i.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Fs(i,e){return i===0||e===0}function Is(i,e,t){if(t.size&&e.length){let s=e[0],n=[];if(t.forEach((r,a)=>{s.has(a)||n.push(a),s.set(a,r)}),n.length)for(let r=1;r<e.length;r++){let a=e[r];n.forEach(o=>a.set(o,it(i,o)))}}return e}function O(i,e,t){switch(e.type){case E.Trigger:return i.visitTrigger(e,t);case E.State:return i.visitState(e,t);case E.Transition:return i.visitTransition(e,t);case E.Sequence:return i.visitSequence(e,t);case E.Group:return i.visitGroup(e,t);case E.Animate:return i.visitAnimate(e,t);case E.Keyframes:return i.visitKeyframes(e,t);case E.Style:return i.visitStyle(e,t);case E.Reference:return i.visitReference(e,t);case E.AnimateChild:return i.visitAnimateChild(e,t);case E.AnimateRef:return i.visitAnimateRef(e,t);case E.Query:return i.visitQuery(e,t);case E.Stagger:return i.visitStagger(e,t);default:throw Yt(e.type)}}function it(i,e){return window.getComputedStyle(i)[e]}var zs=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Be=class extends Fe{normalizePropertyName(e,t){return nt(e)}normalizeStyleValue(e,t,s,n){let r="",a=s.toString().trim();if(zs.has(t)&&s!==0&&s!=="0")if(typeof s=="number")r="px";else{let o=s.match(/^[+-]?[\d\.]+([a-z]*)$/);o&&o[1].length==0&&n.push(Xt(e,s))}return a+r}};var _e="*";function Ks(i,e){let t=[];return typeof i=="string"?i.split(/\s*,\s*/).forEach(s=>qs(s,t,e)):t.push(i),t}function qs(i,e,t){if(i[0]==":"){let l=Bs(i,t);if(typeof l=="function"){e.push(l);return}i=l}let s=i.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(s==null||s.length<4)return t.push(ls(i)),e;let n=s[1],r=s[2],a=s[3];e.push(Tt(n,a));let o=n==_e&&a==_e;r[0]=="<"&&!o&&e.push(Tt(a,n))}function Bs(i,e){switch(i){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(us(i)),"* => *"}}var he=new Set(["true","1"]),fe=new Set(["false","0"]);function Tt(i,e){let t=he.has(i)||fe.has(i),s=he.has(e)||fe.has(e);return(n,r)=>{let a=i==_e||i==n,o=e==_e||e==r;return!a&&t&&typeof n=="boolean"&&(a=n?he.has(i):fe.has(i)),!o&&s&&typeof r=="boolean"&&(o=r?he.has(e):fe.has(e)),a&&o}}var qt=":self",Qs=new RegExp(`s*${qt}s*,?`,"g");function rt(i,e,t,s){return new Qe(i).build(e,t,s)}var vt="",Qe=class{_driver;constructor(e){this._driver=e}build(e,t,s){let n=new $e(t);return this._resetContextStyleTimingState(n),O(this,se(e),n)}_resetContextStyleTimingState(e){e.currentQuerySelector=vt,e.collectedStyles=new Map,e.collectedStyles.set(vt,new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,n=t.depCount=0,r=[],a=[];return e.name.charAt(0)=="@"&&t.errors.push(Zt()),e.definitions.forEach(o=>{if(this._resetContextStyleTimingState(t),o.type==E.State){let l=o,u=l.name;u.toString().split(/\s*,\s*/).forEach(h=>{l.name=h,r.push(this.visitState(l,t))}),l.name=u}else if(o.type==E.Transition){let l=this.visitTransition(o,t);s+=l.queryCount,n+=l.depCount,a.push(l)}else t.errors.push(Jt())}),{type:E.Trigger,name:e.name,states:r,transitions:a,queryCount:s,depCount:n,options:null}}visitState(e,t){let s=this.visitStyle(e.styles,t),n=e.options&&e.options.params||null;if(s.containsDynamicStyles){let r=new Set,a=n||{};s.styles.forEach(o=>{o instanceof Map&&o.forEach(l=>{Kt(l).forEach(u=>{a.hasOwnProperty(u)||r.add(u)})})}),r.size&&t.errors.push(xt(e.name,[...r.values()]))}return{type:E.State,name:e.name,style:s,options:n?{params:n}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let s=O(this,se(e.animation),t),n=Ks(e.expr,t.errors);return{type:E.Transition,matchers:n,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:Y(e.options)}}visitSequence(e,t){return{type:E.Sequence,steps:e.steps.map(s=>O(this,s,t)),options:Y(e.options)}}visitGroup(e,t){let s=t.currentTime,n=0,r=e.steps.map(a=>{t.currentTime=s;let o=O(this,a,t);return n=Math.max(n,t.currentTime),o});return t.currentTime=n,{type:E.Group,steps:r,options:Y(e.options)}}visitAnimate(e,t){let s=js(e.timings,t.errors);t.currentAnimateTimings=s;let n,r=e.styles?e.styles:Ne({});if(r.type==E.Keyframes)n=this.visitKeyframes(r,t);else{let a=e.styles,o=!1;if(!a){o=!0;let u={};s.easing&&(u.easing=s.easing),a=Ne(u)}t.currentTime+=s.duration+s.delay;let l=this.visitStyle(a,t);l.isEmptyStep=o,n=l}return t.currentAnimateTimings=null,{type:E.Animate,timings:s,style:n,options:null}}visitStyle(e,t){let s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){let s=[],n=Array.isArray(e.styles)?e.styles:[e.styles];for(let o of n)typeof o=="string"?o===B?s.push(o):t.errors.push(es(o)):s.push(new Map(Object.entries(o)));let r=!1,a=null;return s.forEach(o=>{if(o instanceof Map&&(o.has("easing")&&(a=o.get("easing"),o.delete("easing")),!r)){for(let l of o.values())if(l.toString().indexOf(zt)>=0){r=!0;break}}}),{type:E.Style,styles:s,easing:a,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){let s=t.currentAnimateTimings,n=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(a=>{typeof a!="string"&&a.forEach((o,l)=>{let u=t.collectedStyles.get(t.currentQuerySelector),h=u.get(l),c=!0;h&&(r!=n&&r>=h.startTime&&n<=h.endTime&&(t.errors.push(ts(l,h.startTime,h.endTime,r,n)),c=!1),r=h.startTime),c&&u.set(l,{startTime:r,endTime:n}),t.options&&Os(o,t.options,t.errors)})})}visitKeyframes(e,t){let s={type:E.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(ss()),s;let n=1,r=0,a=[],o=!1,l=!1,u=0,h=e.steps.map(b=>{let A=this._makeStyleAst(b,t),C=A.offset!=null?A.offset:Us(A.styles),N=0;return C!=null&&(r++,N=A.offset=C),l=l||N<0||N>1,o=o||N<u,u=N,a.push(N),A});l&&t.errors.push(ns()),o&&t.errors.push(is());let c=e.steps.length,_=0;r>0&&r<c?t.errors.push(rs()):r==0&&(_=n/(c-1));let y=c-1,d=t.currentTime,g=t.currentAnimateTimings,v=g.duration;return h.forEach((b,A)=>{let C=_>0?A==y?1:_*A:a[A],N=C*v;t.currentTime=d+g.delay+N,g.duration=N,this._validateStyleAst(b,t),b.offset=C,s.styles.push(b)}),s}visitReference(e,t){return{type:E.Reference,animation:O(this,se(e.animation),t),options:Y(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:E.AnimateChild,options:Y(e.options)}}visitAnimateRef(e,t){return{type:E.AnimateRef,animation:this.visitReference(e.animation,t),options:Y(e.options)}}visitQuery(e,t){let s=t.currentQuerySelector,n=e.options||{};t.queryCount++,t.currentQuery=e;let[r,a]=$s(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,L(t.collectedStyles,t.currentQuerySelector,new Map);let o=O(this,se(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:E.Query,selector:r,limit:n.limit||0,optional:!!n.optional,includeSelf:a,animation:o,originalSelector:e.selector,options:Y(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(as());let s=e.timings==="full"?{duration:0,delay:0,easing:"full"}:ye(e.timings,t.errors,!0);return{type:E.Stagger,animation:O(this,se(e.animation),t),timings:s,options:null}}};function $s(i){let e=!!i.split(/\s*,\s*/).find(t=>t==qt);return e&&(i=i.replace(Qs,"")),i=i.replace(/@\*/g,ge).replace(/@\w+/g,t=>ge+"-"+t.slice(1)).replace(/:animating/g,ze),[i,e]}function Vs(i){return i?le({},i):null}var $e=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function Us(i){if(typeof i=="string")return null;let e=null;if(Array.isArray(i))i.forEach(t=>{if(t instanceof Map&&t.has("offset")){let s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(i instanceof Map&&i.has("offset")){let t=i;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function js(i,e){if(i.hasOwnProperty("duration"))return i;if(typeof i=="number"){let r=ye(i,e).duration;return ke(r,0,"")}let t=i;if(t.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=ke(0,0,"");return r.dynamic=!0,r.strValue=t,r}let n=ye(t,e);return ke(n.duration,n.delay,n.easing)}function Y(i){return i?(i=le({},i),i.params&&(i.params=Vs(i.params))):i={},i}function ke(i,e,t){return{duration:i,delay:e,easing:t}}function at(i,e,t,s,n,r,a=null,o=!1){return{type:1,element:i,keyframes:e,preStyleProps:t,postStyleProps:s,duration:n,delay:r,totalTime:n+r,easing:a,subTimeline:o}}var te=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},Ws=1,Gs=":enter",Hs=new RegExp(Gs,"g"),Ys=":leave",Xs=new RegExp(Ys,"g");function ot(i,e,t,s,n,r=new Map,a=new Map,o,l,u=[]){return new Ve().buildKeyframes(i,e,t,s,n,r,a,o,l,u)}var Ve=class{buildKeyframes(e,t,s,n,r,a,o,l,u,h=[]){u=u||new te;let c=new Ue(e,t,u,n,r,h,[]);c.options=l;let _=l.delay?$(l.delay):0;c.currentTimeline.delayNextStep(_),c.currentTimeline.setStyles([a],null,c.errors,l),O(this,s,c);let y=c.timelines.filter(d=>d.containsAnimation());if(y.length&&o.size){let d;for(let g=y.length-1;g>=0;g--){let v=y[g];if(v.element===t){d=v;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([o],null,c.errors,l)}return y.length?y.map(d=>d.buildKeyframes()):[at(t,[],[],[],0,_,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let s=t.subInstructions.get(t.element);if(s){let n=t.createSubContext(e.options),r=t.currentTimeline.currentTime,a=this._visitSubInstructions(s,n,n.options);r!=a&&t.transformIntoNewTimeline(a)}t.previousNode=e}visitAnimateRef(e,t){let s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(let n of e){let r=n?.delay;if(r){let a=typeof r=="number"?r:$(ie(r,n?.params??{},t.errors));s.delayNextStep(a)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime,a=s.duration!=null?$(s.duration):null,o=s.delay!=null?$(s.delay):null;return a!==0&&e.forEach(l=>{let u=t.appendInstructionToTimeline(l,a,o);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),O(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let s=t.subContextCount,n=t,r=e.options;if(r&&(r.params||r.delay)&&(n=t.createSubContext(r),n.transformIntoNewTimeline(),r.delay!=null)){n.previousNode.type==E.Style&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Se);let a=$(r.delay);n.delayNextStep(a)}e.steps.length&&(e.steps.forEach(a=>O(this,a,n)),n.currentTimeline.applyStylesToKeyframe(),n.subContextCount>s&&n.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let s=[],n=t.currentTimeline.currentTime,r=e.options&&e.options.delay?$(e.options.delay):0;e.steps.forEach(a=>{let o=t.createSubContext(e.options);r&&o.delayNextStep(r),O(this,a,o),n=Math.max(n,o.currentTimeline.currentTime),s.push(o.currentTimeline)}),s.forEach(a=>t.currentTimeline.mergeTimelineCollectedStyles(a)),t.transformIntoNewTimeline(n),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let s=e.strValue,n=t.params?ie(s,t.params,t.errors):s;return ye(n,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let s=t.currentAnimateTimings=this._visitTiming(e.timings,t),n=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),n.snapshotCurrentStyles());let r=e.style;r.type==E.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),n.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let s=t.currentTimeline,n=t.currentAnimateTimings;!n&&s.hasCurrentStyleProperties()&&s.forwardFrame();let r=n&&n.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let s=t.currentAnimateTimings,n=t.currentTimeline.duration,r=s.duration,o=t.createSubContext().currentTimeline;o.easing=s.easing,e.styles.forEach(l=>{let u=l.offset||0;o.forwardTime(u*r),o.setStyles(l.styles,l.easing,t.errors,t.options),o.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(o),t.transformIntoNewTimeline(n+r),t.previousNode=e}visitQuery(e,t){let s=t.currentTimeline.currentTime,n=e.options||{},r=n.delay?$(n.delay):0;r&&(t.previousNode.type===E.Style||s==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=Se);let a=s,o=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!n.optional,t.errors);t.currentQueryTotal=o.length;let l=null;o.forEach((u,h)=>{t.currentQueryIndex=h;let c=t.createSubContext(e.options,u);r&&c.delayNextStep(r),u===t.element&&(l=c.currentTimeline),O(this,e.animation,c),c.currentTimeline.applyStylesToKeyframe();let _=c.currentTimeline.currentTime;a=Math.max(a,_)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(a),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let s=t.parentContext,n=t.currentTimeline,r=e.timings,a=Math.abs(r.duration),o=a*(t.currentQueryTotal-1),l=a*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=o-l;break;case"full":l=s.currentStaggerTime;break}let h=t.currentTimeline;l&&h.delayNextStep(l);let c=h.currentTime;O(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=n.currentTime-c+(n.startTime-s.currentTimeline.startTime)}},Se={},Ue=class i{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=Se;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,s,n,r,a,o,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=n,this._leaveClassName=r,this.errors=a,this.timelines=o,this.currentTimeline=l||new Ee(this._driver,t,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let s=e,n=this.options;s.duration!=null&&(n.duration=$(s.duration)),s.delay!=null&&(n.delay=$(s.delay));let r=s.params;if(r){let a=n.params;a||(a=this.options.params={}),Object.keys(r).forEach(o=>{(!t||!a.hasOwnProperty(o))&&(a[o]=ie(r[o],a,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let s=e.params={};Object.keys(t).forEach(n=>{s[n]=t[n]})}}return e}createSubContext(e=null,t,s){let n=t||this.element,r=new i(this._driver,n,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(n,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=Se,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){let n={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new je(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,n,e.stretchStartingKeyframe);return this.timelines.push(r),n}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,n,r,a){let o=[];if(n&&o.push(this.element),e.length>0){e=e.replace(Hs,"."+this._enterClassName),e=e.replace(Xs,"."+this._leaveClassName);let l=s!=1,u=this._driver.query(this.element,e,l);s!==0&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),o.push(...u)}return!r&&o.length==0&&a.push(os(t)),o}},Ee=class i{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,s,n){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=n,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new i(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Ws,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||B),this._currentKeyframe.set(t,B);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,n){t&&this._previousKeyframe.set("easing",t);let r=n&&n.params||{},a=Zs(e,this._globalTimelineStyles);for(let[o,l]of a){let u=ie(l,r,s);this._pendingStyles.set(o,u),this._localTimelineStyles.has(o)||this._backFill.set(o,this._globalTimelineStyles.get(o)??B),this._updateStyle(o,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{let n=this._styleSummary.get(s);(!n||t.time>n.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,s=this._keyframes.size===1&&this.duration===0,n=[];this._keyframes.forEach((o,l)=>{let u=new Map([...this._backFill,...o]);u.forEach((h,c)=>{h===ue?e.add(c):h===B&&t.add(c)}),s||u.set("offset",l/this.duration),n.push(u)});let r=[...e.values()],a=[...t.values()];if(s){let o=n[0],l=new Map(o);o.set("offset",0),l.set("offset",1),n=[o,l]}return at(this.element,n,r,a,this.duration,this.startTime,this.easing,!1)}},je=class extends Ee{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,s,n,r,a,o=!1){super(e,t,a.delay),this.keyframes=s,this.preStyleProps=n,this.postStyleProps=r,this._stretchStartingKeyframe=o,this.timings={duration:a.duration,delay:a.delay,easing:a.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:n}=this.timings;if(this._stretchStartingKeyframe&&t){let r=[],a=s+t,o=t/a,l=new Map(e[0]);l.set("offset",0),r.push(l);let u=new Map(e[0]);u.set("offset",wt(o)),r.push(u);let h=e.length-1;for(let c=1;c<=h;c++){let _=new Map(e[c]),y=_.get("offset"),d=t+y*s;_.set("offset",wt(d/a)),r.push(_)}s=a,t=0,n="",e=r}return at(this.element,e,this.preStyleProps,this.postStyleProps,s,t,n,!0)}};function wt(i,e=3){let t=Math.pow(10,e-1);return Math.round(i*t)/t}function Zs(i,e){let t=new Map,s;return i.forEach(n=>{if(n==="*"){s??=e.keys();for(let r of s)t.set(r,B)}else for(let[r,a]of n)t.set(r,a)}),t}function bt(i,e,t,s,n,r,a,o,l,u,h,c,_){return{type:0,element:i,triggerName:e,isRemovalTransition:n,fromState:t,fromStyles:r,toState:s,toStyles:a,timelines:o,queriedElements:l,preStyleProps:u,postStyleProps:h,totalTime:c,errors:_}}var De={},Te=class{_triggerName;ast;_stateStyles;constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,n){return Js(this.ast.matchers,e,t,s,n)}buildStyles(e,t,s){let n=this._stateStyles.get("*");return e!==void 0&&(n=this._stateStyles.get(e?.toString())||n),n?n.buildStyles(t,s):new Map}build(e,t,s,n,r,a,o,l,u,h){let c=[],_=this.ast.options&&this.ast.options.params||De,y=o&&o.params||De,d=this.buildStyles(s,y,c),g=l&&l.params||De,v=this.buildStyles(n,g,c),b=new Set,A=new Map,C=new Map,N=n==="void",Z={params:Bt(g,_),delay:this.ast.options?.delay},K=h?[]:ot(e,t,this.ast.animation,r,a,d,v,Z,u,c),k=0;return K.forEach(D=>{k=Math.max(D.duration+D.delay,k)}),c.length?bt(t,this._triggerName,s,n,N,d,v,[],[],A,C,k,c):(K.forEach(D=>{let j=D.element,J=L(A,j,new Set);D.preStyleProps.forEach(W=>J.add(W));let lt=L(C,j,new Set);D.postStyleProps.forEach(W=>lt.add(W)),j!==t&&b.add(j)}),bt(t,this._triggerName,s,n,N,d,v,K,[...b.values()],A,C,k))}};function Js(i,e,t,s,n){return i.some(r=>r(e,t,s,n))}function Bt(i,e){let t=le({},e);return Object.entries(i).forEach(([s,n])=>{n!=null&&(t[s]=n)}),t}var We=class{styles;defaultParams;normalizer;constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){let s=new Map,n=Bt(e,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((a,o)=>{a&&(a=ie(a,n,t));let l=this.normalizer.normalizePropertyName(o,t);a=this.normalizer.normalizeStyleValue(o,l,a,t),s.set(o,a)})}),s}};function xs(i,e,t){return new Ge(i,e,t)}var Ge=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,t.states.forEach(n=>{let r=n.options&&n.options.params||{};this.states.set(n.name,new We(n.style,r,s))}),At(this.states,"true","1"),At(this.states,"false","0"),t.transitions.forEach(n=>{this.transitionFactories.push(new Te(e,n,this.states))}),this.fallbackTransition=en(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,n){return this.transitionFactories.find(a=>a.match(e,t,s,n))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}};function en(i,e,t){let s=[(a,o)=>!0],n={type:E.Sequence,steps:[],options:null},r={type:E.Transition,animation:n,matchers:s,options:null,queryCount:0,depCount:0};return new Te(i,r,e)}function At(i,e,t){i.has(e)?i.has(t)||i.set(t,i.get(e)):i.has(t)&&i.set(e,i.get(t))}var tn=new te,He=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s}register(e,t){let s=[],n=[],r=rt(this._driver,t,s,n);if(s.length)throw ms(s);this._animations.set(e,r)}_buildPlayer(e,t,s){let n=e.element,r=Ot(this._normalizer,e.keyframes,t,s);return this._driver.animate(n,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){let n=[],r=this._animations.get(e),a,o=new Map;if(r?(a=ot(this._driver,t,r,st,pe,new Map,new Map,s,tn,n),a.forEach(h=>{let c=L(o,h.element,new Map);h.postStyleProps.forEach(_=>c.set(_,null))})):(n.push(ps()),a=[]),n.length)throw gs(n);o.forEach((h,c)=>{h.forEach((_,y)=>{h.set(y,this._driver.computeStyle(c,y,B))})});let l=a.map(h=>{let c=o.get(h.element);return this._buildPlayer(h,new Map,c)}),u=U(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw ys(e);return t}listen(e,t,s,n){let r=et(t,"","","");return xe(this._getPlayer(e),s,r,n),()=>{}}command(e,t,s,n){if(s=="register"){this.register(e,n[0]);return}if(s=="create"){let a=n[0]||{};this.create(e,t,a);return}let r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(n[0]));break;case"destroy":this.destroy(e);break}}},Pt="ng-animate-queued",sn=".ng-animate-queued",Re="ng-animate-disabled",nn=".ng-animate-disabled",rn="ng-star-inserted",an=".ng-star-inserted",on=[],Qt={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},ln={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},z="__ng_removed",re=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let s=e&&e.hasOwnProperty("value"),n=s?e.value:e;if(this.value=cn(n),s){let r=e,{value:a}=r,o=ft(r,["value"]);this.options=o}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let s=this.options.params;Object.keys(t).forEach(n=>{s[n]==null&&(s[n]=t[n])})}}},ne="void",Oe=new re(ne),Ye=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this._hostClassName="ng-tns-"+e,I(t,this._hostClassName)}listen(e,t,s,n){if(!this._triggers.has(t))throw _s(s,t);if(s==null||s.length==0)throw Ss(t);if(!hn(s))throw Es(s,t);let r=L(this._elementListeners,e,[]),a={name:t,phase:s,callback:n};r.push(a);let o=L(this._engine.statesByElement,e,new Map);return o.has(t)||(I(e,ce),I(e,ce+"-"+t),o.set(t,Oe)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(a);l>=0&&r.splice(l,1),this._triggers.has(t)||o.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw Ts(e);return t}trigger(e,t,s,n=!0){let r=this._getTrigger(t),a=new ae(this.id,t,e),o=this._engine.statesByElement.get(e);o||(I(e,ce),I(e,ce+"-"+t),this._engine.statesByElement.set(e,o=new Map));let l=o.get(t),u=new re(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),o.set(t,u),l||(l=Oe),!(u.value===ne)&&l.value===u.value){if(!mn(l.params,u.params)){let g=[],v=r.matchStyles(l.value,l.params,g),b=r.matchStyles(u.value,u.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{X(e,v),Q(e,b)})}return}let _=L(this._engine.playersByElement,e,[]);_.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=r.matchTransition(l.value,u.value,e,u.params),d=!1;if(!y){if(!n)return;y=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:u,player:a,isFallbackTransition:d}),d||(I(e,Pt),a.onStart(()=>{ee(e,Pt)})),a.onDone(()=>{let g=this.players.indexOf(a);g>=0&&this.players.splice(g,1);let v=this._engine.playersByElement.get(e);if(v){let b=v.indexOf(a);b>=0&&v.splice(b,1)}}),this.players.push(a),_.push(a),a}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(n=>n.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let s=this._engine.driver.query(e,ge,!0);s.forEach(n=>{if(n[z])return;let r=this._engine.fetchNamespacesByElement(n);r.size?r.forEach(a=>a.triggerLeaveAnimation(n,t,!1,!0)):this.clearElementCache(n)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(n=>this.clearElementCache(n)))}triggerLeaveAnimation(e,t,s,n){let r=this._engine.statesByElement.get(e),a=new Map;if(r){let o=[];if(r.forEach((l,u)=>{if(a.set(u,l.value),this._triggers.has(u)){let h=this.trigger(e,u,ne,n);h&&o.push(h)}}),o.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,a),s&&U(o).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){let n=new Set;t.forEach(r=>{let a=r.name;if(n.has(a))return;n.add(a);let l=this._triggers.get(a).fallbackTransition,u=s.get(a)||Oe,h=new re(ne),c=new ae(this.id,a,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:a,transition:l,fromState:u,toState:h,player:c,isFallbackTransition:!0})})}}removeNode(e,t){let s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let n=!1;if(s.totalAnimations){let r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)n=!0;else{let a=e;for(;a=a.parentNode;)if(s.statesByElement.get(a)){n=!0;break}}}if(this.prepareLeaveAnimationListeners(e),n)s.markElementAsRemoved(this.id,e,!1,t);else{let r=e[z];(!r||r===Qt)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){I(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(s=>{let n=s.player;if(n.destroyed)return;let r=s.element,a=this._elementListeners.get(r);a&&a.forEach(o=>{if(o.name==s.triggerName){let l=et(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,xe(s.player,o.phase,l,o.callback)}}),n.markedForDestroy?this._engine.afterFlush(()=>{n.destroy()}):t.push(s)}),this._queue=[],t.sort((s,n)=>{let r=s.transition.ast.depCount,a=n.transition.ast.depCount;return r==0||a==0?r-a:this._engine.driver.containsElement(s.element,n.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},Xe=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s){this.bodyNode=e,this.driver=t,this._normalizer=s}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){let s=new Ye(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){let s=this._namespaceList,n=this.namespacesByHostElement;if(s.length-1>=0){let a=!1,o=this.driver.getParentElement(t);for(;o;){let l=n.get(o);if(l){let u=s.indexOf(l);s.splice(u+1,0,e),a=!0;break}o=this.driver.getParentElement(o)}a||s.unshift(e)}else s.push(e);return n.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let n=this._namespaceLookup[e];n&&n.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);let n=this._namespaceList.indexOf(s);n>=0&&this._namespaceList.splice(n,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,s=this.statesByElement.get(e);if(s){for(let n of s.values())if(n.namespaceId){let r=this._fetchNamespace(n.namespaceId);r&&t.add(r)}}return t}trigger(e,t,s,n){if(de(t)){let r=this._fetchNamespace(e);if(r)return r.trigger(t,s,n),!0}return!1}insertNode(e,t,s,n){if(!de(t))return;let r=t[z];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let a=this.collectedLeaveElements.indexOf(t);a>=0&&this.collectedLeaveElements.splice(a,1)}if(e){let a=this._fetchNamespace(e);a&&a.insertNode(t,s)}n&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),I(e,Re)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),ee(e,Re))}removeNode(e,t,s){if(de(t)){let n=e?this._fetchNamespace(e):null;n?n.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);let r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,n,r){this.collectedLeaveElements.push(t),t[z]={namespaceId:e,setForRemoval:n,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,n,r){return de(t)?this._fetchNamespace(e).listen(t,s,n,r):()=>{}}_buildInstruction(e,t,s,n,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,n,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,ge,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,ze,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return U(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[z];if(t&&t.setForRemoval){if(e[z]=Qt,t.namespaceId){this.destroyInnerAnimations(e);let s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Re)&&this.markElementAsDisabled(e,!1),this.driver.query(e,nn,!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,n)=>this._balanceNamespaceList(s,n)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++){let n=this.collectedEnterElements[s];I(n,rn)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let s=[];try{t=this._flushAnimations(s,e)}finally{for(let n=0;n<s.length;n++)s[n]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++){let n=this.collectedLeaveElements[s];this.processLeaveNode(n)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){let s=this._whenQuietFns;this._whenQuietFns=[],t.length?U(t).onDone(()=>{s.forEach(n=>n())}):s.forEach(n=>n())}}reportError(e){throw vs(e)}_flushAnimations(e,t){let s=new te,n=[],r=new Map,a=[],o=new Map,l=new Map,u=new Map,h=new Set;this.disabledNodes.forEach(f=>{h.add(f);let m=this.driver.query(f,sn,!0);for(let p=0;p<m.length;p++)h.add(m[p])});let c=this.bodyNode,_=Array.from(this.statesByElement.keys()),y=Ct(_,this.collectedEnterElements),d=new Map,g=0;y.forEach((f,m)=>{let p=st+g++;d.set(m,p),f.forEach(T=>I(T,p))});let v=[],b=new Set,A=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){let m=this.collectedLeaveElements[f],p=m[z];p&&p.setForRemoval&&(v.push(m),b.add(m),p.hasAnimation?this.driver.query(m,an,!0).forEach(T=>b.add(T)):A.add(m))}let C=new Map,N=Ct(_,Array.from(b));N.forEach((f,m)=>{let p=pe+g++;C.set(m,p),f.forEach(T=>I(T,p))}),e.push(()=>{y.forEach((f,m)=>{let p=d.get(m);f.forEach(T=>ee(T,p))}),N.forEach((f,m)=>{let p=C.get(m);f.forEach(T=>ee(T,p))}),v.forEach(f=>{this.processLeaveNode(f)})});let Z=[],K=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(p=>{let T=p.player,P=p.element;if(Z.push(T),this.collectedEnterElements.length){let M=P[z];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(p.triggerName)){let G=M.previousTriggersValues.get(p.triggerName),F=this.statesByElement.get(p.element);if(F&&F.has(p.triggerName)){let oe=F.get(p.triggerName);oe.value=G,F.set(p.triggerName,oe)}}T.destroy();return}}let q=!c||!this.driver.containsElement(c,P),R=C.get(P),V=d.get(P),w=this._buildInstruction(p,s,V,R,q);if(w.errors&&w.errors.length){K.push(w);return}if(q){T.onStart(()=>X(P,w.fromStyles)),T.onDestroy(()=>Q(P,w.toStyles)),n.push(T);return}if(p.isFallbackTransition){T.onStart(()=>X(P,w.fromStyles)),T.onDestroy(()=>Q(P,w.toStyles)),n.push(T);return}let ht=[];w.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||ht.push(M)}),w.timelines=ht,s.append(P,w.timelines);let Ut={instruction:w,player:T,element:P};a.push(Ut),w.queriedElements.forEach(M=>L(o,M,[]).push(T)),w.preStyleProps.forEach((M,G)=>{if(M.size){let F=l.get(G);F||l.set(G,F=new Set),M.forEach((oe,Pe)=>F.add(Pe))}}),w.postStyleProps.forEach((M,G)=>{let F=u.get(G);F||u.set(G,F=new Set),M.forEach((oe,Pe)=>F.add(Pe))})});if(K.length){let f=[];K.forEach(m=>{f.push(ws(m.triggerName,m.errors))}),Z.forEach(m=>m.destroy()),this.reportError(f)}let k=new Map,D=new Map;a.forEach(f=>{let m=f.element;s.has(m)&&(D.set(m,m),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,k))}),n.forEach(f=>{let m=f.element;this._getPreviousPlayers(m,!1,f.namespaceId,f.triggerName,null).forEach(T=>{L(k,m,[]).push(T),T.destroy()})});let j=v.filter(f=>kt(f,l,u)),J=new Map;Mt(J,this.driver,A,u,B).forEach(f=>{kt(f,l,u)&&j.push(f)});let W=new Map;y.forEach((f,m)=>{Mt(W,this.driver,new Set(f),l,ue)}),j.forEach(f=>{let m=J.get(f),p=W.get(f);J.set(f,new Map([...m?.entries()??[],...p?.entries()??[]]))});let Ae=[],ut=[],ct={};a.forEach(f=>{let{element:m,player:p,instruction:T}=f;if(s.has(m)){if(h.has(m)){p.onDestroy(()=>Q(m,T.toStyles)),p.disabled=!0,p.overrideTotalTime(T.totalTime),n.push(p);return}let P=ct;if(D.size>1){let R=m,V=[];for(;R=R.parentNode;){let w=D.get(R);if(w){P=w;break}V.push(R)}V.forEach(w=>D.set(w,P))}let q=this._buildAnimation(p.namespaceId,T,k,r,W,J);if(p.setRealPlayer(q),P===ct)Ae.push(p);else{let R=this.playersByElement.get(P);R&&R.length&&(p.parentPlayer=U(R)),n.push(p)}}else X(m,T.fromStyles),p.onDestroy(()=>Q(m,T.toStyles)),ut.push(p),h.has(m)&&n.push(p)}),ut.forEach(f=>{let m=r.get(f.element);if(m&&m.length){let p=U(m);f.setRealPlayer(p)}}),n.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<v.length;f++){let m=v[f],p=m[z];if(ee(m,pe),p&&p.hasAnimation)continue;let T=[];if(o.size){let q=o.get(m);q&&q.length&&T.push(...q);let R=this.driver.query(m,ze,!0);for(let V=0;V<R.length;V++){let w=o.get(R[V]);w&&w.length&&T.push(...w)}}let P=T.filter(q=>!q.destroyed);P.length?fn(this,m,P):this.processLeaveNode(m)}return v.length=0,Ae.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();let m=this.players.indexOf(f);this.players.splice(m,1)}),f.play()}),Ae}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,n,r){let a=[];if(t){let o=this.playersByQueriedElement.get(e);o&&(a=o)}else{let o=this.playersByElement.get(e);if(o){let l=!r||r==ne;o.forEach(u=>{u.queued||!l&&u.triggerName!=n||a.push(u)})}}return(s||n)&&(a=a.filter(o=>!(s&&s!=o.namespaceId||n&&n!=o.triggerName))),a}_beforeAnimationBuild(e,t,s){let n=t.triggerName,r=t.element,a=t.isRemovalTransition?void 0:e,o=t.isRemovalTransition?void 0:n;for(let l of t.timelines){let u=l.element,h=u!==r,c=L(s,u,[]);this._getPreviousPlayers(u,h,a,o,t.toState).forEach(y=>{let d=y.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),y.destroy(),c.push(y)})}X(r,t.fromStyles)}_buildAnimation(e,t,s,n,r,a){let o=t.triggerName,l=t.element,u=[],h=new Set,c=new Set,_=t.timelines.map(d=>{let g=d.element;h.add(g);let v=g[z];if(v&&v.removedBeforeQueried)return new x(d.duration,d.delay);let b=g!==l,A=dn((s.get(g)||on).map(k=>k.getRealPlayer())).filter(k=>{let D=k;return D.element?D.element===g:!1}),C=r.get(g),N=a.get(g),Z=Ot(this._normalizer,d.keyframes,C,N),K=this._buildPlayer(d,Z,A);if(d.subTimeline&&n&&c.add(g),b){let k=new ae(e,o,g);k.setRealPlayer(K),u.push(k)}return K});u.forEach(d=>{L(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>un(this.playersByQueriedElement,d.element,d))}),h.forEach(d=>I(d,St));let y=U(_);return y.onDestroy(()=>{h.forEach(d=>ee(d,St)),Q(l,t.toStyles)}),c.forEach(d=>{L(n,d,[]).push(y)}),y}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new x(e.duration,e.delay)}},ae=class{namespaceId;triggerName;element;_player=new x;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(n=>xe(e,s,void 0,n))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){L(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function un(i,e,t){let s=i.get(e);if(s){if(s.length){let n=s.indexOf(t);s.splice(n,1)}s.length==0&&i.delete(e)}return s}function cn(i){return i??null}function de(i){return i&&i.nodeType===1}function hn(i){return i=="start"||i=="done"}function Nt(i,e){let t=i.style.display;return i.style.display=e??"none",t}function Mt(i,e,t,s,n){let r=[];t.forEach(l=>r.push(Nt(l)));let a=[];s.forEach((l,u)=>{let h=new Map;l.forEach(c=>{let _=e.computeStyle(u,c,n);h.set(c,_),(!_||_.length==0)&&(u[z]=ln,a.push(u))}),i.set(u,h)});let o=0;return t.forEach(l=>Nt(l,r[o++])),a}function Ct(i,e){let t=new Map;if(i.forEach(o=>t.set(o,[])),e.length==0)return t;let s=1,n=new Set(e),r=new Map;function a(o){if(!o)return s;let l=r.get(o);if(l)return l;let u=o.parentNode;return t.has(u)?l=u:n.has(u)?l=s:l=a(u),r.set(o,l),l}return e.forEach(o=>{let l=a(o);l!==s&&t.get(l).push(o)}),t}function I(i,e){i.classList?.add(e)}function ee(i,e){i.classList?.remove(e)}function fn(i,e,t){U(t).onDone(()=>i.processLeaveNode(e))}function dn(i){let e=[];return $t(i,e),e}function $t(i,e){for(let t=0;t<i.length;t++){let s=i[t];s instanceof Me?$t(s.players,e):e.push(s)}}function mn(i,e){let t=Object.keys(i),s=Object.keys(e);if(t.length!=s.length)return!1;for(let n=0;n<t.length;n++){let r=t[n];if(!e.hasOwnProperty(r)||i[r]!==e[r])return!1}return!0}function kt(i,e,t){let s=t.get(i);if(!s)return!1;let n=e.get(i);return n?s.forEach(r=>n.add(r)):e.set(i,s),t.delete(i),!0}var ve=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,s){this._driver=t,this._normalizer=s,this._transitionEngine=new Xe(e.body,t,s),this._timelineEngine=new He(e.body,t,s),this._transitionEngine.onRemovalComplete=(n,r)=>this.onRemovalComplete(n,r)}registerTrigger(e,t,s,n,r){let a=e+"-"+n,o=this._triggerCache[a];if(!o){let l=[],u=[],h=rt(this._driver,r,l,u);if(l.length)throw fs(n,l);o=xs(n,h,this._normalizer),this._triggerCache[a]=o}this._transitionEngine.registerTrigger(t,n,o)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,n){this._transitionEngine.insertNode(e,t,s,n)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,n){if(s.charAt(0)=="@"){let[r,a]=gt(s),o=n;this._timelineEngine.command(r,t,a,o)}else this._transitionEngine.trigger(e,t,s,n)}listen(e,t,s,n,r){if(s.charAt(0)=="@"){let[a,o]=gt(s);return this._timelineEngine.listen(a,t,o,r)}return this._transitionEngine.listen(e,t,s,n,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function pn(i,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=Le(e[0]),e.length>1&&(s=Le(e[e.length-1]))):e instanceof Map&&(t=Le(e)),t||s?new gn(i,t,s):null}var gn=(()=>{class i{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,s,n){this._element=t,this._startStyles=s,this._endStyles=n;let r=i.initialStylesByElement.get(t);r||i.initialStylesByElement.set(t,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&Q(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Q(this._element,this._initialStyles),this._endStyles&&(Q(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(i.initialStylesByElement.delete(this._element),this._startStyles&&(X(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(X(this._element,this._endStyles),this._endStyles=null),Q(this._element,this._initialStyles),this._state=3)}}return i})();function Le(i){let e=null;return i.forEach((t,s)=>{yn(s)&&(e=e||new Map,e.set(s,t))}),e}function yn(i){return i==="display"||i==="position"}var we=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,s,n){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=n,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,n)=>{n!=="offset"&&e.set(n,this._finished?s:it(this.element,n))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},Ze=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return Lt(e,t)}getParentElement(e){return tt(e)}query(e,t,s){return Ft(e,t,s)}computeStyle(e,t,s){return it(e,t)}animate(e,t,s,n,r,a=[]){let o=n==0?"both":"forwards",l={duration:s,delay:n,fill:o};r&&(l.easing=r);let u=new Map,h=a.filter(y=>y instanceof we);Fs(s,n)&&h.forEach(y=>{y.currentSnapshot.forEach((d,g)=>u.set(g,d))});let c=Rs(t).map(y=>new Map(y));c=Is(e,c,u);let _=pn(e,c);return new we(e,c,l,_)}};function Pn(i,e){return i==="noop"?new ve(e,new It,new Ie):new ve(e,new Ze,new Be)}var Dt=class{_driver;_animationAst;constructor(e,t){this._driver=e;let s=[],r=rt(e,t,s,[]);if(s.length)throw cs(s);this._animationAst=r}buildTimelines(e,t,s,n,r){let a=Array.isArray(t)?Et(t):t,o=Array.isArray(s)?Et(s):s,l=[];r=r||new te;let u=ot(this._driver,e,this._animationAst,st,pe,a,o,n,r,l);if(l.length)throw hs(l);return u}},me="@",Vt="@.disabled",be=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,s,n){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=n}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,n=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,n)}removeChild(e,t,s){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,n){this.delegate.setAttribute(e,t,s,n)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,n){this.delegate.setStyle(e,t,s,n)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){t.charAt(0)==me&&t==Vt?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s,n){return this.delegate.listen(e,t,s,n)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},Je=class extends be{factory;constructor(e,t,s,n,r){super(t,s,n,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){t.charAt(0)==me?t.charAt(1)=="."&&t==Vt?(s=s===void 0?!0:!!s,this.disableAnimations(e,s)):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s,n){if(t.charAt(0)==me){let r=_n(e),a=t.slice(1),o="";return a.charAt(0)!=me&&([a,o]=Sn(a)),this.engine.listen(this.namespaceId,r,a,o,l=>{let u=l._data||-1;this.factory.scheduleListenerCallback(u,s,l)})}return this.delegate.listen(e,t,s,n)}};function _n(i){switch(i){case"body":return document.body;case"document":return document;case"window":return window;default:return i}}function Sn(i){let e=i.indexOf("."),t=i.substring(0,e),s=i.slice(e+1);return[t,s]}var Rt=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,t.onRemovalComplete=(n,r)=>{r?.removeChild(null,n)}}createRenderer(e,t){let s="",n=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let u=this._rendererCache,h=u.get(n);if(!h){let c=()=>u.delete(n);h=new be(s,n,this.engine,c),u.set(n,h)}return h}let r=t.id,a=t.id+"-"+this._currentId;this._currentId++,this.engine.register(a,e);let o=u=>{Array.isArray(u)?u.forEach(o):this.engine.registerTrigger(r,a,e,u.name,u)};return t.data.animation.forEach(o),new Je(this,a,n,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(s));return}let n=this._animationCallbacksBuffer;n.length==0&&queueMicrotask(()=>{this._zone.run(()=>{n.forEach(r=>{let[a,o]=r;a(o)}),this._animationCallbacksBuffer=[]})}),n.push([t,s])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};export{_t as AnimationDriver,It as NoopAnimationDriver,Dt as \u0275Animation,ve as \u0275AnimationEngine,Je as \u0275AnimationRenderer,Rt as \u0275AnimationRendererFactory,Fe as \u0275AnimationStyleNormalizer,be as \u0275BaseAnimationRenderer,Ie as \u0275NoopAnimationStyleNormalizer,Ze as \u0275WebAnimationsDriver,we as \u0275WebAnimationsPlayer,Be as \u0275WebAnimationsStyleNormalizer,Fs as \u0275allowPreviousPlayerStylesMerge,An as \u0275camelCaseToDashCase,Lt as \u0275containsElement,Pn as \u0275createEngine,tt as \u0275getParentElement,Ft as \u0275invokeQuery,Rs as \u0275normalizeKeyframes,Ns as \u0275validateStyleProperty,bn as \u0275validateWebAnimatableStyleProperty};
