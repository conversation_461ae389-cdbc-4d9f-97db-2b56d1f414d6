import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class PlatformService {
  private _isBrowser: boolean;
  private _isServer: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this._isBrowser = isPlatformBrowser(this.platformId);
    this._isServer = isPlatformServer(this.platformId);

    if (this._isServer) {
      console.log('[PlatformService] Running on server-side');
    } else {
      console.log('[PlatformService] Running on client-side');
    }
  }

  get isBrowser(): boolean {
    return this._isBrowser;
  }

  get isServer(): boolean {
    return this._isServer;
  }

  // Safe browser API access methods
  safeLocalStorage(): Storage | null {
    if (this.isBrowser && typeof localStorage !== 'undefined') {
      return localStorage;
    }
    console.warn('[PlatformService] localStorage not available on server');
    return null;
  }

  safeSessionStorage(): Storage | null {
    if (this.isBrowser && typeof sessionStorage !== 'undefined') {
      return sessionStorage;
    }
    console.warn('[PlatformService] sessionStorage not available on server');
    return null;
  }

  safeWindow(): Window | null {
    if (this.isBrowser && typeof window !== 'undefined') {
      return window;
    }
    console.warn('[PlatformService] window not available on server');
    return null;
  }

  safeDocument(): Document | null {
    if (this.isBrowser && typeof document !== 'undefined') {
      return document;
    }
    console.warn('[PlatformService] document not available on server');
    return null;
  }

  safeLocation(): Location | null {
    if (this.isBrowser && typeof location !== 'undefined') {
      return location;
    }
    console.warn('[PlatformService] location not available on server');
    return null;
  }

  // Utility methods for common operations
  safeNavigate(url: string): void {
    const window = this.safeWindow();
    if (window) {
      window.location.href = url;
    } else {
      console.log(`[PlatformService] Would navigate to: ${url} (server-side)`);
    }
  }

  safeReload(): void {
    const window = this.safeWindow();
    if (window) {
      window.location.reload();
    } else {
      console.log('[PlatformService] Would reload page (server-side)');
    }
  }

  getUserAgent(): string {
    const window = this.safeWindow();
    return window?.navigator?.userAgent || 'Server-Side-Rendering';
  }

  // Legacy methods for backward compatibility
  getLocalStorage(): Storage | null {
    return this.safeLocalStorage();
  }

  getSessionStorage(): Storage | null {
    return this.safeSessionStorage();
  }

  getWindow(): Window | null {
    return this.safeWindow();
  }

  getDocument(): Document | null {
    return this.safeDocument();
  }

  getLocation(): Location | null {
    return this.safeLocation();
  }
}
