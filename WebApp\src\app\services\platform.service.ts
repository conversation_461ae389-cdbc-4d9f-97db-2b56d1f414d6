import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class PlatformService {
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  get isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  get isServer(): boolean {
    return !this.isBrowser;
  }

  getLocalStorage(): Storage | null {
    if (this.isBrowser) {
      return localStorage;
    }
    return null;
  }

  getSessionStorage(): Storage | null {
    if (this.isBrowser) {
      return sessionStorage;
    }
    return null;
  }

  getWindow(): Window | null {
    if (this.isBrowser) {
      return window;
    }
    return null;
  }

  getDocument(): Document | null {
    if (this.isBrowser) {
      return document;
    }
    return null;
  }

  getLocation(): Location | null {
    if (this.isBrowser) {
      return location;
    }
    return null;
  }
}
