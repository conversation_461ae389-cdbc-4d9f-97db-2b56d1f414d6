# xhr2 Contribution Policy

This is an open-source library and welcomes outside contributions.

Please file bugs on
[the GitHub issue page](https://github.com/pwnall/xhr2/issues).

Please submit patches as
[GitHub pull requests](https://help.github.com/articles/using-pull-requests).
Please check the
[existing pull requests](https://github.com/pwnall/xhr2/issues) to avoid
duplicating effort.


## Pull Request Checklist

* Do not modify the version in `package.json` or the commit history. Feel free
  to rebase your commits while the pull request is in progress.
* If your patch adds new functionality, please make sure to include link to the
  relevant parts of the
  [W3C XMLHttpRequest specification](http://www.w3.org/TR/XMLHttpRequest/). Use
  the same style as existing source code.
* Include tests whenever possible, so the functionality won't be broken by
  accident in future releases.


## Obligatory Legalese

By submitting a contribution to the library, you grant <PERSON>
(the library's author) a non-exclusive, irrevocable, perpetual, transferable,
license to use, reproduce, modify, adapt, publish, translate, create derivative
works from, distribute, perform and display your contribution (in whole or
part) worldwide under the MIT license.
