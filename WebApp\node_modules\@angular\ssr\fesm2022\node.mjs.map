{"version": 3, "file": "node.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/errors.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/common-engine/inline-css-processor.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/common-engine/peformance-profiler.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/common-engine/common-engine.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/request.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/app-engine.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/handler.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/response.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/node/src/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Attaches listeners to the Node.js process to capture and handle unhandled rejections and uncaught exceptions.\n * Captured errors are logged to the console. This function logs errors to the console, preventing unhandled errors\n * from crashing the server. It is particularly useful for Zoneless apps, ensuring error handling without relying on Zone.js.\n *\n * @remarks\n * This function is a no-op if zone.js is available.\n * For Zone-based apps, similar functionality is provided by Zone.js itself. See the Zone.js implementation here:\n * https://github.com/angular/angular/blob/4a8d0b79001ec09bcd6f2d6b15117aa6aac1932c/packages/zone.js/lib/node/node.ts#L94%7C\n *\n * @internal\n */\nexport function attachNodeGlobalErrorHandlers(): void {\n  if (typeof Zone !== 'undefined') {\n    return;\n  }\n\n  // Ensure that the listeners are registered only once.\n  // Otherwise, multiple instances may be registered during edit/refresh.\n  const gThis: typeof globalThis & { ngAttachNodeGlobalErrorHandlersCalled?: boolean } = globalThis;\n  if (gThis.ngAttachNodeGlobalErrorHandlersCalled) {\n    return;\n  }\n\n  gThis.ngAttachNodeGlobalErrorHandlersCalled = true;\n\n  process\n    // eslint-disable-next-line no-console\n    .on('unhandledRejection', (error) => console.error('unhandledRejection', error))\n    // eslint-disable-next-line no-console\n    .on('uncaughtException', (error) => console.error('uncaughtException', error));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { ɵInlineCriticalCssProcessor as InlineCriticalCssProcessor } from '@angular/ssr';\nimport { readFile } from 'node:fs/promises';\n\nexport class CommonEngineInlineCriticalCssProcessor {\n  private readonly resourceCache = new Map<string, string>();\n\n  async process(html: string, outputPath: string | undefined): Promise<string> {\n    const beasties = new InlineCriticalCssProcessor(async (path) => {\n      let resourceContent = this.resourceCache.get(path);\n      if (resourceContent === undefined) {\n        resourceContent = await readFile(path, 'utf-8');\n        this.resourceCache.set(path, resourceContent);\n      }\n\n      return resourceContent;\n    }, outputPath);\n\n    return beasties.process(html);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nconst PERFORMANCE_MARK_PREFIX = '🅰️';\n\nexport function printPerformanceLogs(): void {\n  let maxWordLength = 0;\n  const benchmarks: [step: string, value: string][] = [];\n\n  for (const { name, duration } of performance.getEntriesByType('measure')) {\n    if (!name.startsWith(PERFORMANCE_MARK_PREFIX)) {\n      continue;\n    }\n\n    // `🅰️:Retrieve SSG Page` -> `Retrieve SSG Page:`\n    const step = name.slice(PERFORMANCE_MARK_PREFIX.length + 1) + ':';\n    if (step.length > maxWordLength) {\n      maxWordLength = step.length;\n    }\n\n    benchmarks.push([step, `${duration.toFixed(1)}ms`]);\n    performance.clearMeasures(name);\n  }\n\n  /* eslint-disable no-console */\n  console.log('********** Performance results **********');\n  for (const [step, value] of benchmarks) {\n    const spaces = maxWordLength - step.length + 5;\n    console.log(step + ' '.repeat(spaces) + value);\n  }\n  console.log('*****************************************');\n  /* eslint-enable no-console */\n}\n\nexport async function runMethodAndMeasurePerf<T>(\n  label: string,\n  asyncMethod: () => Promise<T>,\n): Promise<T> {\n  const labelName = `${PERFORMANCE_MARK_PREFIX}:${label}`;\n  const startLabel = `start:${labelName}`;\n  const endLabel = `end:${labelName}`;\n\n  try {\n    performance.mark(startLabel);\n\n    return await asyncMethod();\n  } finally {\n    performance.mark(endLabel);\n    performance.measure(labelName, startLabel, endLabel);\n    performance.clearMarks(startLabel);\n    performance.clearMarks(endLabel);\n  }\n}\n\nexport function noopRunMethodAndMeasurePerf<T>(\n  label: string,\n  asyncMethod: () => Promise<T>,\n): Promise<T> {\n  return asyncMethod();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { ApplicationRef, StaticProvider, Type } from '@angular/core';\nimport { renderApplication, renderModule, ɵSERVER_CONTEXT } from '@angular/platform-server';\nimport * as fs from 'node:fs';\nimport { dirname, join, normalize, resolve } from 'node:path';\nimport { URL } from 'node:url';\nimport { attachNodeGlobalErrorHandlers } from '../errors';\nimport { CommonEngineInlineCriticalCssProcessor } from './inline-css-processor';\nimport {\n  noopRunMethodAndMeasurePerf,\n  printPerformanceLogs,\n  runMethodAndMeasurePerf,\n} from './peformance-profiler';\n\nconst SSG_MARKER_REGEXP = /ng-server-context=[\"']\\w*\\|?ssg\\|?\\w*[\"']/;\n\nexport interface CommonEngineOptions {\n  /** A method that when invoked returns a promise that returns an `ApplicationRef` instance once resolved or an NgModule. */\n  bootstrap?: Type<{}> | (() => Promise<ApplicationRef>);\n\n  /** A set of platform level providers for all requests. */\n  providers?: StaticProvider[];\n\n  /** Enable request performance profiling data collection and printing the results in the server console. */\n  enablePerformanceProfiler?: boolean;\n}\n\nexport interface CommonEngineRenderOptions {\n  /** A method that when invoked returns a promise that returns an `ApplicationRef` instance once resolved or an NgModule. */\n  bootstrap?: Type<{}> | (() => Promise<ApplicationRef>);\n\n  /** A set of platform level providers for the current request. */\n  providers?: StaticProvider[];\n  url?: string;\n  document?: string;\n  documentFilePath?: string;\n\n  /**\n   * Reduce render blocking requests by inlining critical CSS.\n   * Defaults to true.\n   */\n  inlineCriticalCss?: boolean;\n\n  /**\n   * Base path location of index file.\n   * Defaults to the 'documentFilePath' dirname when not provided.\n   */\n  publicPath?: string;\n}\n\n/**\n * A common engine to use to server render an application.\n */\n\nexport class CommonEngine {\n  private readonly templateCache = new Map<string, string>();\n  private readonly inlineCriticalCssProcessor = new CommonEngineInlineCriticalCssProcessor();\n  private readonly pageIsSSG = new Map<string, boolean>();\n\n  constructor(private options?: CommonEngineOptions) {\n    attachNodeGlobalErrorHandlers();\n  }\n\n  /**\n   * Render an HTML document for a specific URL with specified\n   * render options\n   */\n  async render(opts: CommonEngineRenderOptions): Promise<string> {\n    const enablePerformanceProfiler = this.options?.enablePerformanceProfiler;\n\n    const runMethod = enablePerformanceProfiler\n      ? runMethodAndMeasurePerf\n      : noopRunMethodAndMeasurePerf;\n\n    let html = await runMethod('Retrieve SSG Page', () => this.retrieveSSGPage(opts));\n\n    if (html === undefined) {\n      html = await runMethod('Render Page', () => this.renderApplication(opts));\n\n      if (opts.inlineCriticalCss !== false) {\n        const content = await runMethod('Inline Critical CSS', () =>\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          this.inlineCriticalCss(html!, opts),\n        );\n\n        html = content;\n      }\n    }\n\n    if (enablePerformanceProfiler) {\n      printPerformanceLogs();\n    }\n\n    return html;\n  }\n\n  private inlineCriticalCss(html: string, opts: CommonEngineRenderOptions): Promise<string> {\n    const outputPath =\n      opts.publicPath ?? (opts.documentFilePath ? dirname(opts.documentFilePath) : '');\n\n    return this.inlineCriticalCssProcessor.process(html, outputPath);\n  }\n\n  private async retrieveSSGPage(opts: CommonEngineRenderOptions): Promise<string | undefined> {\n    const { publicPath, documentFilePath, url } = opts;\n    if (!publicPath || !documentFilePath || url === undefined) {\n      return undefined;\n    }\n\n    const { pathname } = new URL(url, 'resolve://');\n    // Do not use `resolve` here as otherwise it can lead to path traversal vulnerability.\n    // See: https://portswigger.net/web-security/file-path-traversal\n    const pagePath = join(publicPath, pathname, 'index.html');\n\n    if (this.pageIsSSG.get(pagePath)) {\n      // Serve pre-rendered page.\n      return fs.promises.readFile(pagePath, 'utf-8');\n    }\n\n    if (!pagePath.startsWith(normalize(publicPath))) {\n      // Potential path traversal detected.\n      return undefined;\n    }\n\n    if (pagePath === resolve(documentFilePath) || !(await exists(pagePath))) {\n      // View matches with prerender path or file does not exist.\n      this.pageIsSSG.set(pagePath, false);\n\n      return undefined;\n    }\n\n    // Static file exists.\n    const content = await fs.promises.readFile(pagePath, 'utf-8');\n    const isSSG = SSG_MARKER_REGEXP.test(content);\n    this.pageIsSSG.set(pagePath, isSSG);\n\n    return isSSG ? content : undefined;\n  }\n\n  private async renderApplication(opts: CommonEngineRenderOptions): Promise<string> {\n    const moduleOrFactory = this.options?.bootstrap ?? opts.bootstrap;\n    if (!moduleOrFactory) {\n      throw new Error('A module or bootstrap option must be provided.');\n    }\n\n    const extraProviders: StaticProvider[] = [\n      { provide: ɵSERVER_CONTEXT, useValue: 'ssr' },\n      ...(opts.providers ?? []),\n      ...(this.options?.providers ?? []),\n    ];\n\n    let document = opts.document;\n    if (!document && opts.documentFilePath) {\n      document = await this.getDocument(opts.documentFilePath);\n    }\n\n    const commonRenderingOptions = {\n      url: opts.url,\n      document,\n    };\n\n    return isBootstrapFn(moduleOrFactory)\n      ? renderApplication(moduleOrFactory, {\n          platformProviders: extraProviders,\n          ...commonRenderingOptions,\n        })\n      : renderModule(moduleOrFactory, { extraProviders, ...commonRenderingOptions });\n  }\n\n  /** Retrieve the document from the cache or the filesystem */\n  private async getDocument(filePath: string): Promise<string> {\n    let doc = this.templateCache.get(filePath);\n\n    if (!doc) {\n      doc = await fs.promises.readFile(filePath, 'utf-8');\n      this.templateCache.set(filePath, doc);\n    }\n\n    return doc;\n  }\n}\n\nasync function exists(path: fs.PathLike): Promise<boolean> {\n  try {\n    await fs.promises.access(path, fs.constants.F_OK);\n\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction isBootstrapFn(value: unknown): value is () => Promise<ApplicationRef> {\n  // We can differentiate between a module and a bootstrap function by reading compiler-generated `ɵmod` static property:\n  return typeof value === 'function' && !('ɵmod' in value);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type { IncomingHttpHeaders, IncomingMessage } from 'node:http';\nimport type { Http2ServerRequest } from 'node:http2';\n\n/**\n * A set containing all the pseudo-headers defined in the HTTP/2 specification.\n *\n * This set can be used to filter out pseudo-headers from a list of headers,\n * as they are not allowed to be set directly using the `Node.js` Undici API or\n * the web `Headers` API.\n */\nconst HTTP2_PSEUDO_HEADERS = new Set([':method', ':scheme', ':authority', ':path', ':status']);\n\n/**\n * Converts a Node.js `IncomingMessage` or `Http2ServerRequest` into a\n * Web Standard `Request` object.\n *\n * This function adapts the Node.js request objects to a format that can\n * be used by web platform APIs.\n *\n * @param nodeRequest - The Node.js request object (`IncomingMessage` or `Http2ServerRequest`) to convert.\n * @returns A Web Standard `Request` object.\n */\nexport function createWebRequestFromNodeRequest(\n  nodeRequest: IncomingMessage | Http2ServerRequest,\n): Request {\n  const { headers, method = 'GET' } = nodeRequest;\n  const withBody = method !== 'GET' && method !== 'HEAD';\n  const referrer = headers.referer && URL.canParse(headers.referer) ? headers.referer : undefined;\n\n  return new Request(createRequestUrl(nodeRequest), {\n    method,\n    headers: createRequestHeaders(headers),\n    body: withBody ? nodeRequest : undefined,\n    duplex: withBody ? 'half' : undefined,\n    referrer,\n  });\n}\n\n/**\n * Creates a `Headers` object from Node.js `IncomingHttpHeaders`.\n *\n * @param nodeHeaders - The Node.js `IncomingHttpHeaders` object to convert.\n * @returns A `Headers` object containing the converted headers.\n */\nfunction createRequestHeaders(nodeHeaders: IncomingHttpHeaders): Headers {\n  const headers = new Headers();\n\n  for (const [name, value] of Object.entries(nodeHeaders)) {\n    if (HTTP2_PSEUDO_HEADERS.has(name)) {\n      continue;\n    }\n\n    if (typeof value === 'string') {\n      headers.append(name, value);\n    } else if (Array.isArray(value)) {\n      for (const item of value) {\n        headers.append(name, item);\n      }\n    }\n  }\n\n  return headers;\n}\n\n/**\n * Creates a `URL` object from a Node.js `IncomingMessage`, taking into account the protocol, host, and port.\n *\n * @param nodeRequest - The Node.js `IncomingMessage` or `Http2ServerRequest` object to extract URL information from.\n * @returns A `URL` object representing the request URL.\n */\nfunction createRequestUrl(nodeRequest: IncomingMessage | Http2ServerRequest): URL {\n  const {\n    headers,\n    socket,\n    url = '',\n    originalUrl,\n  } = nodeRequest as IncomingMessage & { originalUrl?: string };\n  const protocol =\n    getFirstHeaderValue(headers['x-forwarded-proto']) ??\n    ('encrypted' in socket && socket.encrypted ? 'https' : 'http');\n  const hostname =\n    getFirstHeaderValue(headers['x-forwarded-host']) ?? headers.host ?? headers[':authority'];\n\n  if (Array.isArray(hostname)) {\n    throw new Error('host value cannot be an array.');\n  }\n\n  let hostnameWithPort = hostname;\n  if (!hostname?.includes(':')) {\n    const port = getFirstHeaderValue(headers['x-forwarded-port']);\n    if (port) {\n      hostnameWithPort += `:${port}`;\n    }\n  }\n\n  return new URL(originalUrl ?? url, `${protocol}://${hostnameWithPort}`);\n}\n\n/**\n * Extracts the first value from a multi-value header string.\n *\n * @param value - A string or an array of strings representing the header values.\n *                           If it's a string, values are expected to be comma-separated.\n * @returns The first trimmed value from the multi-value header, or `undefined` if the input is invalid or empty.\n *\n * @example\n * ```typescript\n * getFirstHeaderValue(\"value1, value2, value3\"); // \"value1\"\n * getFirstHeaderValue([\"value1\", \"value2\"]); // \"value1\"\n * getFirstHeaderValue(undefined); // undefined\n * ```\n */\nfunction getFirstHeaderValue(value: string | string[] | undefined): string | undefined {\n  return value?.toString().split(',', 1)[0]?.trim();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { AngularAppEngine } from '@angular/ssr';\nimport type { IncomingMessage } from 'node:http';\nimport type { Http2ServerRequest } from 'node:http2';\nimport { attachNodeGlobalErrorHandlers } from './errors';\nimport { createWebRequestFromNodeRequest } from './request';\n\n/**\n * Angular server application engine.\n * Manages Angular server applications (including localized ones), handles rendering requests,\n * and optionally transforms index HTML before rendering.\n *\n * @remarks This class should be instantiated once and used as a singleton across the server-side\n * application to ensure consistent handling of rendering requests and resource management.\n */\nexport class AngularNodeAppEngine {\n  private readonly angularAppEngine = new AngularAppEngine();\n\n  constructor() {\n    attachNodeGlobalErrorHandlers();\n  }\n\n  /**\n   * Handles an incoming HTTP request by serving prerendered content, performing server-side rendering,\n   * or delivering a static file for client-side rendered routes based on the `RenderMode` setting.\n   *\n   * This method adapts Node.js's `IncomingMessage` or `Http2ServerRequest`\n   * to a format compatible with the `AngularAppEngine` and delegates the handling logic to it.\n   *\n   * @param request - The incoming HTTP request (`IncomingMessage` or `Http2ServerRequest`).\n   * @param requestContext - Optional context for rendering, such as metadata associated with the request.\n   * @returns A promise that resolves to the resulting HTTP response object, or `null` if no matching Angular route is found.\n   *\n   * @remarks A request to `https://www.example.com/page/index.html` will serve or render the Angular route\n   * corresponding to `https://www.example.com/page`.\n   */\n  async handle(\n    request: IncomingMessage | Http2ServerRequest,\n    requestContext?: unknown,\n  ): Promise<Response | null> {\n    const webRequest = createWebRequestFromNodeRequest(request);\n\n    return this.angularAppEngine.handle(webRequest, requestContext);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type { IncomingMessage, ServerResponse } from 'node:http';\n\n/**\n * Represents a middleware function for handling HTTP requests in a Node.js environment.\n *\n * @param req - The incoming HTTP request object.\n * @param res - The outgoing HTTP response object.\n * @param next - A callback function that signals the completion of the middleware or forwards the error if provided.\n *\n * @returns A Promise that resolves to void or simply void. The handler can be asynchronous.\n */\nexport type NodeRequestHandlerFunction = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  next: (err?: unknown) => void,\n) => Promise<void> | void;\n\n/**\n * Attaches metadata to the handler function to mark it as a special handler for Node.js environments.\n *\n * @typeParam T - The type of the handler function.\n * @param handler - The handler function to be defined and annotated.\n * @returns The same handler function passed as an argument, with metadata attached.\n *\n * @example\n * Usage in an Express application:\n * ```ts\n * const app = express();\n * export default createNodeRequestHandler(app);\n * ```\n *\n * @example\n * Usage in a Hono application:\n * ```ts\n * const app = new Hono();\n * export default createNodeRequestHandler(async (req, res, next) => {\n *   try {\n *     const webRes = await app.fetch(createWebRequestFromNodeRequest(req));\n *     if (webRes) {\n *       await writeResponseToNodeResponse(webRes, res);\n *     } else {\n *       next();\n *     }\n *   } catch (error) {\n *     next(error);\n *   }\n * }));\n * ```\n *\n * @example\n * Usage in a Fastify application:\n * ```ts\n * const app = Fastify();\n * export default createNodeRequestHandler(async (req, res) => {\n *   await app.ready();\n *   app.server.emit('request', req, res);\n *   res.send('Hello from Fastify with Node Next Handler!');\n * }));\n * ```\n */\nexport function createNodeRequestHandler<T extends NodeRequestHandlerFunction>(handler: T): T {\n  (handler as T & { __ng_node_request_handler__?: boolean })['__ng_node_request_handler__'] = true;\n\n  return handler;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type { ServerResponse } from 'node:http';\nimport type { Http2ServerResponse } from 'node:http2';\n\n/**\n * Streams a web-standard `Response` into a Node.js `ServerResponse`\n * or `Http2ServerResponse`.\n *\n * This function adapts the web `Response` object to write its content\n * to a Node.js response object, handling both HTTP/1.1 and HTTP/2.\n *\n * @param source - The web-standard `Response` object to stream from.\n * @param destination - The Node.js response object (`ServerResponse` or `Http2ServerResponse`) to stream into.\n * @returns A promise that resolves once the streaming operation is complete.\n */\nexport async function writeResponseToNodeResponse(\n  source: Response,\n  destination: ServerResponse | Http2ServerResponse,\n): Promise<void> {\n  const { status, headers, body } = source;\n  destination.statusCode = status;\n\n  let cookieHeaderSet = false;\n  for (const [name, value] of headers.entries()) {\n    if (name === 'set-cookie') {\n      if (cookieHeaderSet) {\n        continue;\n      }\n\n      // Sets the 'set-cookie' header only once to ensure it is correctly applied.\n      // Concatenating 'set-cookie' values can lead to incorrect behavior, so we use a single value from `headers.getSetCookie()`.\n      destination.setHeader(name, headers.getSetCookie());\n      cookieHeaderSet = true;\n    } else {\n      destination.setHeader(name, value);\n    }\n  }\n\n  if ('flushHeaders' in destination) {\n    destination.flushHeaders();\n  }\n\n  if (!body) {\n    destination.end();\n\n    return;\n  }\n\n  try {\n    const reader = body.getReader();\n\n    destination.on('close', () => {\n      reader.cancel().catch((error) => {\n        // eslint-disable-next-line no-console\n        console.error(\n          `An error occurred while writing the response body for: ${destination.req.url}.`,\n          error,\n        );\n      });\n    });\n\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        destination.end();\n        break;\n      }\n\n      const canContinue = (destination as ServerResponse).write(value);\n      if (canContinue === false) {\n        // Explicitly check for `false`, as AWS may return `undefined` even though this is not valid.\n        // See: https://github.com/CodeGenieApp/serverless-express/issues/683\n        await new Promise<void>((resolve) => destination.once('drain', resolve));\n      }\n    }\n  } catch {\n    destination.end('Internal server error.');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { argv } from 'node:process';\nimport { fileURLToPath } from 'node:url';\n\n/**\n * Determines whether the provided URL represents the main entry point module.\n *\n * This function checks if the provided URL corresponds to the main ESM module being executed directly.\n * It's useful for conditionally executing code that should only run when a module is the entry point,\n * such as starting a server or initializing an application.\n *\n * It performs two key checks:\n * 1. Verifies if the URL starts with 'file:', ensuring it is a local file.\n * 2. Compares the URL's resolved file path with the first command-line argument (`process.argv[1]`),\n *    which points to the file being executed.\n *\n * @param url The URL of the module to check. This should typically be `import.meta.url`.\n * @returns `true` if the provided URL represents the main entry point, otherwise `false`.\n */\nexport function isMainModule(url: string): boolean {\n  return url.startsWith('file:') && argv[1] === fileURLToPath(url);\n}\n"], "names": ["InlineCriticalCssProcessor", "URL", "ɵSERVER_CONTEXT"], "mappings": ";;;;;;;;AAQA;;;;;;;;;;;AAWG;SACa,6BAA6B,GAAA;AAC3C,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B;;;;IAKF,MAAM,KAAK,GAA4E,UAAU;AACjG,IAAA,IAAI,KAAK,CAAC,qCAAqC,EAAE;QAC/C;;AAGF,IAAA,KAAK,CAAC,qCAAqC,GAAG,IAAI;IAElD;;AAEG,SAAA,EAAE,CAAC,oBAAoB,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC;;AAE9E,SAAA,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAClF;;MC5Ba,sCAAsC,CAAA;AAChC,IAAA,aAAa,GAAG,IAAI,GAAG,EAAkB;AAE1D,IAAA,MAAM,OAAO,CAAC,IAAY,EAAE,UAA8B,EAAA;QACxD,MAAM,QAAQ,GAAG,IAAIA,2BAA0B,CAAC,OAAO,IAAI,KAAI;YAC7D,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;AAClD,YAAA,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,eAAe,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;gBAC/C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC;;AAG/C,YAAA,OAAO,eAAe;SACvB,EAAE,UAAU,CAAC;AAEd,QAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;;AAEhC;;ACnBD,MAAM,uBAAuB,GAAG,KAAK;SAErB,oBAAoB,GAAA;IAClC,IAAI,aAAa,GAAG,CAAC;IACrB,MAAM,UAAU,GAAoC,EAAE;AAEtD,IAAA,KAAK,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE;QACxE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,EAAE;YAC7C;;;AAIF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;AACjE,QAAA,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,EAAE;AAC/B,YAAA,aAAa,GAAG,IAAI,CAAC,MAAM;;AAG7B,QAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAG,EAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAI,EAAA,CAAA,CAAC,CAAC;AACnD,QAAA,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC;;;AAIjC,IAAA,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;IACxD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,UAAU,EAAE;QACtC,MAAM,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;AAC9C,QAAA,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;;AAEhD,IAAA,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;;AAE1D;AAEO,eAAe,uBAAuB,CAC3C,KAAa,EACb,WAA6B,EAAA;AAE7B,IAAA,MAAM,SAAS,GAAG,CAAA,EAAG,uBAAuB,CAAI,CAAA,EAAA,KAAK,EAAE;AACvD,IAAA,MAAM,UAAU,GAAG,CAAS,MAAA,EAAA,SAAS,EAAE;AACvC,IAAA,MAAM,QAAQ,GAAG,CAAO,IAAA,EAAA,SAAS,EAAE;AAEnC,IAAA,IAAI;AACF,QAAA,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QAE5B,OAAO,MAAM,WAAW,EAAE;;YAClB;AACR,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;AACpD,QAAA,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC;AAClC,QAAA,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;;AAEpC;AAEgB,SAAA,2BAA2B,CACzC,KAAa,EACb,WAA6B,EAAA;IAE7B,OAAO,WAAW,EAAE;AACtB;;AC3CA,MAAM,iBAAiB,GAAG,2CAA2C;AAoCrE;;AAEG;MAEU,YAAY,CAAA;AAKH,IAAA,OAAA;AAJH,IAAA,aAAa,GAAG,IAAI,GAAG,EAAkB;AACzC,IAAA,0BAA0B,GAAG,IAAI,sCAAsC,EAAE;AACzE,IAAA,SAAS,GAAG,IAAI,GAAG,EAAmB;AAEvD,IAAA,WAAA,CAAoB,OAA6B,EAAA;QAA7B,IAAO,CAAA,OAAA,GAAP,OAAO;AACzB,QAAA,6BAA6B,EAAE;;AAGjC;;;AAGG;IACH,MAAM,MAAM,CAAC,IAA+B,EAAA;AAC1C,QAAA,MAAM,yBAAyB,GAAG,IAAI,CAAC,OAAO,EAAE,yBAAyB;QAEzE,MAAM,SAAS,GAAG;AAChB,cAAE;cACA,2BAA2B;AAE/B,QAAA,IAAI,IAAI,GAAG,MAAM,SAAS,CAAC,mBAAmB,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAEjF,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,IAAI,GAAG,MAAM,SAAS,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAEzE,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;gBACpC,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,qBAAqB,EAAE;;gBAErD,IAAI,CAAC,iBAAiB,CAAC,IAAK,EAAE,IAAI,CAAC,CACpC;gBAED,IAAI,GAAG,OAAO;;;QAIlB,IAAI,yBAAyB,EAAE;AAC7B,YAAA,oBAAoB,EAAE;;AAGxB,QAAA,OAAO,IAAI;;IAGL,iBAAiB,CAAC,IAAY,EAAE,IAA+B,EAAA;QACrE,MAAM,UAAU,GACd,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;QAElF,OAAO,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC;;IAG1D,MAAM,eAAe,CAAC,IAA+B,EAAA;QAC3D,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,EAAE,GAAG,IAAI;QAClD,IAAI,CAAC,UAAU,IAAI,CAAC,gBAAgB,IAAI,GAAG,KAAK,SAAS,EAAE;AACzD,YAAA,OAAO,SAAS;;QAGlB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAIC,KAAG,CAAC,GAAG,EAAE,YAAY,CAAC;;;QAG/C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;QAEzD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;;YAEhC,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;;QAGhD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE;;AAE/C,YAAA,OAAO,SAAS;;AAGlB,QAAA,IAAI,QAAQ,KAAK,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE;;YAEvE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;AAEnC,YAAA,OAAO,SAAS;;;AAIlB,QAAA,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;QAC7D,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAEnC,OAAO,KAAK,GAAG,OAAO,GAAG,SAAS;;IAG5B,MAAM,iBAAiB,CAAC,IAA+B,EAAA;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,SAAS;QACjE,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC;;AAGnE,QAAA,MAAM,cAAc,GAAqB;AACvC,YAAA,EAAE,OAAO,EAAEC,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC7C,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;SACnC;AAED,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC5B,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACtC,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAG1D,QAAA,MAAM,sBAAsB,GAAG;YAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ;SACT;QAED,OAAO,aAAa,CAAC,eAAe;AAClC,cAAE,iBAAiB,CAAC,eAAe,EAAE;AACjC,gBAAA,iBAAiB,EAAE,cAAc;AACjC,gBAAA,GAAG,sBAAsB;aAC1B;AACH,cAAE,YAAY,CAAC,eAAe,EAAE,EAAE,cAAc,EAAE,GAAG,sBAAsB,EAAE,CAAC;;;IAI1E,MAAM,WAAW,CAAC,QAAgB,EAAA;QACxC,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;QAE1C,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,GAAG,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;;AAGvC,QAAA,OAAO,GAAG;;AAEb;AAED,eAAe,MAAM,CAAC,IAAiB,EAAA;AACrC,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;AAEjD,QAAA,OAAO,IAAI;;AACX,IAAA,MAAM;AACN,QAAA,OAAO,KAAK;;AAEhB;AAEA,SAAS,aAAa,CAAC,KAAc,EAAA;;IAEnC,OAAO,OAAO,KAAK,KAAK,UAAU,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC;AAC1D;;AC/LA;;;;;;AAMG;AACH,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AAE9F;;;;;;;;;AASG;AACG,SAAU,+BAA+B,CAC7C,WAAiD,EAAA;IAEjD,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,WAAW;IAC/C,MAAM,QAAQ,GAAG,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM;IACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS;AAE/F,IAAA,OAAO,IAAI,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE;QAChD,MAAM;AACN,QAAA,OAAO,EAAE,oBAAoB,CAAC,OAAO,CAAC;QACtC,IAAI,EAAE,QAAQ,GAAG,WAAW,GAAG,SAAS;QACxC,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,SAAS;QACrC,QAAQ;AACT,KAAA,CAAC;AACJ;AAEA;;;;;AAKG;AACH,SAAS,oBAAoB,CAAC,WAAgC,EAAA;AAC5D,IAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE;AAE7B,IAAA,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AACvD,QAAA,IAAI,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAClC;;AAGF,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;;AACtB,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC/B,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,gBAAA,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;;;;AAKhC,IAAA,OAAO,OAAO;AAChB;AAEA;;;;;AAKG;AACH,SAAS,gBAAgB,CAAC,WAAiD,EAAA;AACzE,IAAA,MAAM,EACJ,OAAO,EACP,MAAM,EACN,GAAG,GAAG,EAAE,EACR,WAAW,GACZ,GAAG,WAAyD;IAC7D,MAAM,QAAQ,GACZ,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,SAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,OAAO,GAAG,MAAM,CAAC;AAChE,IAAA,MAAM,QAAQ,GACZ,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,YAAY,CAAC;AAE3F,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;;IAGnD,IAAI,gBAAgB,GAAG,QAAQ;IAC/B,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC7D,IAAI,IAAI,EAAE;AACR,YAAA,gBAAgB,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,CAAE;;;AAIlC,IAAA,OAAO,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,EAAE,CAAA,EAAG,QAAQ,CAAA,GAAA,EAAM,gBAAgB,CAAA,CAAE,CAAC;AACzE;AAEA;;;;;;;;;;;;;AAaG;AACH,SAAS,mBAAmB,CAAC,KAAoC,EAAA;AAC/D,IAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;AACnD;;AC5GA;;;;;;;AAOG;MACU,oBAAoB,CAAA;AACd,IAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;AAE1D,IAAA,WAAA,GAAA;AACE,QAAA,6BAA6B,EAAE;;AAGjC;;;;;;;;;;;;;AAaG;AACH,IAAA,MAAM,MAAM,CACV,OAA6C,EAC7C,cAAwB,EAAA;AAExB,QAAA,MAAM,UAAU,GAAG,+BAA+B,CAAC,OAAO,CAAC;QAE3D,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC;;AAElE;;AC1BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CG;AACG,SAAU,wBAAwB,CAAuC,OAAU,EAAA;AACtF,IAAA,OAAyD,CAAC,6BAA6B,CAAC,GAAG,IAAI;AAEhG,IAAA,OAAO,OAAO;AAChB;;AC7DA;;;;;;;;;;AAUG;AACI,eAAe,2BAA2B,CAC/C,MAAgB,EAChB,WAAiD,EAAA;IAEjD,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM;AACxC,IAAA,WAAW,CAAC,UAAU,GAAG,MAAM;IAE/B,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAA,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;AAC7C,QAAA,IAAI,IAAI,KAAK,YAAY,EAAE;YACzB,IAAI,eAAe,EAAE;gBACnB;;;;YAKF,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC;YACnD,eAAe,GAAG,IAAI;;aACjB;AACL,YAAA,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC;;;AAItC,IAAA,IAAI,cAAc,IAAI,WAAW,EAAE;QACjC,WAAW,CAAC,YAAY,EAAE;;IAG5B,IAAI,CAAC,IAAI,EAAE;QACT,WAAW,CAAC,GAAG,EAAE;QAEjB;;AAGF,IAAA,IAAI;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;AAE/B,QAAA,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,MAAK;YAC3B,MAAM,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAI;;AAE9B,gBAAA,OAAO,CAAC,KAAK,CACX,CAAA,uDAAA,EAA0D,WAAW,CAAC,GAAG,CAAC,GAAG,CAAA,CAAA,CAAG,EAChF,KAAK,CACN;AACH,aAAC,CAAC;AACJ,SAAC,CAAC;;QAGF,OAAO,IAAI,EAAE;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE;YAC3C,IAAI,IAAI,EAAE;gBACR,WAAW,CAAC,GAAG,EAAE;gBACjB;;YAGF,MAAM,WAAW,GAAI,WAA8B,CAAC,KAAK,CAAC,KAAK,CAAC;AAChE,YAAA,IAAI,WAAW,KAAK,KAAK,EAAE;;;AAGzB,gBAAA,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;;;AAG5E,IAAA,MAAM;AACN,QAAA,WAAW,CAAC,GAAG,CAAC,wBAAwB,CAAC;;AAE7C;;AC3EA;;;;;;;;;;;;;;AAcG;AACG,SAAU,YAAY,CAAC,GAAW,EAAA;AACtC,IAAA,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,aAAa,CAAC,GAAG,CAAC;AAClE;;;;"}