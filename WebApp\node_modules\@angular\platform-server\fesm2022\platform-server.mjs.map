{"version": 3, "file": "platform-server.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-server/src/provide_server.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-server/src/utils.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-server/src/version.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EnvironmentProviders, makeEnvironmentProviders} from '@angular/core';\n\nimport {PLATFORM_SERVER_PROVIDERS} from './server';\n\n/**\n * Sets up providers necessary to enable server rendering functionality for the application.\n *\n * @usageNotes\n *\n * Basic example of how you can add server support to your application:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideServerRendering()]\n * });\n * ```\n *\n * @publicApi\n * @returns A set of providers to setup the server.\n */\nexport function provideServerRendering(): EnvironmentProviders {\n  if (typeof ngServerMode === 'undefined') {\n    globalThis['ngServerMode'] = true;\n  }\n\n  return makeEnvironmentProviders([...PLATFORM_SERVER_PROVIDERS]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  APP_ID,\n  ApplicationRef,\n  CSP_NONCE,\n  InjectionToken,\n  PlatformRef,\n  Provider,\n  Renderer2,\n  StaticProvider,\n  Type,\n  ɵannotateForHydration as annotateForHydration,\n  ɵIS_HYDRATION_DOM_REUSE_ENABLED as IS_HYDRATION_DOM_REUSE_ENABLED,\n  ɵSSR_CONTENT_INTEGRITY_MARKER as SSR_CONTENT_INTEGRITY_MARKER,\n  ɵstartMeasuring as startMeasuring,\n  ɵstopMeasuring as stopMeasuring,\n} from '@angular/core';\n\nimport {PlatformState} from './platform_state';\nimport {platformServer} from './server';\nimport {BEFORE_APP_SERIALIZED, INITIAL_CONFIG} from './tokens';\nimport {createScript} from './transfer_state';\n\n/**\n * Event dispatch (JSAction) script is inlined into the HTML by the build\n * process to avoid extra blocking request on a page. The script looks like this:\n * ```html\n * <script type=\"text/javascript\" id=\"ng-event-dispatch-contract\">...</script>\n * ```\n * This const represents the \"id\" attribute value.\n */\nexport const EVENT_DISPATCH_SCRIPT_ID = 'ng-event-dispatch-contract';\n\ninterface PlatformOptions {\n  document?: string | Document;\n  url?: string;\n  platformProviders?: Provider[];\n}\n\n/**\n * Creates an instance of a server platform (with or without JIT compiler support\n * depending on the `ngJitMode` global const value), using provided options.\n */\nfunction createServerPlatform(options: PlatformOptions): PlatformRef {\n  const extraProviders = options.platformProviders ?? [];\n  const measuringLabel = 'createServerPlatform';\n  startMeasuring(measuringLabel);\n\n  const platform = platformServer([\n    {provide: INITIAL_CONFIG, useValue: {document: options.document, url: options.url}},\n    extraProviders,\n  ]);\n\n  stopMeasuring(measuringLabel);\n  return platform;\n}\n\n/**\n * Finds and returns inlined event dispatch script if it exists.\n * See the `EVENT_DISPATCH_SCRIPT_ID` const docs for additional info.\n */\nfunction findEventDispatchScript(doc: Document) {\n  return doc.getElementById(EVENT_DISPATCH_SCRIPT_ID);\n}\n\n/**\n * Removes inlined event dispatch script if it exists.\n * See the `EVENT_DISPATCH_SCRIPT_ID` const docs for additional info.\n */\nfunction removeEventDispatchScript(doc: Document) {\n  findEventDispatchScript(doc)?.remove();\n}\n\n/**\n * Annotate nodes for hydration and remove event dispatch script when not needed.\n */\nfunction prepareForHydration(platformState: PlatformState, applicationRef: ApplicationRef): void {\n  const measuringLabel = 'prepareForHydration';\n  startMeasuring(measuringLabel);\n  const environmentInjector = applicationRef.injector;\n  const doc = platformState.getDocument();\n\n  if (!environmentInjector.get(IS_HYDRATION_DOM_REUSE_ENABLED, false)) {\n    // Hydration is diabled, remove inlined event dispatch script.\n    // (which was injected by the build process) from the HTML.\n    removeEventDispatchScript(doc);\n\n    return;\n  }\n\n  appendSsrContentIntegrityMarker(doc);\n\n  const eventTypesToReplay = annotateForHydration(applicationRef, doc);\n  if (eventTypesToReplay.regular.size || eventTypesToReplay.capture.size) {\n    insertEventRecordScript(\n      environmentInjector.get(APP_ID),\n      doc,\n      eventTypesToReplay,\n      environmentInjector.get(CSP_NONCE, null),\n    );\n  } else {\n    // No events to replay, we should remove inlined event dispatch script\n    // (which was injected by the build process) from the HTML.\n    removeEventDispatchScript(doc);\n  }\n  stopMeasuring(measuringLabel);\n}\n\n/**\n * Creates a marker comment node and append it into the `<body>`.\n * Some CDNs have mechanisms to remove all comment node from HTML.\n * This behaviour breaks hydration, so we'll detect on the client side if this\n * marker comment is still available or else throw an error\n */\nfunction appendSsrContentIntegrityMarker(doc: Document) {\n  // Adding a ng hydration marker comment\n  const comment = doc.createComment(SSR_CONTENT_INTEGRITY_MARKER);\n  doc.body.firstChild\n    ? doc.body.insertBefore(comment, doc.body.firstChild)\n    : doc.body.append(comment);\n}\n\n/**\n * Adds the `ng-server-context` attribute to host elements of all bootstrapped components\n * within a given application.\n */\nfunction appendServerContextInfo(applicationRef: ApplicationRef) {\n  const injector = applicationRef.injector;\n  let serverContext = sanitizeServerContext(injector.get(SERVER_CONTEXT, DEFAULT_SERVER_CONTEXT));\n  applicationRef.components.forEach((componentRef) => {\n    const renderer = componentRef.injector.get(Renderer2);\n    const element = componentRef.location.nativeElement;\n    if (element) {\n      renderer.setAttribute(element, 'ng-server-context', serverContext);\n    }\n  });\n}\n\nfunction insertEventRecordScript(\n  appId: string,\n  doc: Document,\n  eventTypesToReplay: {regular: Set<string>; capture: Set<string>},\n  nonce: string | null,\n): void {\n  const measuringLabel = 'insertEventRecordScript';\n  startMeasuring(measuringLabel);\n  const {regular, capture} = eventTypesToReplay;\n  const eventDispatchScript = findEventDispatchScript(doc);\n\n  // Note: this is only true when build with the CLI tooling, which inserts the script in the HTML\n  if (eventDispatchScript) {\n    // This is defined in packages/core/primitives/event-dispatch/contract_binary.ts\n    const replayScriptContents =\n      `window.__jsaction_bootstrap(` +\n      `document.body,` +\n      `\"${appId}\",` +\n      `${JSON.stringify(Array.from(regular))},` +\n      `${JSON.stringify(Array.from(capture))}` +\n      `);`;\n\n    const replayScript = createScript(doc, replayScriptContents, nonce);\n\n    // Insert replay script right after inlined event dispatch script, since it\n    // relies on `__jsaction_bootstrap` to be defined in the global scope.\n    eventDispatchScript.after(replayScript);\n  }\n  stopMeasuring(measuringLabel);\n}\n\n/**\n * Renders an Angular application to a string.\n *\n * @private\n *\n * @param platformRef - Reference to the Angular platform.\n * @param applicationRef - Reference to the Angular application.\n * @returns A promise that resolves to the rendered string.\n */\nexport async function renderInternal(\n  platformRef: PlatformRef,\n  applicationRef: ApplicationRef,\n): Promise<string> {\n  const platformState = platformRef.injector.get(PlatformState);\n  prepareForHydration(platformState, applicationRef);\n  appendServerContextInfo(applicationRef);\n\n  // Run any BEFORE_APP_SERIALIZED callbacks just before rendering to string.\n  const environmentInjector = applicationRef.injector;\n  const callbacks = environmentInjector.get(BEFORE_APP_SERIALIZED, null);\n  if (callbacks) {\n    const asyncCallbacks: Promise<void>[] = [];\n    for (const callback of callbacks) {\n      try {\n        const callbackResult = callback();\n        if (callbackResult) {\n          asyncCallbacks.push(callbackResult);\n        }\n      } catch (e) {\n        // Ignore exceptions.\n        console.warn('Ignoring BEFORE_APP_SERIALIZED Exception: ', e);\n      }\n    }\n\n    if (asyncCallbacks.length) {\n      for (const result of await Promise.allSettled(asyncCallbacks)) {\n        if (result.status === 'rejected') {\n          console.warn('Ignoring BEFORE_APP_SERIALIZED Exception: ', result.reason);\n        }\n      }\n    }\n  }\n\n  return platformState.renderToString();\n}\n\n/**\n * Destroy the application in a macrotask, this allows pending promises to be settled and errors\n * to be surfaced to the users.\n */\nfunction asyncDestroyPlatform(platformRef: PlatformRef): Promise<void> {\n  return new Promise<void>((resolve) => {\n    setTimeout(() => {\n      platformRef.destroy();\n      resolve();\n    }, 0);\n  });\n}\n\n/**\n * Specifies the value that should be used if no server context value has been provided.\n */\nconst DEFAULT_SERVER_CONTEXT = 'other';\n\n/**\n * An internal token that allows providing extra information about the server context\n * (e.g. whether SSR or SSG was used). The value is a string and characters other\n * than [a-zA-Z0-9\\-] are removed. See the default value in `DEFAULT_SERVER_CONTEXT` const.\n */\nexport const SERVER_CONTEXT = new InjectionToken<string>('SERVER_CONTEXT');\n\n/**\n * Sanitizes provided server context:\n * - removes all characters other than a-z, A-Z, 0-9 and `-`\n * - returns `other` if nothing is provided or the string is empty after sanitization\n */\nfunction sanitizeServerContext(serverContext: string): string {\n  const context = serverContext.replace(/[^a-zA-Z0-9\\-]/g, '');\n  return context.length > 0 ? context : DEFAULT_SERVER_CONTEXT;\n}\n\n/**\n * Bootstraps an application using provided NgModule and serializes the page content to string.\n *\n * @param moduleType A reference to an NgModule that should be used for bootstrap.\n * @param options Additional configuration for the render operation:\n *  - `document` - the document of the page to render, either as an HTML string or\n *                 as a reference to the `document` instance.\n *  - `url` - the URL for the current render request.\n *  - `extraProviders` - set of platform level providers for the current render request.\n *\n * @publicApi\n */\nexport async function renderModule<T>(\n  moduleType: Type<T>,\n  options: {document?: string | Document; url?: string; extraProviders?: StaticProvider[]},\n): Promise<string> {\n  const {document, url, extraProviders: platformProviders} = options;\n  const platformRef = createServerPlatform({document, url, platformProviders});\n  try {\n    const moduleRef = await platformRef.bootstrapModule(moduleType);\n    const applicationRef = moduleRef.injector.get(ApplicationRef);\n\n    const measuringLabel = 'whenStable';\n    startMeasuring(measuringLabel);\n    // Block until application is stable.\n    await applicationRef.whenStable();\n    stopMeasuring(measuringLabel);\n\n    return await renderInternal(platformRef, applicationRef);\n  } finally {\n    await asyncDestroyPlatform(platformRef);\n  }\n}\n\n/**\n * Bootstraps an instance of an Angular application and renders it to a string.\n\n * ```ts\n * const bootstrap = () => bootstrapApplication(RootComponent, appConfig);\n * const output: string = await renderApplication(bootstrap);\n * ```\n *\n * @param bootstrap A method that when invoked returns a promise that returns an `ApplicationRef`\n *     instance once resolved.\n * @param options Additional configuration for the render operation:\n *  - `document` - the document of the page to render, either as an HTML string or\n *                 as a reference to the `document` instance.\n *  - `url` - the URL for the current render request.\n *  - `platformProviders` - the platform level providers for the current render request.\n *\n * @returns A Promise, that returns serialized (to a string) rendered page, once resolved.\n *\n * @publicApi\n */\nexport async function renderApplication<T>(\n  bootstrap: () => Promise<ApplicationRef>,\n  options: {document?: string | Document; url?: string; platformProviders?: Provider[]},\n): Promise<string> {\n  const renderAppLabel = 'renderApplication';\n  const bootstrapLabel = 'bootstrap';\n  const _renderLabel = '_render';\n\n  startMeasuring(renderAppLabel);\n  const platformRef = createServerPlatform(options);\n  try {\n    startMeasuring(bootstrapLabel);\n    const applicationRef = await bootstrap();\n    stopMeasuring(bootstrapLabel);\n\n    startMeasuring(_renderLabel);\n\n    const measuringLabel = 'whenStable';\n    startMeasuring(measuringLabel);\n    // Block until application is stable.\n    await applicationRef.whenStable();\n    stopMeasuring(measuringLabel);\n\n    const rendered = await renderInternal(platformRef, applicationRef);\n    stopMeasuring(_renderLabel);\n    return rendered;\n  } finally {\n    await asyncDestroyPlatform(platformRef);\n    stopMeasuring(renderAppLabel);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-server package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('19.2.14');\n"], "names": ["startMeasuring", "stopMeasuring", "IS_HYDRATION_DOM_REUSE_ENABLED", "annotateForHydration", "SSR_CONTENT_INTEGRITY_MARKER"], "mappings": ";;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;AAcG;SACa,sBAAsB,GAAA;AACpC,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACvC,QAAA,UAAU,CAAC,cAAc,CAAC,GAAG,IAAI;;AAGnC,IAAA,OAAO,wBAAwB,CAAC,CAAC,GAAG,yBAAyB,CAAC,CAAC;AACjE;;ACHA;;;;;;;AAOG;AACI,MAAM,wBAAwB,GAAG,4BAA4B;AAQpE;;;AAGG;AACH,SAAS,oBAAoB,CAAC,OAAwB,EAAA;AACpD,IAAA,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,IAAI,EAAE;IACtD,MAAM,cAAc,GAAG,sBAAsB;IAC7CA,eAAc,CAAC,cAAc,CAAC;IAE9B,MAAM,QAAQ,GAAG,cAAc,CAAC;AAC9B,QAAA,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAC,EAAC;QACnF,cAAc;AACf,KAAA,CAAC;IAEFC,cAAa,CAAC,cAAc,CAAC;AAC7B,IAAA,OAAO,QAAQ;AACjB;AAEA;;;AAGG;AACH,SAAS,uBAAuB,CAAC,GAAa,EAAA;AAC5C,IAAA,OAAO,GAAG,CAAC,cAAc,CAAC,wBAAwB,CAAC;AACrD;AAEA;;;AAGG;AACH,SAAS,yBAAyB,CAAC,GAAa,EAAA;AAC9C,IAAA,uBAAuB,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE;AACxC;AAEA;;AAEG;AACH,SAAS,mBAAmB,CAAC,aAA4B,EAAE,cAA8B,EAAA;IACvF,MAAM,cAAc,GAAG,qBAAqB;IAC5CD,eAAc,CAAC,cAAc,CAAC;AAC9B,IAAA,MAAM,mBAAmB,GAAG,cAAc,CAAC,QAAQ;AACnD,IAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,EAAE;IAEvC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAACE,+BAA8B,EAAE,KAAK,CAAC,EAAE;;;QAGnE,yBAAyB,CAAC,GAAG,CAAC;QAE9B;;IAGF,+BAA+B,CAAC,GAAG,CAAC;IAEpC,MAAM,kBAAkB,GAAGC,qBAAoB,CAAC,cAAc,EAAE,GAAG,CAAC;AACpE,IAAA,IAAI,kBAAkB,CAAC,OAAO,CAAC,IAAI,IAAI,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE;QACtE,uBAAuB,CACrB,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAC/B,GAAG,EACH,kBAAkB,EAClB,mBAAmB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CACzC;;SACI;;;QAGL,yBAAyB,CAAC,GAAG,CAAC;;IAEhCF,cAAa,CAAC,cAAc,CAAC;AAC/B;AAEA;;;;;AAKG;AACH,SAAS,+BAA+B,CAAC,GAAa,EAAA;;IAEpD,MAAM,OAAO,GAAG,GAAG,CAAC,aAAa,CAACG,6BAA4B,CAAC;IAC/D,GAAG,CAAC,IAAI,CAAC;AACP,UAAE,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,UAAU;UAClD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC9B;AAEA;;;AAGG;AACH,SAAS,uBAAuB,CAAC,cAA8B,EAAA;AAC7D,IAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ;AACxC,IAAA,IAAI,aAAa,GAAG,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,sBAAsB,CAAC,CAAC;IAC/F,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,YAAY,KAAI;QACjD,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC;AACrD,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa;QACnD,IAAI,OAAO,EAAE;YACX,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAE,aAAa,CAAC;;AAEtE,KAAC,CAAC;AACJ;AAEA,SAAS,uBAAuB,CAC9B,KAAa,EACb,GAAa,EACb,kBAAgE,EAChE,KAAoB,EAAA;IAEpB,MAAM,cAAc,GAAG,yBAAyB;IAChDJ,eAAc,CAAC,cAAc,CAAC;AAC9B,IAAA,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,kBAAkB;AAC7C,IAAA,MAAM,mBAAmB,GAAG,uBAAuB,CAAC,GAAG,CAAC;;IAGxD,IAAI,mBAAmB,EAAE;;QAEvB,MAAM,oBAAoB,GACxB,CAA8B,4BAAA,CAAA;YAC9B,CAAgB,cAAA,CAAA;AAChB,YAAA,CAAA,CAAA,EAAI,KAAK,CAAI,EAAA,CAAA;YACb,CAAG,EAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAG,CAAA,CAAA;YACzC,CAAG,EAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE,CAAA;AACxC,YAAA,CAAA,EAAA,CAAI;QAEN,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,EAAE,oBAAoB,EAAE,KAAK,CAAC;;;AAInE,QAAA,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC;;IAEzCC,cAAa,CAAC,cAAc,CAAC;AAC/B;AAEA;;;;;;;;AAQG;AACI,eAAe,cAAc,CAClC,WAAwB,EACxB,cAA8B,EAAA;IAE9B,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC;AAC7D,IAAA,mBAAmB,CAAC,aAAa,EAAE,cAAc,CAAC;IAClD,uBAAuB,CAAC,cAAc,CAAC;;AAGvC,IAAA,MAAM,mBAAmB,GAAG,cAAc,CAAC,QAAQ;IACnD,MAAM,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC;IACtE,IAAI,SAAS,EAAE;QACb,MAAM,cAAc,GAAoB,EAAE;AAC1C,QAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,YAAA,IAAI;AACF,gBAAA,MAAM,cAAc,GAAG,QAAQ,EAAE;gBACjC,IAAI,cAAc,EAAE;AAClB,oBAAA,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;;;YAErC,OAAO,CAAC,EAAE;;AAEV,gBAAA,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,CAAC,CAAC;;;AAIjE,QAAA,IAAI,cAAc,CAAC,MAAM,EAAE;YACzB,KAAK,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;AAC7D,gBAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;oBAChC,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,MAAM,CAAC,MAAM,CAAC;;;;;AAMjF,IAAA,OAAO,aAAa,CAAC,cAAc,EAAE;AACvC;AAEA;;;AAGG;AACH,SAAS,oBAAoB,CAAC,WAAwB,EAAA;AACpD,IAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;QACnC,UAAU,CAAC,MAAK;YACd,WAAW,CAAC,OAAO,EAAE;AACrB,YAAA,OAAO,EAAE;SACV,EAAE,CAAC,CAAC;AACP,KAAC,CAAC;AACJ;AAEA;;AAEG;AACH,MAAM,sBAAsB,GAAG,OAAO;AAEtC;;;;AAIG;MACU,cAAc,GAAG,IAAI,cAAc,CAAS,gBAAgB;AAEzE;;;;AAIG;AACH,SAAS,qBAAqB,CAAC,aAAqB,EAAA;IAClD,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;AAC5D,IAAA,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,sBAAsB;AAC9D;AAEA;;;;;;;;;;;AAWG;AACI,eAAe,YAAY,CAChC,UAAmB,EACnB,OAAwF,EAAA;IAExF,MAAM,EAAC,QAAQ,EAAE,GAAG,EAAE,cAAc,EAAE,iBAAiB,EAAC,GAAG,OAAO;AAClE,IAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,EAAC,QAAQ,EAAE,GAAG,EAAE,iBAAiB,EAAC,CAAC;AAC5E,IAAA,IAAI;QACF,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC;QAC/D,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;QAE7D,MAAM,cAAc,GAAG,YAAY;QACnCD,eAAc,CAAC,cAAc,CAAC;;AAE9B,QAAA,MAAM,cAAc,CAAC,UAAU,EAAE;QACjCC,cAAa,CAAC,cAAc,CAAC;AAE7B,QAAA,OAAO,MAAM,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC;;YAChD;AACR,QAAA,MAAM,oBAAoB,CAAC,WAAW,CAAC;;AAE3C;AAEA;;;;;;;;;;;;;;;;;;;AAmBG;AACI,eAAe,iBAAiB,CACrC,SAAwC,EACxC,OAAqF,EAAA;IAErF,MAAM,cAAc,GAAG,mBAAmB;IAC1C,MAAM,cAAc,GAAG,WAAW;IAClC,MAAM,YAAY,GAAG,SAAS;IAE9BD,eAAc,CAAC,cAAc,CAAC;AAC9B,IAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,OAAO,CAAC;AACjD,IAAA,IAAI;QACFA,eAAc,CAAC,cAAc,CAAC;AAC9B,QAAA,MAAM,cAAc,GAAG,MAAM,SAAS,EAAE;QACxCC,cAAa,CAAC,cAAc,CAAC;QAE7BD,eAAc,CAAC,YAAY,CAAC;QAE5B,MAAM,cAAc,GAAG,YAAY;QACnCA,eAAc,CAAC,cAAc,CAAC;;AAE9B,QAAA,MAAM,cAAc,CAAC,UAAU,EAAE;QACjCC,cAAa,CAAC,cAAc,CAAC;QAE7B,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC;QAClEA,cAAa,CAAC,YAAY,CAAC;AAC3B,QAAA,OAAO,QAAQ;;YACP;AACR,QAAA,MAAM,oBAAoB,CAAC,WAAW,CAAC;QACvCA,cAAa,CAAC,cAAc,CAAC;;AAEjC;;AC7UA;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;;;"}