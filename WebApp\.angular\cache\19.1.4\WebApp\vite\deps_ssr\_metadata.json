{"hash": "22fb0026", "configHash": "fa10a812", "lockfileHash": "2aaf4cb6", "browserHash": "396b1fd1", "optimized": {"@angular/cdk/bidi": {"src": "../../../../../../node_modules/@angular/cdk/fesm2022/bidi.mjs", "file": "@angular_cdk_bidi.js", "fileHash": "c7db1238", "needsInterop": false}, "@angular/common": {"src": "../../../../../../node_modules/@angular/common/fesm2022/common.mjs", "file": "@angular_common.js", "fileHash": "6b5d6678", "needsInterop": false}, "@angular/common/http": {"src": "../../../../../../node_modules/@angular/common/fesm2022/http.mjs", "file": "@angular_common_http.js", "fileHash": "42abd1d3", "needsInterop": false}, "@angular/core": {"src": "../../../../../../node_modules/@angular/core/fesm2022/core.mjs", "file": "@angular_core.js", "fileHash": "db1c173a", "needsInterop": false}, "@angular/forms": {"src": "../../../../../../node_modules/@angular/forms/fesm2022/forms.mjs", "file": "@angular_forms.js", "fileHash": "4e8af6a6", "needsInterop": false}, "@angular/material/button": {"src": "../../../../../../node_modules/@angular/material/fesm2022/button.mjs", "file": "@angular_material_button.js", "fileHash": "f1293e64", "needsInterop": false}, "@angular/material/dialog": {"src": "../../../../../../node_modules/@angular/material/fesm2022/dialog.mjs", "file": "@angular_material_dialog.js", "fileHash": "45398a5c", "needsInterop": false}, "@angular/platform-browser": {"src": "../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs", "file": "@angular_platform-browser.js", "fileHash": "eed609cf", "needsInterop": false}, "@angular/platform-browser/animations": {"src": "../../../../../../node_modules/@angular/platform-browser/fesm2022/animations.mjs", "file": "@angular_platform-browser_animations.js", "fileHash": "7e02d080", "needsInterop": false}, "@angular/platform-browser/animations/async": {"src": "../../../../../../node_modules/@angular/platform-browser/fesm2022/animations/async.mjs", "file": "@angular_platform-browser_animations_async.js", "fileHash": "87cbabc7", "needsInterop": false}, "@angular/platform-server": {"src": "../../../../../../node_modules/@angular/platform-server/fesm2022/platform-server.mjs", "file": "@angular_platform-server.js", "fileHash": "3e5efe6d", "needsInterop": false}, "@angular/router": {"src": "../../../../../../node_modules/@angular/router/fesm2022/router.mjs", "file": "@angular_router.js", "fileHash": "c6c90bf7", "needsInterop": false}, "@angular/ssr": {"src": "../../../../../../node_modules/@angular/ssr/fesm2022/ssr.mjs", "file": "@angular_ssr.js", "fileHash": "24219f51", "needsInterop": false}, "@microsoft/signalr": {"src": "../../../../../../node_modules/@microsoft/signalr/dist/esm/index.js", "file": "@microsoft_signalr.js", "fileHash": "dfbe183d", "needsInterop": false}, "express": {"src": "../../../../../../node_modules/express/index.js", "file": "express.js", "fileHash": "68760064", "needsInterop": true}, "luxon": {"src": "../../../../../../node_modules/luxon/src/luxon.js", "file": "luxon.js", "fileHash": "36810328", "needsInterop": false}, "rxjs": {"src": "../../../../../../node_modules/rxjs/dist/cjs/index.js", "file": "rxjs.js", "fileHash": "18b39472", "needsInterop": true}, "rxjs/operators": {"src": "../../../../../../node_modules/rxjs/dist/cjs/operators/index.js", "file": "rxjs_operators.js", "fileHash": "413c791b", "needsInterop": true}}, "chunks": {"browser-OCQTQJO6": {"file": "browser-OCQTQJO6.js"}, "xhr2-TXIMV6CV": {"file": "xhr2-TXIMV6CV.js"}, "chunk-63NE7DOI": {"file": "chunk-63NE7DOI.js"}, "chunk-ESFBQBY2": {"file": "chunk-ESFBQBY2.js"}, "chunk-3OFSWJI2": {"file": "chunk-3OFSWJI2.js"}, "chunk-GJCHE5LL": {"file": "chunk-GJCHE5LL.js"}, "chunk-EFIYZZ4Y": {"file": "chunk-EFIYZZ4Y.js"}, "chunk-ZRZSEIBW": {"file": "chunk-ZRZSEIBW.js"}, "chunk-2VEYT5I5": {"file": "chunk-2VEYT5I5.js"}, "chunk-TOQZJBGR": {"file": "chunk-TOQZJBGR.js"}, "chunk-N4RD55UT": {"file": "chunk-N4RD55UT.js"}, "chunk-5D2JJTZD": {"file": "chunk-5D2JJTZD.js"}, "chunk-WBQSER3X": {"file": "chunk-WBQSER3X.js"}, "chunk-TDK5NIWS": {"file": "chunk-TDK5NIWS.js"}, "chunk-RK6XMIZN": {"file": "chunk-RK6XMIZN.js"}, "chunk-YHCV7DAQ": {"file": "chunk-YHCV7DAQ.js"}}}