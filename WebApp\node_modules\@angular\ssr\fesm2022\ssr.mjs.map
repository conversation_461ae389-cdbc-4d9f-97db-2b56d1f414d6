{"version": 3, "file": "ssr.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/assets.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/console.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/manifest.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/url.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/ng.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/promise.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/routes/route-config.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/routes/route-tree.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/routes/ng-routes.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/hooks.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/routes/router.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/crypto.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/inline-critical-css.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/utils/lru-cache.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/app.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/i18n.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/app-engine.ts", "../../../../../../darwin_arm64-fastbuild-ST-c646aa77115d/bin/packages/angular/ssr/src/handler.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { AngularAppManifest, ServerAsset } from './manifest';\n\n/**\n * Manages server-side assets.\n */\nexport class ServerAssets {\n  /**\n   * Creates an instance of ServerAsset.\n   *\n   * @param manifest - The manifest containing the server assets.\n   */\n  constructor(private readonly manifest: AngularAppManifest) {}\n\n  /**\n   * Retrieves the content of a server-side asset using its path.\n   *\n   * @param path - The path to the server asset within the manifest.\n   * @returns The server asset associated with the provided path, as a `ServerAsset` object.\n   * @throws Error - Throws an error if the asset does not exist.\n   */\n  getServerAsset(path: string): ServerAsset {\n    const asset = this.manifest.assets[path];\n    if (!asset) {\n      throw new Error(`Server asset '${path}' does not exist.`);\n    }\n\n    return asset;\n  }\n\n  /**\n   * Checks if a specific server-side asset exists.\n   *\n   * @param path - The path to the server asset.\n   * @returns A boolean indicating whether the asset exists.\n   */\n  hasServerAsset(path: string): boolean {\n    return !!this.manifest.assets[path];\n  }\n\n  /**\n   * Retrieves the asset for 'index.server.html'.\n   *\n   * @returns The `ServerAsset` object for 'index.server.html'.\n   * @throws Error - Throws an error if 'index.server.html' does not exist.\n   */\n  getIndexServerHtml(): ServerAsset {\n    return this.getServerAsset('index.server.html');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { ɵConsole } from '@angular/core';\n\n/**\n * A set of log messages that should be ignored and not printed to the console.\n */\nconst IGNORED_LOGS = new Set(['Angular is running in development mode.']);\n\n/**\n * Custom implementation of the Angular Console service that filters out specific log messages.\n *\n * This class extends the internal Angular `ɵConsole` class to provide customized logging behavior.\n * It overrides the `log` method to suppress logs that match certain predefined messages.\n */\nexport class Console extends ɵConsole {\n  /**\n   * Logs a message to the console if it is not in the set of ignored messages.\n   *\n   * @param message - The message to log to the console.\n   *\n   * This method overrides the `log` method of the `ɵConsole` class. It checks if the\n   * message is in the `IGNORED_LOGS` set. If it is not, it delegates the logging to\n   * the parent class's `log` method. Otherwise, the message is suppressed.\n   */\n  override log(message: string): void {\n    if (!IGNORED_LOGS.has(message)) {\n      super.log(message);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type { SerializableRouteTreeNode } from './routes/route-tree';\nimport { AngularBootstrap } from './utils/ng';\n\n/**\n * Represents a server asset stored in the manifest.\n */\nexport interface ServerAsset {\n  /**\n   * Retrieves the text content of the asset.\n   *\n   * @returns A promise that resolves to the asset's content as a string.\n   */\n  text: () => Promise<string>;\n\n  /**\n   * A hash string representing the asset's content.\n   */\n  hash: string;\n\n  /**\n   * The size of the asset's content in bytes.\n   */\n  size: number;\n}\n\n/**\n * Represents the exports of an Angular server application entry point.\n */\nexport interface EntryPointExports {\n  /**\n   * A reference to the function that creates an Angular server application instance.\n   *\n   * @remarks The return type is `unknown` to prevent circular dependency issues.\n   */\n  ɵgetOrCreateAngularServerApp: () => unknown;\n\n  /**\n   * A reference to the function that destroys the `AngularServerApp` instance.\n   */\n  ɵdestroyAngularServerApp: () => void;\n}\n\n/**\n * Manifest for the Angular server application engine, defining entry points.\n */\nexport interface AngularAppEngineManifest {\n  /**\n   * A readonly record of entry points for the server application.\n   * Each entry consists of:\n   * - `key`: The url segment for the entry point.\n   * - `value`: A function that returns a promise resolving to an object of type `EntryPointExports`.\n   */\n  readonly entryPoints: Readonly<Record<string, (() => Promise<EntryPointExports>) | undefined>>;\n\n  /**\n   * The base path for the server application.\n   * This is used to determine the root path of the application.\n   */\n  readonly basePath: string;\n\n  /**\n   * A readonly record mapping supported locales to their respective entry-point paths.\n   * Each entry consists of:\n   * - `key`: The locale identifier (e.g., 'en', 'fr').\n   * - `value`: The url segment associated with that locale.\n   */\n  readonly supportedLocales: Readonly<Record<string, string | undefined>>;\n}\n\n/**\n * Manifest for a specific Angular server application, defining assets and bootstrap logic.\n */\nexport interface AngularAppManifest {\n  /**\n   * The base href for the application.\n   * This is used to determine the root path of the application.\n   */\n  readonly baseHref: string;\n\n  /**\n   * A readonly record of assets required by the server application.\n   * Each entry consists of:\n   * - `key`: The path of the asset.\n   * - `value`: An object of type `ServerAsset`.\n   */\n  readonly assets: Readonly<Record<string, ServerAsset | undefined>>;\n\n  /**\n   * The bootstrap mechanism for the server application.\n   * A function that returns a promise that resolves to an `NgModule` or a function\n   * returning a promise that resolves to an `ApplicationRef`.\n   */\n  readonly bootstrap: () => Promise<AngularBootstrap>;\n\n  /**\n   * Indicates whether critical CSS should be inlined into the HTML.\n   * If set to `true`, critical CSS will be inlined for faster page rendering.\n   */\n  readonly inlineCriticalCss?: boolean;\n\n  /**\n   * The route tree representation for the routing configuration of the application.\n   * This represents the routing information of the application, mapping route paths to their corresponding metadata.\n   * It is used for route matching and navigation within the server application.\n   */\n  readonly routes?: SerializableRouteTreeNode;\n\n  /**\n   * An optional string representing the locale or language code to be used for\n   * the application, aiding with localization and rendering content specific to the locale.\n   */\n  readonly locale?: string;\n\n  /**\n   * Maps entry-point names to their corresponding browser bundles and loading strategies.\n   *\n   * - **Key**: The entry-point name, typically the value of `ɵentryName`.\n   * - **Value**: A readonly array of JavaScript bundle paths or `undefined` if no bundles are associated.\n   *\n   * ### Example\n   * ```ts\n   * {\n   *   'src/app/lazy/lazy.ts': ['src/app/lazy/lazy.js']\n   * }\n   * ```\n   */\n  readonly entryPointToBrowserMapping?: Readonly<Record<string, readonly string[] | undefined>>;\n}\n\n/**\n * The Angular app manifest object.\n * This is used internally to store the current Angular app manifest.\n */\nlet angularAppManifest: AngularAppManifest | undefined;\n\n/**\n * Sets the Angular app manifest.\n *\n * @param manifest - The manifest object to set for the Angular application.\n */\nexport function setAngularAppManifest(manifest: AngularAppManifest): void {\n  angularAppManifest = manifest;\n}\n\n/**\n * Gets the Angular app manifest.\n *\n * @returns The Angular app manifest.\n * @throws Will throw an error if the Angular app manifest is not set.\n */\nexport function getAngularAppManifest(): AngularAppManifest {\n  if (!angularAppManifest) {\n    throw new Error(\n      'Angular app manifest is not set. ' +\n        `Please ensure you are using the '@angular/build:application' builder to build your server application.`,\n    );\n  }\n\n  return angularAppManifest;\n}\n\n/**\n * The Angular app engine manifest object.\n * This is used internally to store the current Angular app engine manifest.\n */\nlet angularAppEngineManifest: AngularAppEngineManifest | undefined;\n\n/**\n * Sets the Angular app engine manifest.\n *\n * @param manifest - The engine manifest object to set.\n */\nexport function setAngularAppEngineManifest(manifest: AngularAppEngineManifest): void {\n  angularAppEngineManifest = manifest;\n}\n\n/**\n * Gets the Angular app engine manifest.\n *\n * @returns The Angular app engine manifest.\n * @throws Will throw an error if the Angular app engine manifest is not set.\n */\nexport function getAngularAppEngineManifest(): AngularAppEngineManifest {\n  if (!angularAppEngineManifest) {\n    throw new Error(\n      'Angular app engine manifest is not set. ' +\n        `Please ensure you are using the '@angular/build:application' builder to build your server application.`,\n    );\n  }\n\n  return angularAppEngineManifest;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Removes the trailing slash from a URL if it exists.\n *\n * @param url - The URL string from which to remove the trailing slash.\n * @returns The URL string without a trailing slash.\n *\n * @example\n * ```js\n * stripTrailingSlash('path/'); // 'path'\n * stripTrailingSlash('/path');  // '/path'\n * stripTrailingSlash('/'); // '/'\n * stripTrailingSlash(''); // ''\n * ```\n */\nexport function stripTrailingSlash(url: string): string {\n  // Check if the last character of the URL is a slash\n  return url.length > 1 && url[url.length - 1] === '/' ? url.slice(0, -1) : url;\n}\n\n/**\n * Removes the leading slash from a URL if it exists.\n *\n * @param url - The URL string from which to remove the leading slash.\n * @returns The URL string without a leading slash.\n *\n * @example\n * ```js\n * stripLeadingSlash('/path'); // 'path'\n * stripLeadingSlash('/path/');  // 'path/'\n * stripLeadingSlash('/'); // '/'\n * stripLeadingSlash(''); // ''\n * ```\n */\nexport function stripLeadingSlash(url: string): string {\n  // Check if the first character of the URL is a slash\n  return url.length > 1 && url[0] === '/' ? url.slice(1) : url;\n}\n\n/**\n * Adds a leading slash to a URL if it does not already have one.\n *\n * @param url - The URL string to which the leading slash will be added.\n * @returns The URL string with a leading slash.\n *\n * @example\n * ```js\n * addLeadingSlash('path'); // '/path'\n * addLeadingSlash('/path'); // '/path'\n * ```\n */\nexport function addLeadingSlash(url: string): string {\n  // Check if the URL already starts with a slash\n  return url[0] === '/' ? url : `/${url}`;\n}\n\n/**\n * Adds a trailing slash to a URL if it does not already have one.\n *\n * @param url - The URL string to which the trailing slash will be added.\n * @returns The URL string with a trailing slash.\n *\n * @example\n * ```js\n * addTrailingSlash('path'); // 'path/'\n * addTrailingSlash('path/'); // 'path/'\n * ```\n */\nexport function addTrailingSlash(url: string): string {\n  // Check if the URL already end with a slash\n  return url[url.length - 1] === '/' ? url : `${url}/`;\n}\n\n/**\n * Joins URL parts into a single URL string.\n *\n * This function takes multiple URL segments, normalizes them by removing leading\n * and trailing slashes where appropriate, and then joins them into a single URL.\n *\n * @param parts - The parts of the URL to join. Each part can be a string with or without slashes.\n * @returns The joined URL string, with normalized slashes.\n *\n * @example\n * ```js\n * joinUrlParts('path/', '/to/resource'); // '/path/to/resource'\n * joinUrlParts('/path/', 'to/resource'); // '/path/to/resource'\n * joinUrlParts('', ''); // '/'\n * ```\n */\nexport function joinUrlParts(...parts: string[]): string {\n  const normalizeParts: string[] = [];\n  for (const part of parts) {\n    if (part === '') {\n      // Skip any empty parts\n      continue;\n    }\n\n    let normalizedPart = part;\n    if (part[0] === '/') {\n      normalizedPart = normalizedPart.slice(1);\n    }\n    if (part[part.length - 1] === '/') {\n      normalizedPart = normalizedPart.slice(0, -1);\n    }\n    if (normalizedPart !== '') {\n      normalizeParts.push(normalizedPart);\n    }\n  }\n\n  return addLeadingSlash(normalizeParts.join('/'));\n}\n\n/**\n * Strips `/index.html` from the end of a URL's path, if present.\n *\n * This function is used to convert URLs pointing to an `index.html` file into their directory\n * equivalents. For example, it transforms a URL like `http://www.example.com/page/index.html`\n * into `http://www.example.com/page`.\n *\n * @param url - The URL object to process.\n * @returns A new URL object with `/index.html` removed from the path, if it was present.\n *\n * @example\n * ```typescript\n * const originalUrl = new URL('http://www.example.com/page/index.html');\n * const cleanedUrl = stripIndexHtmlFromURL(originalUrl);\n * console.log(cleanedUrl.href); // Output: 'http://www.example.com/page'\n * ```\n */\nexport function stripIndexHtmlFromURL(url: URL): URL {\n  if (url.pathname.endsWith('/index.html')) {\n    const modifiedURL = new URL(url);\n    // Remove '/index.html' from the pathname\n    modifiedURL.pathname = modifiedURL.pathname.slice(0, /** '/index.html'.length */ -11);\n\n    return modifiedURL;\n  }\n\n  return url;\n}\n\n/**\n * Resolves `*` placeholders in a path template by mapping them to corresponding segments\n * from a base path. This is useful for constructing paths dynamically based on a given base path.\n *\n * The function processes the `toPath` string, replacing each `*` placeholder with\n * the corresponding segment from the `fromPath`. If the `toPath` contains no placeholders,\n * it is returned as-is. Invalid `toPath` formats (not starting with `/`) will throw an error.\n *\n * @param toPath - A path template string that may contain `*` placeholders. Each `*` is replaced\n * by the corresponding segment from the `fromPath`. Static paths (e.g., `/static/path`) are returned\n * directly without placeholder replacement.\n * @param fromPath - A base path string, split into segments, that provides values for\n * replacing `*` placeholders in the `toPath`.\n * @returns A resolved path string with `*` placeholders replaced by segments from the `fromPath`,\n * or the `toPath` returned unchanged if it contains no placeholders.\n *\n * @throws If the `toPath` does not start with a `/`, indicating an invalid path format.\n *\n * @example\n * ```typescript\n * // Example with placeholders resolved\n * const resolvedPath = buildPathWithParams('/*\\/details', '/123/abc');\n * console.log(resolvedPath); // Outputs: '/123/details'\n *\n * // Example with a static path\n * const staticPath = buildPathWithParams('/static/path', '/base/unused');\n * console.log(staticPath); // Outputs: '/static/path'\n * ```\n */\nexport function buildPathWithParams(toPath: string, fromPath: string): string {\n  if (toPath[0] !== '/') {\n    throw new Error(`Invalid toPath: The string must start with a '/'. Received: '${toPath}'`);\n  }\n\n  if (fromPath[0] !== '/') {\n    throw new Error(`Invalid fromPath: The string must start with a '/'. Received: '${fromPath}'`);\n  }\n\n  if (!toPath.includes('/*')) {\n    return toPath;\n  }\n\n  const fromPathParts = fromPath.split('/');\n  const toPathParts = toPath.split('/');\n  const resolvedParts = toPathParts.map((part, index) =>\n    toPathParts[index] === '*' ? fromPathParts[index] : part,\n  );\n\n  return joinUrlParts(...resolvedParts);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { APP_BASE_HREF, PlatformLocation } from '@angular/common';\nimport {\n  ApplicationRef,\n  type PlatformRef,\n  type StaticProvider,\n  type Type,\n  ɵConsole,\n} from '@angular/core';\nimport {\n  INITIAL_CONFIG,\n  ɵSERVER_CONTEXT as SERVER_CONTEXT,\n  platformServer,\n  ɵrenderInternal as renderInternal,\n} from '@angular/platform-server';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Console } from '../console';\nimport { joinUrlParts, stripIndexHtmlFromURL } from './url';\n\n/**\n * Represents the bootstrap mechanism for an Angular application.\n *\n * This type can either be:\n * - A reference to an Angular component or module (`Type<unknown>`) that serves as the root of the application.\n * - A function that returns a `Promise<ApplicationRef>`, which resolves with the root application reference.\n */\nexport type AngularBootstrap = Type<unknown> | (() => Promise<ApplicationRef>);\n\n/**\n * Renders an Angular application or module to an HTML string.\n *\n * This function determines whether the provided `bootstrap` value is an Angular module\n * or a bootstrap function and invokes the appropriate rendering method (`renderModule` or `renderApplication`).\n *\n * @param html - The initial HTML document content.\n * @param bootstrap - An Angular module type or a function returning a promise that resolves to an `ApplicationRef`.\n * @param url - The application URL, used for route-based rendering in SSR.\n * @param platformProviders - An array of platform providers for the rendering process.\n * @param serverContext - A string representing the server context, providing additional metadata for SSR.\n * @returns A promise resolving to an object containing:\n *          - `hasNavigationError`: Indicates if a navigation error occurred.\n *          - `redirectTo`: (Optional) The redirect URL if a navigation redirect occurred.\n *          - `content`: A function returning a promise that resolves to the rendered HTML string.\n */\nexport async function renderAngular(\n  html: string,\n  bootstrap: AngularBootstrap,\n  url: URL,\n  platformProviders: StaticProvider[],\n  serverContext: string,\n): Promise<{ hasNavigationError: boolean; redirectTo?: string; content: () => Promise<string> }> {\n  // A request to `http://www.example.com/page/index.html` will render the Angular route corresponding to `http://www.example.com/page`.\n  const urlToRender = stripIndexHtmlFromURL(url).toString();\n  const platformRef = platformServer([\n    {\n      provide: INITIAL_CONFIG,\n      useValue: {\n        url: urlToRender,\n        document: html,\n      },\n    },\n    {\n      provide: SERVER_CONTEXT,\n      useValue: serverContext,\n    },\n    {\n      // An Angular Console Provider that does not print a set of predefined logs.\n      provide: ɵConsole,\n      // Using `useClass` would necessitate decorating `Console` with `@Injectable`,\n      // which would require switching from `ts_library` to `ng_module`. This change\n      // would also necessitate various patches of `@angular/bazel` to support ESM.\n      useFactory: () => new Console(),\n    },\n    ...platformProviders,\n  ]);\n\n  let redirectTo: string | undefined;\n  let hasNavigationError = true;\n\n  try {\n    let applicationRef: ApplicationRef;\n    if (isNgModule(bootstrap)) {\n      const moduleRef = await platformRef.bootstrapModule(bootstrap);\n      applicationRef = moduleRef.injector.get(ApplicationRef);\n    } else {\n      applicationRef = await bootstrap();\n    }\n\n    // Block until application is stable.\n    await applicationRef.whenStable();\n\n    // TODO(alanagius): Find a way to avoid rendering here especially for redirects as any output will be discarded.\n    const envInjector = applicationRef.injector;\n    const routerIsProvided = !!envInjector.get(ActivatedRoute, null);\n    const router = envInjector.get(Router);\n    const lastSuccessfulNavigation = router.lastSuccessfulNavigation;\n\n    if (!routerIsProvided) {\n      hasNavigationError = false;\n    } else if (lastSuccessfulNavigation?.finalUrl) {\n      hasNavigationError = false;\n\n      const { finalUrl, initialUrl } = lastSuccessfulNavigation;\n      const finalUrlStringified = finalUrl.toString();\n\n      if (initialUrl.toString() !== finalUrlStringified) {\n        const baseHref =\n          envInjector.get(APP_BASE_HREF, null, { optional: true }) ??\n          envInjector.get(PlatformLocation).getBaseHrefFromDOM();\n\n        redirectTo = joinUrlParts(baseHref, finalUrlStringified);\n      }\n    }\n\n    return {\n      hasNavigationError,\n      redirectTo,\n      content: () =>\n        new Promise<string>((resolve, reject) => {\n          // Defer rendering to the next event loop iteration to avoid blocking, as most operations in `renderInternal` are synchronous.\n          setTimeout(() => {\n            renderInternal(platformRef, applicationRef)\n              .then(resolve)\n              .catch(reject)\n              .finally(() => void asyncDestroyPlatform(platformRef));\n          }, 0);\n        }),\n    };\n  } catch (error) {\n    await asyncDestroyPlatform(platformRef);\n\n    throw error;\n  } finally {\n    if (hasNavigationError || redirectTo) {\n      void asyncDestroyPlatform(platformRef);\n    }\n  }\n}\n\n/**\n * Type guard to determine if a given value is an Angular module.\n * Angular modules are identified by the presence of the `ɵmod` static property.\n * This function helps distinguish between Angular modules and bootstrap functions.\n *\n * @param value - The value to be checked.\n * @returns True if the value is an Angular module (i.e., it has the `ɵmod` property), false otherwise.\n */\nexport function isNgModule(value: AngularBootstrap): value is Type<unknown> {\n  return 'ɵmod' in value;\n}\n\n/**\n * Gracefully destroys the application in a macrotask, allowing pending promises to resolve\n * and surfacing any potential errors to the user.\n *\n * @param platformRef - The platform reference to be destroyed.\n */\nfunction asyncDestroyPlatform(platformRef: PlatformRef): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      if (!platformRef.destroyed) {\n        platformRef.destroy();\n      }\n\n      resolve();\n    }, 0);\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Creates a promise that resolves with the result of the provided `promise` or rejects with an\n * `AbortError` if the `AbortSignal` is triggered before the promise resolves.\n *\n * @param promise - The promise to monitor for completion.\n * @param signal - An `AbortSignal` used to monitor for an abort event. If the signal is aborted,\n *                 the returned promise will reject.\n * @param errorMessagePrefix - A custom message prefix to include in the error message when the operation is aborted.\n * @returns A promise that either resolves with the value of the provided `promise` or rejects with\n *          an `AbortError` if the `AbortSignal` is triggered.\n *\n * @throws {AbortError} If the `AbortSignal` is triggered before the `promise` resolves.\n */\nexport function promiseWithAbort<T>(\n  promise: Promise<T>,\n  signal: AbortSignal,\n  errorMessagePrefix: string,\n): Promise<T> {\n  return new Promise<T>((resolve, reject) => {\n    const abortHandler = () => {\n      reject(\n        new DOMException(`${errorMessagePrefix} was aborted.\\n${signal.reason}`, 'AbortError'),\n      );\n    };\n\n    // Check for abort signal\n    if (signal.aborted) {\n      abortHandler();\n\n      return;\n    }\n\n    signal.addEventListener('abort', abortHandler, { once: true });\n\n    promise\n      .then(resolve)\n      .catch(reject)\n      .finally(() => {\n        signal.removeEventListener('abort', abortHandler);\n      });\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EnvironmentProviders,\n  InjectionToken,\n  Provider,\n  Type,\n  inject,\n  makeEnvironmentProviders,\n  provideEnvironmentInitializer,\n} from '@angular/core';\nimport { provideServerRendering as provideServerRenderingPlatformServer } from '@angular/platform-server';\nimport { type DefaultExport, ROUTES, type Route } from '@angular/router';\n\n/**\n * The internal path used for the app shell route.\n * @internal\n */\nconst APP_SHELL_ROUTE = 'ng-app-shell';\n\n/**\n * Identifies a particular kind of `ServerRenderingFeatureKind`.\n * @see {@link ServerRenderingFeature}\n */\nenum ServerRenderingFeatureKind {\n  AppShell,\n  ServerRoutes,\n}\n\n/**\n * Helper type to represent a server routes feature.\n * @see {@link ServerRenderingFeatureKind}\n */\ninterface ServerRenderingFeature<FeatureKind extends ServerRenderingFeatureKind> {\n  ɵkind: FeatureKind;\n  ɵproviders: (Provider | EnvironmentProviders)[];\n}\n\n/**\n * Different rendering modes for server routes.\n * @see {@link withRoutes}\n * @see {@link ServerRoute}\n */\nexport enum RenderMode {\n  /** Server-Side Rendering (SSR) mode, where content is rendered on the server for each request. */\n  Server,\n\n  /** Client-Side Rendering (CSR) mode, where content is rendered on the client side in the browser. */\n  Client,\n\n  /** Static Site Generation (SSG) mode, where content is pre-rendered at build time and served as static files. */\n  Prerender,\n}\n\n/**\n * Defines the fallback strategies for Static Site Generation (SSG) routes when a pre-rendered path is not available.\n * This is particularly relevant for routes with parameterized URLs where some paths might not be pre-rendered at build time.\n * @see {@link ServerRoutePrerenderWithParams}\n */\nexport enum PrerenderFallback {\n  /**\n   * Fallback to Server-Side Rendering (SSR) if the pre-rendered path is not available.\n   * This strategy dynamically generates the page on the server at request time.\n   */\n  Server,\n\n  /**\n   * Fallback to Client-Side Rendering (CSR) if the pre-rendered path is not available.\n   * This strategy allows the page to be rendered on the client side.\n   */\n  Client,\n\n  /**\n   * No fallback; if the path is not pre-rendered, the server will not handle the request.\n   * This means the application will not provide any response for paths that are not pre-rendered.\n   */\n  None,\n}\n\n/**\n * Common interface for server routes, providing shared properties.\n */\nexport interface ServerRouteCommon {\n  /** The path associated with this route. */\n  path: string;\n\n  /** Optional additional headers to include in the response for this route. */\n  headers?: Record<string, string>;\n\n  /** Optional status code to return for this route. */\n  status?: number;\n}\n\n/**\n * A server route that uses Client-Side Rendering (CSR) mode.\n * @see {@link RenderMode}\n */\nexport interface ServerRouteClient extends ServerRouteCommon {\n  /** Specifies that the route uses Client-Side Rendering (CSR) mode. */\n  renderMode: RenderMode.Client;\n}\n\n/**\n * A server route that uses Static Site Generation (SSG) mode.\n * @see {@link RenderMode}\n */\nexport interface ServerRoutePrerender extends Omit<ServerRouteCommon, 'status'> {\n  /** Specifies that the route uses Static Site Generation (SSG) mode. */\n  renderMode: RenderMode.Prerender;\n\n  /** Fallback cannot be specified unless `getPrerenderParams` is used. */\n  fallback?: never;\n}\n\n/**\n * A server route configuration that uses Static Site Generation (SSG) mode, including support for routes with parameters.\n * @see {@link RenderMode}\n * @see {@link ServerRoutePrerender}\n * @see {@link PrerenderFallback}\n */\nexport interface ServerRoutePrerenderWithParams extends Omit<ServerRoutePrerender, 'fallback'> {\n  /**\n   * Optional strategy to use if the SSG path is not pre-rendered.\n   * This is especially relevant for routes with parameterized URLs, where some paths may not be pre-rendered at build time.\n   *\n   * This property determines how to handle requests for paths that are not pre-rendered:\n   * - `PrerenderFallback.Server`: Use Server-Side Rendering (SSR) to dynamically generate the page at request time.\n   * - `PrerenderFallback.Client`: Use Client-Side Rendering (CSR) to fetch and render the page on the client side.\n   * - `PrerenderFallback.None`: No fallback; if the path is not pre-rendered, the server will not handle the request.\n   *\n   * @default `PrerenderFallback.Server` if not provided.\n   */\n  fallback?: PrerenderFallback;\n\n  /**\n   * A function that returns a Promise resolving to an array of objects, each representing a route path with URL parameters.\n   * This function runs in the injector context, allowing access to Angular services and dependencies.\n   *\n   * It also works for catch-all routes (e.g., `/**`), where the parameter name will be `**` and the return value will be\n   * the segments of the path, such as `/foo/bar`. These routes can also be combined, e.g., `/product/:id/**`,\n   * where both a parameterized segment (`:id`) and a catch-all segment (`**`) can be used together to handle more complex paths.\n   *\n   * @returns A Promise resolving to an array where each element is an object with string keys (representing URL parameter names)\n   * and string values (representing the corresponding values for those parameters in the route path).\n   *\n   * @example\n   * ```typescript\n   * export const serverRouteConfig: ServerRoutes[] = [\n   *   {\n   *     path: '/product/:id',\n   *     renderMode: RenderMode.Prerender,\n   *     async getPrerenderParams() {\n   *       const productService = inject(ProductService);\n   *       const ids = await productService.getIds(); // Assuming this returns ['1', '2', '3']\n   *\n   *       return ids.map(id => ({ id })); // Generates paths like: ['product/1', 'product/2', 'product/3']\n   *     },\n   *   },\n   *   {\n   *     path: '/product/:id/**',\n   *     renderMode: RenderMode.Prerender,\n   *     async getPrerenderParams() {\n   *       return [\n   *         { id: '1', '**': 'laptop/3' },\n   *         { id: '2', '**': 'laptop/4' }\n   *       ]; // Generates paths like: ['product/1/laptop/3', 'product/2/laptop/4']\n   *     },\n   *   },\n   * ];\n   * ```\n   */\n  getPrerenderParams: () => Promise<Record<string, string>[]>;\n}\n\n/**\n * A server route that uses Server-Side Rendering (SSR) mode.\n * @see {@link RenderMode}\n */\nexport interface ServerRouteServer extends ServerRouteCommon {\n  /** Specifies that the route uses Server-Side Rendering (SSR) mode. */\n  renderMode: RenderMode.Server;\n}\n\n/**\n * Server route configuration.\n * @see {@link withRoutes}\n */\nexport type ServerRoute =\n  | ServerRouteClient\n  | ServerRoutePrerender\n  | ServerRoutePrerenderWithParams\n  | ServerRouteServer;\n\n/**\n * Configuration value for server routes configuration.\n * @internal\n */\nexport interface ServerRoutesConfig {\n  /**\n   * Defines the route to be used as the app shell.\n   */\n  appShellRoute?: string;\n\n  /** List of server routes for the application. */\n  routes: ServerRoute[];\n}\n\n/**\n * Token for providing the server routes configuration.\n * @internal\n */\nexport const SERVER_ROUTES_CONFIG = new InjectionToken<ServerRoutesConfig>('SERVER_ROUTES_CONFIG');\n\n/**\n * Configures server-side routing for the application.\n *\n * This function registers an array of `ServerRoute` definitions, enabling server-side rendering\n * for specific URL paths. These routes are used to pre-render content on the server, improving\n * initial load performance and SEO.\n *\n * @param routes - An array of `ServerRoute` objects, each defining a server-rendered route.\n * @returns A `ServerRenderingFeature` object configuring server-side routes.\n *\n * @example\n * ```ts\n * import { provideServerRendering, withRoutes, ServerRoute, RenderMode } from '@angular/ssr';\n *\n * const serverRoutes: ServerRoute[] = [\n *   {\n *     route: '', // This renders the \"/\" route on the client (CSR)\n *     renderMode: RenderMode.Client,\n *   },\n *   {\n *     route: 'about', // This page is static, so we prerender it (SSG)\n *     renderMode: RenderMode.Prerender,\n *   },\n *   {\n *     route: 'profile', // This page requires user-specific data, so we use SSR\n *     renderMode: RenderMode.Server,\n *   },\n *   {\n *     route: '**', // All other routes will be rendered on the server (SSR)\n *     renderMode: RenderMode.Server,\n *   },\n * ];\n *\n * provideServerRendering(withRoutes(serverRoutes));\n * ```\n *\n * @see {@link provideServerRendering}\n * @see {@link ServerRoute}\n */\nexport function withRoutes(\n  routes: ServerRoute[],\n): ServerRenderingFeature<ServerRenderingFeatureKind.ServerRoutes> {\n  const config: ServerRoutesConfig = { routes };\n\n  return {\n    ɵkind: ServerRenderingFeatureKind.ServerRoutes,\n    ɵproviders: [\n      {\n        provide: SERVER_ROUTES_CONFIG,\n        useValue: config,\n      },\n    ],\n  };\n}\n\n/**\n * Configures the shell of the application.\n *\n * The app shell is a minimal, static HTML page that is served immediately, while the\n * full Angular application loads in the background. This improves perceived performance\n * by providing instant feedback to the user.\n *\n * This function configures the app shell route, which serves the provided component for\n * requests that do not match any defined server routes.\n *\n * @param component - The Angular component to render for the app shell. Can be a direct\n * component type or a dynamic import function.\n * @returns A `ServerRenderingFeature` object configuring the app shell.\n *\n * @example\n * ```ts\n * import { provideServerRendering, withAppShell, withRoutes } from '@angular/ssr';\n * import { AppShellComponent } from './app-shell.component';\n *\n * provideServerRendering(\n *   withRoutes(serverRoutes),\n *   withAppShell(AppShellComponent)\n * );\n * ```\n *\n * @example\n * ```ts\n * import { provideServerRendering, withAppShell, withRoutes } from '@angular/ssr';\n *\n * provideServerRendering(\n *   withRoutes(serverRoutes),\n *   withAppShell(() =>\n *     import('./app-shell.component').then((m) => m.AppShellComponent)\n *   )\n * );\n * ```\n *\n * @see {@link provideServerRendering}\n * @see {@link https://angular.dev/ecosystem/service-workers/app-shell App shell pattern on Angular.dev}\n */\nexport function withAppShell(\n  component: Type<unknown> | (() => Promise<Type<unknown> | DefaultExport<Type<unknown>>>),\n): ServerRenderingFeature<ServerRenderingFeatureKind.AppShell> {\n  const routeConfig: Route = {\n    path: APP_SHELL_ROUTE,\n  };\n\n  if ('ɵcmp' in component) {\n    routeConfig.component = component as Type<unknown>;\n  } else {\n    routeConfig.loadComponent = component as () => Promise<Type<unknown>>;\n  }\n\n  return {\n    ɵkind: ServerRenderingFeatureKind.AppShell,\n    ɵproviders: [\n      {\n        provide: ROUTES,\n        useValue: routeConfig,\n        multi: true,\n      },\n      provideEnvironmentInitializer(() => {\n        const config = inject(SERVER_ROUTES_CONFIG);\n        config.appShellRoute = APP_SHELL_ROUTE;\n      }),\n    ],\n  };\n}\n\n/**\n * Configures server-side rendering for an Angular application.\n *\n * This function sets up the necessary providers for server-side rendering, including\n * support for server routes and app shell. It combines features configured using\n * `withRoutes` and `withAppShell` to provide a comprehensive server-side rendering setup.\n *\n * @param features - Optional features to configure additional server rendering behaviors.\n * @returns An `EnvironmentProviders` instance with the server-side rendering configuration.\n *\n * @example\n * Basic example of how you can enable server-side rendering in your application\n * when using the `bootstrapApplication` function:\n *\n * ```ts\n * import { bootstrapApplication } from '@angular/platform-browser';\n * import { provideServerRendering, withRoutes, withAppShell } from '@angular/ssr';\n * import { AppComponent } from './app/app.component';\n * import { SERVER_ROUTES } from './app/app.server.routes';\n * import { AppShellComponent } from './app/app-shell.component';\n *\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *      provideServerRendering(\n *         withRoutes(SERVER_ROUTES),\n *         withAppShell(AppShellComponent)\n *      )\n *   ]\n * });\n * ```\n * @see {@link withRoutes} configures server-side routing\n * @see {@link withAppShell} configures the application shell\n */\nexport function provideServerRendering(\n  ...features: ServerRenderingFeature<ServerRenderingFeatureKind>[]\n): EnvironmentProviders {\n  let hasAppShell = false;\n  let hasServerRoutes = false;\n  const providers: (Provider | EnvironmentProviders)[] = [provideServerRenderingPlatformServer()];\n\n  for (const { ɵkind, ɵproviders } of features) {\n    hasAppShell ||= ɵkind === ServerRenderingFeatureKind.AppShell;\n    hasServerRoutes ||= ɵkind === ServerRenderingFeatureKind.ServerRoutes;\n    providers.push(...ɵproviders);\n  }\n\n  if (!hasServerRoutes && hasAppShell) {\n    throw new Error(\n      `Configuration error: found 'withAppShell()' without 'withRoutes()' in the same call to 'provideServerRendering()'.` +\n        `The 'withAppShell()' function requires 'withRoutes()' to be used.`,\n    );\n  }\n\n  return makeEnvironmentProviders(providers);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { addLeadingSlash } from '../utils/url';\nimport { RenderMode } from './route-config';\n\n/**\n * Represents the serialized format of a route tree as an array of node metadata objects.\n * Each entry in the array corresponds to a specific node's metadata within the route tree.\n */\nexport type SerializableRouteTreeNode = ReadonlyArray<RouteTreeNodeMetadata>;\n\n/**\n * Represents metadata for a route tree node, excluding the 'route' path segment.\n */\nexport type RouteTreeNodeMetadataWithoutRoute = Omit<RouteTreeNodeMetadata, 'route'>;\n\n/**\n * Describes metadata associated with a node in the route tree.\n * This metadata includes information such as the route path and optional redirect instructions.\n */\nexport interface RouteTreeNodeMetadata {\n  /**\n   * Optional redirect path associated with this node.\n   * This defines where to redirect if this route is matched.\n   */\n  redirectTo?: string;\n\n  /**\n   * The route path for this node.\n   *\n   * A \"route\" is a URL path or pattern that is used to navigate to different parts of a web application.\n   * It is made up of one or more segments separated by slashes `/`. For instance, in the URL `/products/details/42`,\n   * the full route is `/products/details/42`, with segments `products`, `details`, and `42`.\n   *\n   * Routes define how URLs map to views or components in an application. Each route segment contributes to\n   * the overall path that determines which view or component is displayed.\n   *\n   * - **Static Routes**: These routes have fixed segments. For example, `/about` or `/contact`.\n   * - **Parameterized Routes**: These include dynamic segments that act as placeholders, such as `/users/:id`,\n   *   where `:id` could be any user ID.\n   *\n   * In the context of `RouteTreeNodeMetadata`, the `route` property represents the complete path that this node\n   * in the route tree corresponds to. This path is used to determine how a specific URL in the browser maps to the\n   * structure and content of the application.\n   */\n  route: string;\n\n  /**\n   * Optional status code to return for this route.\n   */\n  status?: number;\n\n  /**\n   * Optional additional headers to include in the response for this route.\n   */\n  headers?: Record<string, string>;\n\n  /**\n   * Specifies the rendering mode used for this route.\n   */\n  renderMode: RenderMode;\n\n  /**\n   * A list of resource that should be preloaded by the browser.\n   */\n  preload?: readonly string[];\n}\n\n/**\n * Represents a node within the route tree structure.\n * Each node corresponds to a route segment and may have associated metadata and child nodes.\n * The `AdditionalMetadata` type parameter allows for extending the node metadata with custom data.\n */\ninterface RouteTreeNode<AdditionalMetadata extends Record<string, unknown>> {\n  /**\n   * A map of child nodes, keyed by their corresponding route segment or wildcard.\n   */\n  children: Map<string, RouteTreeNode<AdditionalMetadata>>;\n\n  /**\n   * Optional metadata associated with this node, providing additional information such as redirects.\n   */\n  metadata?: RouteTreeNodeMetadata & AdditionalMetadata;\n}\n\n/**\n * A route tree implementation that supports efficient route matching, including support for wildcard routes.\n * This structure is useful for organizing and retrieving routes in a hierarchical manner,\n * enabling complex routing scenarios with nested paths.\n *\n * @typeParam AdditionalMetadata - Type of additional metadata that can be associated with route nodes.\n */\nexport class RouteTree<AdditionalMetadata extends Record<string, unknown> = {}> {\n  /**\n   * The root node of the route tree.\n   * All routes are stored and accessed relative to this root node.\n   */\n  private readonly root = this.createEmptyRouteTreeNode();\n\n  /**\n   * Inserts a new route into the route tree.\n   * The route is broken down into segments, and each segment is added to the tree.\n   * Parameterized segments (e.g., :id) are normalized to wildcards (*) for matching purposes.\n   *\n   * @param route - The route path to insert into the tree.\n   * @param metadata - Metadata associated with the route, excluding the route path itself.\n   */\n  insert(route: string, metadata: RouteTreeNodeMetadataWithoutRoute & AdditionalMetadata): void {\n    let node = this.root;\n    const segments = this.getPathSegments(route);\n    const normalizedSegments: string[] = [];\n\n    for (const segment of segments) {\n      // Replace parameterized segments (e.g., :id) with a wildcard (*) for matching\n      const normalizedSegment = segment[0] === ':' ? '*' : segment;\n      let childNode = node.children.get(normalizedSegment);\n      if (!childNode) {\n        childNode = this.createEmptyRouteTreeNode();\n        node.children.set(normalizedSegment, childNode);\n      }\n\n      node = childNode;\n      normalizedSegments.push(normalizedSegment);\n    }\n\n    // At the leaf node, store the full route and its associated metadata\n    node.metadata = {\n      ...metadata,\n      route: addLeadingSlash(normalizedSegments.join('/')),\n    };\n  }\n\n  /**\n   * Matches a given route against the route tree and returns the best matching route's metadata.\n   * The best match is determined by the lowest insertion index, meaning the earliest defined route\n   * takes precedence.\n   *\n   * @param route - The route path to match against the route tree.\n   * @returns The metadata of the best matching route or `undefined` if no match is found.\n   */\n  match(route: string): (RouteTreeNodeMetadata & AdditionalMetadata) | undefined {\n    const segments = this.getPathSegments(route);\n\n    return this.traverseBySegments(segments)?.metadata;\n  }\n\n  /**\n   * Converts the route tree into a serialized format representation.\n   * This method converts the route tree into an array of metadata objects that describe the structure of the tree.\n   * The array represents the routes in a nested manner where each entry includes the route and its associated metadata.\n   *\n   * @returns An array of `RouteTreeNodeMetadata` objects representing the route tree structure.\n   *          Each object includes the `route` and associated metadata of a route.\n   */\n  toObject(): SerializableRouteTreeNode {\n    return Array.from(this.traverse());\n  }\n\n  /**\n   * Constructs a `RouteTree` from an object representation.\n   * This method is used to recreate a `RouteTree` instance from an array of metadata objects.\n   * The array should be in the format produced by `toObject`, allowing for the reconstruction of the route tree\n   * with the same routes and metadata.\n   *\n   * @param value - An array of `RouteTreeNodeMetadata` objects that represent the serialized format of the route tree.\n   *                Each object should include a `route` and its associated metadata.\n   * @returns A new `RouteTree` instance constructed from the provided metadata objects.\n   */\n  static fromObject(value: SerializableRouteTreeNode): RouteTree {\n    const tree = new RouteTree();\n\n    for (const { route, ...metadata } of value) {\n      tree.insert(route, metadata);\n    }\n\n    return tree;\n  }\n\n  /**\n   * A generator function that recursively traverses the route tree and yields the metadata of each node.\n   * This allows for easy and efficient iteration over all nodes in the tree.\n   *\n   * @param node - The current node to start the traversal from. Defaults to the root node of the tree.\n   */\n  *traverse(node = this.root): Generator<RouteTreeNodeMetadata & AdditionalMetadata> {\n    if (node.metadata) {\n      yield node.metadata;\n    }\n\n    for (const childNode of node.children.values()) {\n      yield* this.traverse(childNode);\n    }\n  }\n\n  /**\n   * Extracts the path segments from a given route string.\n   *\n   * @param route - The route string from which to extract segments.\n   * @returns An array of path segments.\n   */\n  private getPathSegments(route: string): string[] {\n    return route.split('/').filter(Boolean);\n  }\n\n  /**\n   * Recursively traverses the route tree from a given node, attempting to match the remaining route segments.\n   * If the node is a leaf node (no more segments to match) and contains metadata, the node is yielded.\n   *\n   * This function prioritizes exact segment matches first, followed by wildcard matches (`*`),\n   * and finally deep wildcard matches (`**`) that consume all segments.\n   *\n   * @param segments - The array of route path segments to match against the route tree.\n   * @param node - The current node in the route tree to start traversal from. Defaults to the root node.\n   * @param currentIndex - The index of the segment in `remainingSegments` currently being matched.\n   * Defaults to `0` (the first segment).\n   *\n   * @returns The node that best matches the remaining segments or `undefined` if no match is found.\n   */\n  private traverseBySegments(\n    segments: string[],\n    node = this.root,\n    currentIndex = 0,\n  ): RouteTreeNode<AdditionalMetadata> | undefined {\n    if (currentIndex >= segments.length) {\n      return node.metadata ? node : node.children.get('**');\n    }\n\n    if (!node.children.size) {\n      return undefined;\n    }\n\n    const segment = segments[currentIndex];\n\n    // 1. Attempt exact match with the current segment.\n    const exactMatch = node.children.get(segment);\n    if (exactMatch) {\n      const match = this.traverseBySegments(segments, exactMatch, currentIndex + 1);\n      if (match) {\n        return match;\n      }\n    }\n\n    // 2. Attempt wildcard match ('*').\n    const wildcardMatch = node.children.get('*');\n    if (wildcardMatch) {\n      const match = this.traverseBySegments(segments, wildcardMatch, currentIndex + 1);\n      if (match) {\n        return match;\n      }\n    }\n\n    // 3. Attempt double wildcard match ('**').\n    return node.children.get('**');\n  }\n\n  /**\n   * Creates an empty route tree node.\n   * This helper function is used during the tree construction.\n   *\n   * @returns A new, empty route tree node.\n   */\n  private createEmptyRouteTreeNode(): RouteTreeNode<AdditionalMetadata> {\n    return {\n      children: new Map(),\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { APP_BASE_HREF, PlatformLocation } from '@angular/common';\nimport {\n  ApplicationRef,\n  Compiler,\n  Injector,\n  runInInjectionContext,\n  ɵConsole,\n  ɵENABLE_ROOT_COMPONENT_BOOTSTRAP,\n} from '@angular/core';\nimport { INITIAL_CONFIG, platformServer } from '@angular/platform-server';\nimport {\n  Route as AngularRoute,\n  Router,\n  ɵloadChildren as loadChildrenHelper,\n} from '@angular/router';\nimport { ServerAssets } from '../assets';\nimport { Console } from '../console';\nimport { AngularAppManifest, getAngularAppManifest } from '../manifest';\nimport { AngularBootstrap, isNgModule } from '../utils/ng';\nimport { promiseWithAbort } from '../utils/promise';\nimport { addTrailingSlash, joinUrlParts, stripLeadingSlash } from '../utils/url';\nimport {\n  PrerenderFallback,\n  RenderMode,\n  SERVER_ROUTES_CONFIG,\n  ServerRoute,\n  ServerRoutesConfig,\n} from './route-config';\nimport { RouteTree, RouteTreeNodeMetadata } from './route-tree';\n\ninterface Route extends AngularRoute {\n  ɵentryName?: string;\n}\n\n/**\n * The maximum number of module preload link elements that should be added for\n * initial scripts.\n */\nconst MODULE_PRELOAD_MAX = 10;\n\n/**\n * Regular expression to match a catch-all route pattern in a URL path,\n * specifically one that ends with '/**'.\n */\nconst CATCH_ALL_REGEXP = /\\/(\\*\\*)$/;\n\n/**\n * Regular expression to match segments preceded by a colon in a string.\n */\nconst URL_PARAMETER_REGEXP = /(?<!\\\\):([^/]+)/g;\n\n/**\n * An set of HTTP status codes that are considered valid for redirect responses.\n */\nconst VALID_REDIRECT_RESPONSE_CODES = new Set([301, 302, 303, 307, 308]);\n\n/**\n * Additional metadata for a server configuration route tree.\n */\ntype ServerConfigRouteTreeAdditionalMetadata = Partial<ServerRoute> & {\n  /** Indicates if the route has been matched with the Angular router routes. */\n  presentInClientRouter?: boolean;\n};\n\n/**\n * Metadata for a server configuration route tree node.\n */\ntype ServerConfigRouteTreeNodeMetadata = RouteTreeNodeMetadata &\n  ServerConfigRouteTreeAdditionalMetadata;\n\n/**\n * Result of extracting routes from an Angular application.\n */\ninterface AngularRouterConfigResult {\n  /**\n   * The base URL for the application.\n   * This is the base href that is used for resolving relative paths within the application.\n   */\n  baseHref: string;\n\n  /**\n   * An array of `RouteTreeNodeMetadata` objects representing the application's routes.\n   *\n   * Each `RouteTreeNodeMetadata` contains details about a specific route, such as its path and any\n   * associated redirection targets. This array is asynchronously generated and\n   * provides information on how routes are structured and resolved.\n   */\n  routes: RouteTreeNodeMetadata[];\n\n  /**\n   * Optional configuration for server routes.\n   *\n   * This property allows you to specify an array of server routes for configuration.\n   * If not provided, the default configuration or behavior will be used.\n   */\n  serverRoutesConfig?: ServerRoute[] | null;\n\n  /**\n   * A list of errors encountered during the route extraction process.\n   */\n  errors: string[];\n\n  /**\n   * The specified route for the app-shell, if configured.\n   */\n  appShellRoute?: string;\n}\n\ntype EntryPointToBrowserMapping = AngularAppManifest['entryPointToBrowserMapping'];\n\n/**\n * Handles a single route within the route tree and yields metadata or errors.\n *\n * @param options - Configuration options for handling the route.\n * @returns An async iterable iterator yielding `RouteTreeNodeMetadata` or an error object.\n */\nasync function* handleRoute(options: {\n  metadata: ServerConfigRouteTreeNodeMetadata;\n  currentRoutePath: string;\n  route: Route;\n  compiler: Compiler;\n  parentInjector: Injector;\n  serverConfigRouteTree?: RouteTree<ServerConfigRouteTreeAdditionalMetadata>;\n  invokeGetPrerenderParams: boolean;\n  includePrerenderFallbackRoutes: boolean;\n  entryPointToBrowserMapping?: EntryPointToBrowserMapping;\n}): AsyncIterableIterator<RouteTreeNodeMetadata | { error: string }> {\n  try {\n    const {\n      metadata,\n      currentRoutePath,\n      route,\n      compiler,\n      parentInjector,\n      serverConfigRouteTree,\n      entryPointToBrowserMapping,\n      invokeGetPrerenderParams,\n      includePrerenderFallbackRoutes,\n    } = options;\n\n    const { redirectTo, loadChildren, loadComponent, children, ɵentryName } = route;\n    if (ɵentryName && loadComponent) {\n      appendPreloadToMetadata(ɵentryName, entryPointToBrowserMapping, metadata);\n    }\n\n    if (metadata.renderMode === RenderMode.Prerender) {\n      yield* handleSSGRoute(\n        serverConfigRouteTree,\n        typeof redirectTo === 'string' ? redirectTo : undefined,\n        metadata,\n        parentInjector,\n        invokeGetPrerenderParams,\n        includePrerenderFallbackRoutes,\n      );\n    } else if (redirectTo !== undefined) {\n      if (metadata.status && !VALID_REDIRECT_RESPONSE_CODES.has(metadata.status)) {\n        yield {\n          error:\n            `The '${metadata.status}' status code is not a valid redirect response code. ` +\n            `Please use one of the following redirect response codes: ${[...VALID_REDIRECT_RESPONSE_CODES.values()].join(', ')}.`,\n        };\n      } else if (typeof redirectTo === 'string') {\n        yield {\n          ...metadata,\n          redirectTo: resolveRedirectTo(metadata.route, redirectTo),\n        };\n      } else {\n        yield metadata;\n      }\n    } else {\n      yield metadata;\n    }\n\n    // Recursively process child routes\n    if (children?.length) {\n      yield* traverseRoutesConfig({\n        ...options,\n        routes: children,\n        parentRoute: currentRoutePath,\n        parentPreloads: metadata.preload,\n      });\n    }\n\n    // Load and process lazy-loaded child routes\n    if (loadChildren) {\n      if (ɵentryName) {\n        appendPreloadToMetadata(ɵentryName, entryPointToBrowserMapping, metadata);\n      }\n\n      const loadedChildRoutes = await loadChildrenHelper(\n        route,\n        compiler,\n        parentInjector,\n      ).toPromise();\n\n      if (loadedChildRoutes) {\n        const { routes: childRoutes, injector = parentInjector } = loadedChildRoutes;\n        yield* traverseRoutesConfig({\n          ...options,\n          routes: childRoutes,\n          parentInjector: injector,\n          parentRoute: currentRoutePath,\n          parentPreloads: metadata.preload,\n        });\n      }\n    }\n  } catch (error) {\n    yield {\n      error: `Error in handleRoute for '${options.currentRoutePath}': ${(error as Error).message}`,\n    };\n  }\n}\n\n/**\n * Traverses an array of route configurations to generate route tree node metadata.\n *\n * This function processes each route and its children, handling redirects, SSG (Static Site Generation) settings,\n * and lazy-loaded routes. It yields route metadata for each route and its potential variants.\n *\n * @param options - The configuration options for traversing routes.\n * @returns An async iterable iterator yielding either route tree node metadata or an error object with an error message.\n */\nasync function* traverseRoutesConfig(options: {\n  routes: Route[];\n  compiler: Compiler;\n  parentInjector: Injector;\n  parentRoute: string;\n  serverConfigRouteTree?: RouteTree<ServerConfigRouteTreeAdditionalMetadata>;\n  invokeGetPrerenderParams: boolean;\n  includePrerenderFallbackRoutes: boolean;\n  entryPointToBrowserMapping?: EntryPointToBrowserMapping;\n  parentPreloads?: readonly string[];\n}): AsyncIterableIterator<RouteTreeNodeMetadata | { error: string }> {\n  const { routes: routeConfigs, parentPreloads, parentRoute, serverConfigRouteTree } = options;\n\n  for (const route of routeConfigs) {\n    const { matcher, path = matcher ? '**' : '' } = route;\n    const currentRoutePath = joinUrlParts(parentRoute, path);\n\n    if (matcher && serverConfigRouteTree) {\n      let foundMatch = false;\n      for (const matchedMetaData of serverConfigRouteTree.traverse()) {\n        if (!matchedMetaData.route.startsWith(currentRoutePath)) {\n          continue;\n        }\n\n        foundMatch = true;\n        matchedMetaData.presentInClientRouter = true;\n\n        if (matchedMetaData.renderMode === RenderMode.Prerender) {\n          yield {\n            error:\n              `The route '${stripLeadingSlash(currentRoutePath)}' is set for prerendering but has a defined matcher. ` +\n              `Routes with matchers cannot use prerendering. Please specify a different 'renderMode'.`,\n          };\n          continue;\n        }\n\n        yield* handleRoute({\n          ...options,\n          currentRoutePath,\n          route,\n          metadata: {\n            ...matchedMetaData,\n            preload: parentPreloads,\n            route: matchedMetaData.route,\n            presentInClientRouter: undefined,\n          },\n        });\n      }\n\n      if (!foundMatch) {\n        yield {\n          error:\n            `The route '${stripLeadingSlash(currentRoutePath)}' has a defined matcher but does not ` +\n            'match any route in the server routing configuration. Please ensure this route is added to the server routing configuration.',\n        };\n      }\n\n      continue;\n    }\n\n    let matchedMetaData: ServerConfigRouteTreeNodeMetadata | undefined;\n    if (serverConfigRouteTree) {\n      matchedMetaData = serverConfigRouteTree.match(currentRoutePath);\n      if (!matchedMetaData) {\n        yield {\n          error:\n            `The '${stripLeadingSlash(currentRoutePath)}' route does not match any route defined in the server routing configuration. ` +\n            'Please ensure this route is added to the server routing configuration.',\n        };\n        continue;\n      }\n\n      matchedMetaData.presentInClientRouter = true;\n    }\n\n    yield* handleRoute({\n      ...options,\n      metadata: {\n        renderMode: RenderMode.Prerender,\n        ...matchedMetaData,\n        preload: parentPreloads,\n        // Match Angular router behavior\n        // ['one', 'two', ''] -> 'one/two/'\n        // ['one', 'two', 'three'] -> 'one/two/three'\n        route: path === '' ? addTrailingSlash(currentRoutePath) : currentRoutePath,\n        presentInClientRouter: undefined,\n      },\n      currentRoutePath,\n      route,\n    });\n  }\n}\n\n/**\n * Appends preload information to the metadata object based on the specified entry-point and chunk mappings.\n *\n * This function extracts preload data for a given entry-point from the provided chunk mappings. It adds the\n * corresponding browser bundles to the metadata's preload list, ensuring no duplicates and limiting the total\n * preloads to a predefined maximum.\n */\nfunction appendPreloadToMetadata(\n  entryName: string,\n  entryPointToBrowserMapping: EntryPointToBrowserMapping,\n  metadata: ServerConfigRouteTreeNodeMetadata,\n): void {\n  const existingPreloads = metadata.preload ?? [];\n  if (!entryPointToBrowserMapping || existingPreloads.length >= MODULE_PRELOAD_MAX) {\n    return;\n  }\n\n  const preload = entryPointToBrowserMapping[entryName];\n  if (!preload?.length) {\n    return;\n  }\n\n  // Merge existing preloads with new ones, ensuring uniqueness and limiting the total to the maximum allowed.\n  const combinedPreloads: Set<string> = new Set(existingPreloads);\n  for (const href of preload) {\n    combinedPreloads.add(href);\n    if (combinedPreloads.size === MODULE_PRELOAD_MAX) {\n      break;\n    }\n  }\n\n  metadata.preload = Array.from(combinedPreloads);\n}\n\n/**\n * Handles SSG (Static Site Generation) routes by invoking `getPrerenderParams` and yielding\n * all parameterized paths, returning any errors encountered.\n *\n * @param serverConfigRouteTree - The tree representing the server's routing setup.\n * @param redirectTo - Optional path to redirect to, if specified.\n * @param metadata - The metadata associated with the route tree node.\n * @param parentInjector - The dependency injection container for the parent route.\n * @param invokeGetPrerenderParams - A flag indicating whether to invoke the `getPrerenderParams` function.\n * @param includePrerenderFallbackRoutes - A flag indicating whether to include fallback routes in the result.\n * @returns An async iterable iterator that yields route tree node metadata for each SSG path or errors.\n */\nasync function* handleSSGRoute(\n  serverConfigRouteTree: RouteTree<ServerConfigRouteTreeAdditionalMetadata> | undefined,\n  redirectTo: string | undefined,\n  metadata: ServerConfigRouteTreeNodeMetadata,\n  parentInjector: Injector,\n  invokeGetPrerenderParams: boolean,\n  includePrerenderFallbackRoutes: boolean,\n): AsyncIterableIterator<RouteTreeNodeMetadata | { error: string }> {\n  if (metadata.renderMode !== RenderMode.Prerender) {\n    throw new Error(\n      `'handleSSGRoute' was called for a route which rendering mode is not prerender.`,\n    );\n  }\n\n  const { route: currentRoutePath, fallback, ...meta } = metadata;\n  const getPrerenderParams = 'getPrerenderParams' in meta ? meta.getPrerenderParams : undefined;\n\n  if ('getPrerenderParams' in meta) {\n    delete meta['getPrerenderParams'];\n  }\n\n  if (redirectTo !== undefined) {\n    meta.redirectTo = resolveRedirectTo(currentRoutePath, redirectTo);\n  }\n\n  const isCatchAllRoute = CATCH_ALL_REGEXP.test(currentRoutePath);\n  if (\n    (isCatchAllRoute && !getPrerenderParams) ||\n    (!isCatchAllRoute && !URL_PARAMETER_REGEXP.test(currentRoutePath))\n  ) {\n    // Route has no parameters\n    yield {\n      ...meta,\n      route: currentRoutePath,\n    };\n\n    return;\n  }\n\n  if (invokeGetPrerenderParams) {\n    if (!getPrerenderParams) {\n      yield {\n        error:\n          `The '${stripLeadingSlash(currentRoutePath)}' route uses prerendering and includes parameters, but 'getPrerenderParams' ` +\n          `is missing. Please define 'getPrerenderParams' function for this route in your server routing configuration ` +\n          `or specify a different 'renderMode'.`,\n      };\n\n      return;\n    }\n\n    if (serverConfigRouteTree) {\n      // Automatically resolve dynamic parameters for nested routes.\n      const catchAllRoutePath = isCatchAllRoute\n        ? currentRoutePath\n        : joinUrlParts(currentRoutePath, '**');\n      const match = serverConfigRouteTree.match(catchAllRoutePath);\n      if (match && match.renderMode === RenderMode.Prerender && !('getPrerenderParams' in match)) {\n        serverConfigRouteTree.insert(catchAllRoutePath, {\n          ...match,\n          presentInClientRouter: true,\n          getPrerenderParams,\n        });\n      }\n    }\n\n    const parameters = await runInInjectionContext(parentInjector, () => getPrerenderParams());\n    try {\n      for (const params of parameters) {\n        const replacer = handlePrerenderParamsReplacement(params, currentRoutePath);\n        const routeWithResolvedParams = currentRoutePath\n          .replace(URL_PARAMETER_REGEXP, replacer)\n          .replace(CATCH_ALL_REGEXP, replacer);\n\n        yield {\n          ...meta,\n          route: routeWithResolvedParams,\n          redirectTo:\n            redirectTo === undefined\n              ? undefined\n              : resolveRedirectTo(routeWithResolvedParams, redirectTo),\n        };\n      }\n    } catch (error) {\n      yield { error: `${(error as Error).message}` };\n\n      return;\n    }\n  }\n\n  // Handle fallback render modes\n  if (\n    includePrerenderFallbackRoutes &&\n    (fallback !== PrerenderFallback.None || !invokeGetPrerenderParams)\n  ) {\n    yield {\n      ...meta,\n      route: currentRoutePath,\n      renderMode: fallback === PrerenderFallback.Client ? RenderMode.Client : RenderMode.Server,\n    };\n  }\n}\n\n/**\n * Creates a replacer function used for substituting parameter placeholders in a route path\n * with their corresponding values provided in the `params` object.\n *\n * @param params - An object mapping parameter names to their string values.\n * @param currentRoutePath - The current route path, used for constructing error messages.\n * @returns A function that replaces a matched parameter placeholder (e.g., ':id') with its corresponding value.\n */\nfunction handlePrerenderParamsReplacement(\n  params: Record<string, string>,\n  currentRoutePath: string,\n): (substring: string, ...args: unknown[]) => string {\n  return (match) => {\n    const parameterName = match.slice(1);\n    const value = params[parameterName];\n    if (typeof value !== 'string') {\n      throw new Error(\n        `The 'getPrerenderParams' function defined for the '${stripLeadingSlash(currentRoutePath)}' route ` +\n          `returned a non-string value for parameter '${parameterName}'. ` +\n          `Please make sure the 'getPrerenderParams' function returns values for all parameters ` +\n          'specified in this route.',\n      );\n    }\n\n    return parameterName === '**' ? `/${value}` : value;\n  };\n}\n\n/**\n * Resolves the `redirectTo` property for a given route.\n *\n * This function processes the `redirectTo` property to ensure that it correctly\n * resolves relative to the current route path. If `redirectTo` is an absolute path,\n * it is returned as is. If it is a relative path, it is resolved based on the current route path.\n *\n * @param routePath - The current route path.\n * @param redirectTo - The target path for redirection.\n * @returns The resolved redirect path as a string.\n */\nfunction resolveRedirectTo(routePath: string, redirectTo: string): string {\n  if (redirectTo[0] === '/') {\n    // If the redirectTo path is absolute, return it as is.\n    return redirectTo;\n  }\n\n  // Resolve relative redirectTo based on the current route path.\n  const segments = routePath.replace(URL_PARAMETER_REGEXP, '*').split('/');\n  segments.pop(); // Remove the last segment to make it relative.\n\n  return joinUrlParts(...segments, redirectTo);\n}\n\n/**\n * Builds a server configuration route tree from the given server routes configuration.\n *\n * @param serverRoutesConfig - The server routes to be used for configuration.\n\n * @returns An object containing:\n * - `serverConfigRouteTree`: A populated `RouteTree` instance, which organizes the server routes\n *   along with their additional metadata.\n * - `errors`: An array of strings that list any errors encountered during the route tree construction\n *   process, such as invalid paths.\n */\nfunction buildServerConfigRouteTree({ routes, appShellRoute }: ServerRoutesConfig): {\n  errors: string[];\n  serverConfigRouteTree: RouteTree<ServerConfigRouteTreeAdditionalMetadata>;\n} {\n  const serverRoutes: ServerRoute[] = [...routes];\n  if (appShellRoute !== undefined) {\n    serverRoutes.unshift({\n      path: appShellRoute,\n      renderMode: RenderMode.Prerender,\n    });\n  }\n\n  const serverConfigRouteTree = new RouteTree<ServerConfigRouteTreeAdditionalMetadata>();\n  const errors: string[] = [];\n\n  for (const { path, ...metadata } of serverRoutes) {\n    if (path[0] === '/') {\n      errors.push(`Invalid '${path}' route configuration: the path cannot start with a slash.`);\n\n      continue;\n    }\n\n    if ('getPrerenderParams' in metadata && (path.includes('/*/') || path.endsWith('/*'))) {\n      errors.push(\n        `Invalid '${path}' route configuration: 'getPrerenderParams' cannot be used with a '*' route.`,\n      );\n      continue;\n    }\n\n    serverConfigRouteTree.insert(path, metadata);\n  }\n\n  return { serverConfigRouteTree, errors };\n}\n\n/**\n * Retrieves routes from the given Angular application.\n *\n * This function initializes an Angular platform, bootstraps the application or module,\n * and retrieves routes from the Angular router configuration. It handles both module-based\n * and function-based bootstrapping. It yields the resulting routes as `RouteTreeNodeMetadata` objects or errors.\n *\n * @param bootstrap - A function that returns a promise resolving to an `ApplicationRef` or an Angular module to bootstrap.\n * @param document - The initial HTML document used for server-side rendering.\n * This document is necessary to render the application on the server.\n * @param url - The URL for server-side rendering. The URL is used to configure `ServerPlatformLocation`. This configuration is crucial\n * for ensuring that API requests for relative paths succeed, which is essential for accurate route extraction.\n * @param invokeGetPrerenderParams - A boolean flag indicating whether to invoke `getPrerenderParams` for parameterized SSG routes\n * to handle prerendering paths. Defaults to `false`.\n * @param includePrerenderFallbackRoutes - A flag indicating whether to include fallback routes in the result. Defaults to `true`.\n * @param entryPointToBrowserMapping - Maps the entry-point name to the associated JavaScript browser bundles.\n *\n * @returns A promise that resolves to an object of type `AngularRouterConfigResult` or errors.\n */\nexport async function getRoutesFromAngularRouterConfig(\n  bootstrap: AngularBootstrap,\n  document: string,\n  url: URL,\n  invokeGetPrerenderParams = false,\n  includePrerenderFallbackRoutes = true,\n  entryPointToBrowserMapping: EntryPointToBrowserMapping | undefined = undefined,\n): Promise<AngularRouterConfigResult> {\n  const { protocol, host } = url;\n\n  // Create and initialize the Angular platform for server-side rendering.\n  const platformRef = platformServer([\n    {\n      provide: INITIAL_CONFIG,\n      useValue: { document, url: `${protocol}//${host}/` },\n    },\n    {\n      // An Angular Console Provider that does not print a set of predefined logs.\n      provide: ɵConsole,\n      // Using `useClass` would necessitate decorating `Console` with `@Injectable`,\n      // which would require switching from `ts_library` to `ng_module`. This change\n      // would also necessitate various patches of `@angular/bazel` to support ESM.\n      useFactory: () => new Console(),\n    },\n    {\n      provide: ɵENABLE_ROOT_COMPONENT_BOOTSTRAP,\n      useValue: false,\n    },\n  ]);\n\n  try {\n    let applicationRef: ApplicationRef;\n\n    if (isNgModule(bootstrap)) {\n      const moduleRef = await platformRef.bootstrapModule(bootstrap);\n      applicationRef = moduleRef.injector.get(ApplicationRef);\n    } else {\n      applicationRef = await bootstrap();\n    }\n\n    const injector = applicationRef.injector;\n    const router = injector.get(Router);\n\n    // Workaround to unblock navigation when `withEnabledBlockingInitialNavigation()` is used.\n    // This is necessary because route extraction disables component bootstrapping.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (router as any).navigationTransitions.afterPreactivation()?.next?.();\n\n    // Wait until the application is stable.\n    await applicationRef.whenStable();\n\n    const errors: string[] = [];\n\n    const rawBaseHref =\n      injector.get(APP_BASE_HREF, null, { optional: true }) ??\n      injector.get(PlatformLocation).getBaseHrefFromDOM();\n    const { pathname: baseHref } = new URL(rawBaseHref, 'http://localhost');\n\n    const compiler = injector.get(Compiler);\n    const serverRoutesConfig = injector.get(SERVER_ROUTES_CONFIG, null, { optional: true });\n    let serverConfigRouteTree: RouteTree<ServerConfigRouteTreeAdditionalMetadata> | undefined;\n\n    if (serverRoutesConfig) {\n      const result = buildServerConfigRouteTree(serverRoutesConfig);\n      serverConfigRouteTree = result.serverConfigRouteTree;\n      errors.push(...result.errors);\n    }\n\n    if (errors.length) {\n      return {\n        baseHref,\n        routes: [],\n        errors,\n      };\n    }\n\n    const routesResults: RouteTreeNodeMetadata[] = [];\n    if (router.config.length) {\n      // Retrieve all routes from the Angular router configuration.\n      const traverseRoutes = traverseRoutesConfig({\n        routes: router.config,\n        compiler,\n        parentInjector: injector,\n        parentRoute: '',\n        serverConfigRouteTree,\n        invokeGetPrerenderParams,\n        includePrerenderFallbackRoutes,\n        entryPointToBrowserMapping,\n      });\n\n      const seenRoutes: Set<string> = new Set();\n      for await (const routeMetadata of traverseRoutes) {\n        if ('error' in routeMetadata) {\n          errors.push(routeMetadata.error);\n          continue;\n        }\n\n        // If a result already exists for the exact same route, subsequent matches should be ignored.\n        // This aligns with Angular's app router behavior, which prioritizes the first route.\n        const routePath = routeMetadata.route;\n        if (!seenRoutes.has(routePath)) {\n          routesResults.push(routeMetadata);\n          seenRoutes.add(routePath);\n        }\n      }\n\n      // This timeout is necessary to prevent 'adev' from hanging in production builds.\n      // The exact cause is unclear, but removing it leads to the issue.\n      await new Promise((resolve) => setTimeout(resolve, 0));\n\n      if (serverConfigRouteTree) {\n        for (const { route, presentInClientRouter } of serverConfigRouteTree.traverse()) {\n          if (presentInClientRouter || route.endsWith('/**')) {\n            // Skip if matched or it's the catch-all route.\n            continue;\n          }\n\n          errors.push(\n            `The '${stripLeadingSlash(route)}' server route does not match any routes defined in the Angular ` +\n              `routing configuration (typically provided as a part of the 'provideRouter' call). ` +\n              'Please make sure that the mentioned server route is present in the Angular routing configuration.',\n          );\n        }\n      }\n    } else {\n      const rootRouteMetadata = serverConfigRouteTree?.match('') ?? {\n        route: '',\n        renderMode: RenderMode.Prerender,\n      };\n\n      routesResults.push({\n        ...rootRouteMetadata,\n        // Matched route might be `/*` or `/**`, which would make Angular serve all routes rather than just `/`.\n        // So we limit to just `/` for the empty app router case.\n        route: '',\n      });\n    }\n\n    return {\n      baseHref,\n      routes: routesResults,\n      errors,\n      appShellRoute: serverRoutesConfig?.appShellRoute,\n    };\n  } finally {\n    platformRef.destroy();\n  }\n}\n\n/**\n * Asynchronously extracts routes from the Angular application configuration\n * and creates a `RouteTree` to manage server-side routing.\n *\n * @param options - An object containing the following options:\n *  - `url`: The URL for server-side rendering. The URL is used to configure `ServerPlatformLocation`. This configuration is crucial\n *     for ensuring that API requests for relative paths succeed, which is essential for accurate route extraction.\n *     See:\n *      - https://github.com/angular/angular/blob/d608b857c689d17a7ffa33bbb510301014d24a17/packages/platform-server/src/location.ts#L51\n *      - https://github.com/angular/angular/blob/6882cc7d9eed26d3caeedca027452367ba25f2b9/packages/platform-server/src/http.ts#L44\n *  - `manifest`: An optional `AngularAppManifest` that contains the application's routing and configuration details.\n *     If not provided, the default manifest is retrieved using `getAngularAppManifest()`.\n *  - `invokeGetPrerenderParams`: A boolean flag indicating whether to invoke `getPrerenderParams` for parameterized SSG routes\n *     to handle prerendering paths. Defaults to `false`.\n *  - `includePrerenderFallbackRoutes`: A flag indicating whether to include fallback routes in the result. Defaults to `true`.\n *  - `signal`: An optional `AbortSignal` that can be used to abort the operation.\n *\n * @returns A promise that resolves to an object containing:\n *  - `routeTree`: A populated `RouteTree` containing all extracted routes from the Angular application.\n *  - `appShellRoute`: The specified route for the app-shell, if configured.\n *  - `errors`: An array of strings representing any errors encountered during the route extraction process.\n */\nexport function extractRoutesAndCreateRouteTree(options: {\n  url: URL;\n  manifest?: AngularAppManifest;\n  invokeGetPrerenderParams?: boolean;\n  includePrerenderFallbackRoutes?: boolean;\n  signal?: AbortSignal;\n}): Promise<{ routeTree: RouteTree; appShellRoute?: string; errors: string[] }> {\n  const {\n    url,\n    manifest = getAngularAppManifest(),\n    invokeGetPrerenderParams = false,\n    includePrerenderFallbackRoutes = true,\n    signal,\n  } = options;\n\n  async function extract(): Promise<{\n    appShellRoute: string | undefined;\n    routeTree: RouteTree<{}>;\n    errors: string[];\n  }> {\n    const routeTree = new RouteTree();\n    const document = await new ServerAssets(manifest).getIndexServerHtml().text();\n    const bootstrap = await manifest.bootstrap();\n    const { baseHref, appShellRoute, routes, errors } = await getRoutesFromAngularRouterConfig(\n      bootstrap,\n      document,\n      url,\n      invokeGetPrerenderParams,\n      includePrerenderFallbackRoutes,\n      manifest.entryPointToBrowserMapping,\n    );\n\n    for (const { route, ...metadata } of routes) {\n      if (metadata.redirectTo !== undefined) {\n        metadata.redirectTo = joinUrlParts(baseHref, metadata.redirectTo);\n      }\n\n      // Remove undefined fields\n      // Helps avoid unnecessary test updates\n      for (const [key, value] of Object.entries(metadata)) {\n        if (value === undefined) {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          delete (metadata as any)[key];\n        }\n      }\n\n      const fullRoute = joinUrlParts(baseHref, route);\n      routeTree.insert(fullRoute, metadata);\n    }\n\n    return {\n      appShellRoute,\n      routeTree,\n      errors,\n    };\n  }\n\n  return signal ? promiseWithAbort(extract(), signal, 'Routes extraction') : extract();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Defines a handler function type for transforming HTML content.\n * This function receives an object with the HTML to be processed.\n *\n * @param ctx - An object containing the URL and HTML content to be transformed.\n * @returns The transformed HTML as a string or a promise that resolves to the transformed HTML.\n */\ntype HtmlTransformHandler = (ctx: { url: URL; html: string }) => string | Promise<string>;\n\n/**\n * Defines the names of available hooks for registering and triggering custom logic within the application.\n */\ntype HookName = keyof HooksMapping;\n\n/**\n * Mapping of hook names to their corresponding handler types.\n */\ninterface HooksMapping {\n  'html:transform:pre': HtmlTransformHandler;\n}\n\n/**\n * Manages a collection of hooks and provides methods to register and execute them.\n * Hooks are functions that can be invoked with specific arguments to allow modifications or enhancements.\n */\nexport class Hooks {\n  /**\n   * A map of hook names to arrays of hook functions.\n   * Each hook name can have multiple associated functions, which are executed in sequence.\n   */\n  private readonly store = new Map<HookName, Function[]>();\n\n  /**\n   * Executes all hooks associated with the specified name, passing the given argument to each hook function.\n   * The hooks are invoked sequentially, and the argument may be modified by each hook.\n   *\n   * @template Hook - The type of the hook name. It should be one of the keys of `HooksMapping`.\n   * @param name - The name of the hook whose functions will be executed.\n   * @param context - The input value to be passed to each hook function. The value is mutated by each hook function.\n   * @returns A promise that resolves once all hook functions have been executed.\n   *\n   * @example\n   * ```typescript\n   * const hooks = new Hooks();\n   * hooks.on('html:transform:pre', async (ctx) => {\n   *   ctx.html = ctx.html.replace(/foo/g, 'bar');\n   *   return ctx.html;\n   * });\n   * const result = await hooks.run('html:transform:pre', { html: '<div>foo</div>' });\n   * console.log(result); // '<div>bar</div>'\n   * ```\n   * @internal\n   */\n  async run<Hook extends keyof HooksMapping>(\n    name: Hook,\n    context: Parameters<HooksMapping[Hook]>[0],\n  ): Promise<Awaited<ReturnType<HooksMapping[Hook]>>> {\n    const hooks = this.store.get(name);\n    switch (name) {\n      case 'html:transform:pre': {\n        if (!hooks) {\n          return context.html as Awaited<ReturnType<HooksMapping[Hook]>>;\n        }\n\n        const ctx = { ...context };\n        for (const hook of hooks) {\n          ctx.html = await hook(ctx);\n        }\n\n        return ctx.html as Awaited<ReturnType<HooksMapping[Hook]>>;\n      }\n      default:\n        throw new Error(`Running hook \"${name}\" is not supported.`);\n    }\n  }\n\n  /**\n   * Registers a new hook function under the specified hook name.\n   * This function should be a function that takes an argument of type `T` and returns a `string` or `Promise<string>`.\n   *\n   * @template Hook - The type of the hook name. It should be one of the keys of `HooksMapping`.\n   * @param name - The name of the hook under which the function will be registered.\n   * @param handler - A function to be executed when the hook is triggered. The handler will be called with an argument\n   *                  that may be modified by the hook functions.\n   *\n   * @remarks\n   * - If there are existing handlers registered under the given hook name, the new handler will be added to the list.\n   * - If no handlers are registered under the given hook name, a new list will be created with the handler as its first element.\n   *\n   * @example\n   * ```typescript\n   * hooks.on('html:transform:pre', async (ctx) => {\n   *   return ctx.html.replace(/foo/g, 'bar');\n   * });\n   * ```\n   */\n  on<Hook extends HookName>(name: Hook, handler: HooksMapping[Hook]): void {\n    const hooks = this.store.get(name);\n    if (hooks) {\n      hooks.push(handler);\n    } else {\n      this.store.set(name, [handler]);\n    }\n  }\n\n  /**\n   * Checks if there are any hooks registered under the specified name.\n   *\n   * @param name - The name of the hook to check.\n   * @returns `true` if there are hooks registered under the specified name, otherwise `false`.\n   */\n  has(name: HookName): boolean {\n    return !!this.store.get(name)?.length;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport { AngularAppManifest } from '../manifest';\nimport { stripIndexHtmlFromURL } from '../utils/url';\nimport { extractRoutesAndCreateRouteTree } from './ng-routes';\nimport { RouteTree, RouteTreeNodeMetadata } from './route-tree';\n\n/**\n * Manages the application's server routing logic by building and maintaining a route tree.\n *\n * This class is responsible for constructing the route tree from the Angular application\n * configuration and using it to match incoming requests to the appropriate routes.\n */\nexport class ServerRouter {\n  /**\n   * Creates an instance of the `ServerRouter`.\n   *\n   * @param routeTree - An instance of `RouteTree` that holds the routing information.\n   * The `RouteTree` is used to match request URLs to the appropriate route metadata.\n   */\n  private constructor(private readonly routeTree: RouteTree) {}\n\n  /**\n   * Static property to track the ongoing build promise.\n   */\n  static #extractionPromise: Promise<ServerRouter> | undefined;\n\n  /**\n   * Creates or retrieves a `ServerRouter` instance based on the provided manifest and URL.\n   *\n   * If the manifest contains pre-built routes, a new `ServerRouter` is immediately created.\n   * Otherwise, it builds the router by extracting routes from the Angular configuration\n   * asynchronously. This method ensures that concurrent builds are prevented by re-using\n   * the same promise.\n   *\n   * @param manifest - An instance of `AngularAppManifest` that contains the route information.\n   * @param url - The URL for server-side rendering. The URL is needed to configure `ServerPlatformLocation`.\n   * This is necessary to ensure that API requests for relative paths succeed, which is crucial for correct route extraction.\n   * [Reference](https://github.com/angular/angular/blob/d608b857c689d17a7ffa33bbb510301014d24a17/packages/platform-server/src/location.ts#L51)\n   * @returns A promise resolving to a `ServerRouter` instance.\n   */\n  static from(manifest: AngularAppManifest, url: URL): Promise<ServerRouter> {\n    if (manifest.routes) {\n      const routeTree = RouteTree.fromObject(manifest.routes);\n\n      return Promise.resolve(new ServerRouter(routeTree));\n    }\n\n    // Create and store a new promise for the build process.\n    // This prevents concurrent builds by re-using the same promise.\n    ServerRouter.#extractionPromise ??= extractRoutesAndCreateRouteTree({ url, manifest })\n      .then(({ routeTree, errors }) => {\n        if (errors.length > 0) {\n          throw new Error(\n            'Error(s) occurred while extracting routes:\\n' +\n              errors.map((error) => `- ${error}`).join('\\n'),\n          );\n        }\n\n        return new ServerRouter(routeTree);\n      })\n      .finally(() => {\n        ServerRouter.#extractionPromise = undefined;\n      });\n\n    return ServerRouter.#extractionPromise;\n  }\n\n  /**\n   * Matches a request URL against the route tree to retrieve route metadata.\n   *\n   * This method strips 'index.html' from the URL if it is present and then attempts\n   * to find a match in the route tree. If a match is found, it returns the associated\n   * route metadata; otherwise, it returns `undefined`.\n   *\n   * @param url - The URL to be matched against the route tree.\n   * @returns The metadata for the matched route or `undefined` if no match is found.\n   */\n  match(url: URL): RouteTreeNodeMetadata | undefined {\n    // Strip 'index.html' from URL if present.\n    // A request to `http://www.example.com/page/index.html` will render the Angular route corresponding to `http://www.example.com/page`.\n    const { pathname } = stripIndexHtmlFromURL(url);\n\n    return this.routeTree.match(decodeURIComponent(pathname));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Generates a SHA-256 hash of the provided string.\n *\n * @param data - The input string to be hashed.\n * @returns A promise that resolves to the SHA-256 hash of the input,\n * represented as a hexadecimal string.\n */\nexport async function sha256(data: string): Promise<string> {\n  const encodedData = new TextEncoder().encode(data);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', encodedData);\n  const hashParts: string[] = [];\n\n  for (const h of new Uint8Array(hashBuffer)) {\n    hashParts.push(h.toString(16).padStart(2, '0'));\n  }\n\n  return hashParts.join('');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport Beasties from '../../third_party/beasties';\n\n/**\n * <PERSON><PERSON> used to extract the media query set by Beasties in an `onload` handler.\n */\nconst MEDIA_SET_HANDLER_PATTERN = /^this\\.media=[\"'](.*)[\"'];?$/;\n\n/**\n * Name of the attribute used to save the Beasties media query so it can be re-assigned on load.\n */\nconst CSP_MEDIA_ATTR = 'ngCspMedia';\n\n/**\n * <PERSON>ript that dynamically updates the `media` attribute of `<link>` tags based on a custom attribute (`CSP_MEDIA_ATTR`).\n *\n * NOTE:\n * We do not use `document.querySelectorAll('link').forEach((s) => s.addEventListener('load', ...)`\n * because load events are not always triggered reliably on Chrome.\n * See: https://github.com/angular/angular-cli/issues/26932 and https://crbug.com/1521256\n *\n * The script:\n * - Ensures the event target is a `<link>` tag with the `CSP_MEDIA_ATTR` attribute.\n * - Updates the `media` attribute with the value of `CSP_MEDIA_ATTR` and then removes the attribute.\n * - Removes the event listener when all relevant `<link>` tags have been processed.\n * - Uses event capturing (the `true` parameter) since load events do not bubble up the DOM.\n */\nconst LINK_LOAD_SCRIPT_CONTENT = /* @__PURE__ */ (() => `(() => {\n  const CSP_MEDIA_ATTR = '${CSP_MEDIA_ATTR}';\n  const documentElement = document.documentElement;\n\n  // Listener for load events on link tags.\n  const listener = (e) => {\n    const target = e.target;\n    if (\n      !target ||\n      target.tagName !== 'LINK' ||\n      !target.hasAttribute(CSP_MEDIA_ATTR)\n    ) {\n      return;\n    }\n\n    target.media = target.getAttribute(CSP_MEDIA_ATTR);\n    target.removeAttribute(CSP_MEDIA_ATTR);\n\n    if (!document.head.querySelector(\\`link[\\${CSP_MEDIA_ATTR}]\\`)) {\n      documentElement.removeEventListener('load', listener);\n    }\n  };\n\n  documentElement.addEventListener('load', listener, true);\n})();`)();\n\n/** Partial representation of an `HTMLElement`. */\ninterface PartialHTMLElement {\n  getAttribute(name: string): string | null;\n  setAttribute(name: string, value: string): void;\n  hasAttribute(name: string): boolean;\n  removeAttribute(name: string): void;\n  appendChild(child: PartialHTMLElement): void;\n  insertBefore(newNode: PartialHTMLElement, referenceNode?: PartialHTMLElement): void;\n  remove(): void;\n  name: string;\n  textContent: string;\n  tagName: string | null;\n  children: PartialHTMLElement[];\n  next: PartialHTMLElement | null;\n  prev: PartialHTMLElement | null;\n}\n\n/** Partial representation of an HTML `Document`. */\ninterface PartialDocument {\n  head: PartialHTMLElement;\n  createElement(tagName: string): PartialHTMLElement;\n  querySelector(selector: string): PartialHTMLElement | null;\n}\n\n/* eslint-disable @typescript-eslint/no-unsafe-declaration-merging */\n\n// We use Typescript declaration merging because `embedLinkedStylesheet` it's not declared in\n// the `Beasties` types which means that we can't call the `super` implementation.\ninterface BeastiesBase {\n  embedLinkedStylesheet(link: PartialHTMLElement, document: PartialDocument): Promise<unknown>;\n}\nclass BeastiesBase extends Beasties {}\n/* eslint-enable @typescript-eslint/no-unsafe-declaration-merging */\n\nexport class InlineCriticalCssProcessor extends BeastiesBase {\n  private addedCspScriptsDocuments = new WeakSet<PartialDocument>();\n  private documentNonces = new WeakMap<PartialDocument, string | null>();\n\n  constructor(\n    public override readFile: (path: string) => Promise<string>,\n    readonly outputPath?: string,\n  ) {\n    super({\n      logger: {\n        // eslint-disable-next-line no-console\n        warn: (s: string) => console.warn(s),\n        // eslint-disable-next-line no-console\n        error: (s: string) => console.error(s),\n        info: () => {},\n      },\n      logLevel: 'warn',\n      path: outputPath,\n      publicPath: undefined,\n      compress: false,\n      pruneSource: false,\n      reduceInlineStyles: false,\n      mergeStylesheets: false,\n      // Note: if `preload` changes to anything other than `media`, the logic in\n      // `embedLinkedStylesheet` will have to be updated.\n      preload: 'media',\n      noscriptFallback: true,\n      inlineFonts: true,\n    });\n  }\n\n  /**\n   * Override of the Beasties `embedLinkedStylesheet` method\n   * that makes it work with Angular's CSP APIs.\n   */\n  override async embedLinkedStylesheet(\n    link: PartialHTMLElement,\n    document: PartialDocument,\n  ): Promise<unknown> {\n    if (link.getAttribute('media') === 'print' && link.next?.name === 'noscript') {\n      // Workaround for https://github.com/GoogleChromeLabs/critters/issues/64\n      // NB: this is only needed for the webpack based builders.\n      const media = link.getAttribute('onload')?.match(MEDIA_SET_HANDLER_PATTERN);\n      if (media) {\n        link.removeAttribute('onload');\n        link.setAttribute('media', media[1]);\n        link?.next?.remove();\n      }\n    }\n\n    const returnValue = await super.embedLinkedStylesheet(link, document);\n    const cspNonce = this.findCspNonce(document);\n\n    if (cspNonce) {\n      const beastiesMedia = link.getAttribute('onload')?.match(MEDIA_SET_HANDLER_PATTERN);\n\n      if (beastiesMedia) {\n        // If there's a Beasties-generated `onload` handler and the file has an Angular CSP nonce,\n        // we have to remove the handler, because it's incompatible with CSP. We save the value\n        // in a different attribute and we generate a script tag with the nonce that uses\n        // `addEventListener` to apply the media query instead.\n        link.removeAttribute('onload');\n        link.setAttribute(CSP_MEDIA_ATTR, beastiesMedia[1]);\n        this.conditionallyInsertCspLoadingScript(document, cspNonce, link);\n      }\n\n      // Ideally we would hook in at the time Beasties inserts the `style` tags, but there isn't\n      // a way of doing that at the moment so we fall back to doing it any time a `link` tag is\n      // inserted. We mitigate it by only iterating the direct children of the `<head>` which\n      // should be pretty shallow.\n      document.head.children.forEach((child) => {\n        if (child.tagName === 'style' && !child.hasAttribute('nonce')) {\n          child.setAttribute('nonce', cspNonce);\n        }\n      });\n    }\n\n    return returnValue;\n  }\n\n  /**\n   * Finds the CSP nonce for a specific document.\n   */\n  private findCspNonce(document: PartialDocument): string | null {\n    if (this.documentNonces.has(document)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.documentNonces.get(document)!;\n    }\n\n    // HTML attribute are case-insensitive, but the parser used by Beasties is case-sensitive.\n    const nonceElement = document.querySelector('[ngCspNonce], [ngcspnonce]');\n    const cspNonce =\n      nonceElement?.getAttribute('ngCspNonce') || nonceElement?.getAttribute('ngcspnonce') || null;\n\n    this.documentNonces.set(document, cspNonce);\n\n    return cspNonce;\n  }\n\n  /**\n   * Inserts the `script` tag that swaps the critical CSS at runtime,\n   * if one hasn't been inserted into the document already.\n   */\n  private conditionallyInsertCspLoadingScript(\n    document: PartialDocument,\n    nonce: string,\n    link: PartialHTMLElement,\n  ): void {\n    if (this.addedCspScriptsDocuments.has(document)) {\n      return;\n    }\n\n    if (document.head.textContent.includes(LINK_LOAD_SCRIPT_CONTENT)) {\n      // Script was already added during the build.\n      this.addedCspScriptsDocuments.add(document);\n\n      return;\n    }\n\n    const script = document.createElement('script');\n    script.setAttribute('nonce', nonce);\n    script.textContent = LINK_LOAD_SCRIPT_CONTENT;\n    // Prepend the script to the head since it needs to\n    // run as early as possible, before the `link` tags.\n    document.head.insertBefore(script, link);\n    this.addedCspScriptsDocuments.add(document);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Represents a node in the doubly linked list.\n */\ninterface Node<Key, Value> {\n  key: Key;\n  value: Value;\n  prev: Node<Key, Value> | undefined;\n  next: Node<Key, Value> | undefined;\n}\n\n/**\n * A Least Recently Used (LRU) cache implementation.\n *\n * This cache stores a fixed number of key-value pairs, and when the cache exceeds its capacity,\n * the least recently accessed items are evicted.\n *\n * @template Key - The type of the cache keys.\n * @template Value - The type of the cache values.\n */\nexport class LRUCache<Key, Value> {\n  /**\n   * The maximum number of items the cache can hold.\n   */\n  capacity: number;\n\n  /**\n   * Internal storage for the cache, mapping keys to their associated nodes in the linked list.\n   */\n  private readonly cache = new Map<Key, Node<Key, Value>>();\n\n  /**\n   * Head of the doubly linked list, representing the most recently used item.\n   */\n  private head: Node<Key, Value> | undefined;\n\n  /**\n   * Tail of the doubly linked list, representing the least recently used item.\n   */\n  private tail: Node<Key, Value> | undefined;\n\n  /**\n   * Creates a new LRUCache instance.\n   * @param capacity The maximum number of items the cache can hold.\n   */\n  constructor(capacity: number) {\n    this.capacity = capacity;\n  }\n\n  /**\n   * Gets the value associated with the given key.\n   * @param key The key to retrieve the value for.\n   * @returns The value associated with the key, or undefined if the key is not found.\n   */\n  get(key: Key): Value | undefined {\n    const node = this.cache.get(key);\n    if (node) {\n      this.moveToHead(node);\n\n      return node.value;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Puts a key-value pair into the cache.\n   * If the key already exists, the value is updated.\n   * If the cache is full, the least recently used item is evicted.\n   * @param key The key to insert or update.\n   * @param value The value to associate with the key.\n   */\n  put(key: Key, value: Value): void {\n    const cachedNode = this.cache.get(key);\n    if (cachedNode) {\n      // Update existing node\n      cachedNode.value = value;\n      this.moveToHead(cachedNode);\n\n      return;\n    }\n\n    // Create a new node\n    const newNode: Node<Key, Value> = { key, value, prev: undefined, next: undefined };\n    this.cache.set(key, newNode);\n    this.addToHead(newNode);\n\n    if (this.cache.size > this.capacity) {\n      // Evict the LRU item\n      const tail = this.removeTail();\n      if (tail) {\n        this.cache.delete(tail.key);\n      }\n    }\n  }\n\n  /**\n   * Adds a node to the head of the linked list.\n   * @param node The node to add.\n   */\n  private addToHead(node: Node<Key, Value>): void {\n    node.next = this.head;\n    node.prev = undefined;\n\n    if (this.head) {\n      this.head.prev = node;\n    }\n\n    this.head = node;\n\n    if (!this.tail) {\n      this.tail = node;\n    }\n  }\n\n  /**\n   * Removes a node from the linked list.\n   * @param node The node to remove.\n   */\n  private removeNode(node: Node<Key, Value>): void {\n    if (node.prev) {\n      node.prev.next = node.next;\n    } else {\n      this.head = node.next;\n    }\n\n    if (node.next) {\n      node.next.prev = node.prev;\n    } else {\n      this.tail = node.prev;\n    }\n  }\n\n  /**\n   * Moves a node to the head of the linked list.\n   * @param node The node to move.\n   */\n  private moveToHead(node: Node<Key, Value>): void {\n    this.removeNode(node);\n    this.addToHead(node);\n  }\n\n  /**\n   * Removes the tail node from the linked list.\n   * @returns The removed tail node, or undefined if the list is empty.\n   */\n  private removeTail(): Node<Key, Value> | undefined {\n    const node = this.tail;\n    if (node) {\n      this.removeNode(node);\n    }\n\n    return node;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  LOCALE_ID,\n  REQUEST,\n  REQUEST_CONTEXT,\n  RESPONSE_INIT,\n  StaticProvider,\n  ɵresetCompiledComponents,\n} from '@angular/core';\nimport { ServerAssets } from './assets';\nimport { Hooks } from './hooks';\nimport { getAngularAppManifest } from './manifest';\nimport { RenderMode } from './routes/route-config';\nimport { RouteTreeNodeMetadata } from './routes/route-tree';\nimport { ServerRouter } from './routes/router';\nimport { sha256 } from './utils/crypto';\nimport { InlineCriticalCssProcessor } from './utils/inline-critical-css';\nimport { LRUCache } from './utils/lru-cache';\nimport { AngularBootstrap, renderAngular } from './utils/ng';\nimport { promiseWithAbort } from './utils/promise';\nimport { buildPathWithParams, joinUrlParts, stripLeadingSlash } from './utils/url';\n\n/**\n * Maximum number of critical CSS entries the cache can store.\n * This value determines the capacity of the LRU (Least Recently Used) cache, which stores critical CSS for pages.\n */\nconst MAX_INLINE_CSS_CACHE_ENTRIES = 50;\n\n/**\n * A mapping of `RenderMode` enum values to corresponding string representations.\n *\n * This record is used to map each `RenderMode` to a specific string value that represents\n * the server context. The string values are used internally to differentiate\n * between various rendering strategies when processing routes.\n *\n * - `RenderMode.Prerender` maps to `'ssg'` (Static Site Generation).\n * - `RenderMode.Server` maps to `'ssr'` (Server-Side Rendering).\n * - `RenderMode.Client` maps to an empty string `''` (Client-Side Rendering, no server context needed).\n */\nconst SERVER_CONTEXT_VALUE: Record<RenderMode, string> = {\n  [RenderMode.Prerender]: 'ssg',\n  [RenderMode.Server]: 'ssr',\n  [RenderMode.Client]: '',\n};\n\n/**\n * Options for configuring an `AngularServerApp`.\n */\ninterface AngularServerAppOptions {\n  /**\n   * Whether to allow rendering of prerendered routes.\n   *\n   * When enabled, prerendered routes will be served directly. When disabled, they will be\n   * rendered on demand.\n   *\n   * Defaults to `false`.\n   */\n  allowStaticRouteRender?: boolean;\n\n  /**\n   *  Hooks for extending or modifying server behavior.\n   *\n   * This allows customization of the server's rendering process and other lifecycle events.\n   *\n   * If not provided, a new `Hooks` instance is created.\n   */\n  hooks?: Hooks;\n}\n\n/**\n * Represents a locale-specific Angular server application managed by the server application engine.\n *\n * The `AngularServerApp` class handles server-side rendering and asset management for a specific locale.\n */\nexport class AngularServerApp {\n  /**\n   * Whether prerendered routes should be rendered on demand or served directly.\n   *\n   * @see {@link AngularServerAppOptions.allowStaticRouteRender} for more details.\n   */\n  private readonly allowStaticRouteRender: boolean;\n\n  /**\n   * Hooks for extending or modifying server behavior.\n   *\n   * @see {@link AngularServerAppOptions.hooks} for more details.\n   */\n  readonly hooks: Hooks;\n\n  /**\n   * Constructs an instance of `AngularServerApp`.\n   *\n   * @param options Optional configuration options for the server application.\n   */\n  constructor(private readonly options: Readonly<AngularServerAppOptions> = {}) {\n    this.allowStaticRouteRender = this.options.allowStaticRouteRender ?? false;\n    this.hooks = options.hooks ?? new Hooks();\n\n    if (this.manifest.inlineCriticalCss) {\n      this.inlineCriticalCssProcessor = new InlineCriticalCssProcessor((path: string) => {\n        const fileName = path.split('/').pop() ?? path;\n\n        return this.assets.getServerAsset(fileName).text();\n      });\n    }\n  }\n\n  /**\n   * The manifest associated with this server application.\n   */\n  private readonly manifest = getAngularAppManifest();\n\n  /**\n   * An instance of ServerAsset that handles server-side asset.\n   */\n  private readonly assets = new ServerAssets(this.manifest);\n\n  /**\n   * The router instance used for route matching and handling.\n   */\n  private router: ServerRouter | undefined;\n\n  /**\n   * The `inlineCriticalCssProcessor` is responsible for handling critical CSS inlining.\n   */\n  private inlineCriticalCssProcessor: InlineCriticalCssProcessor | undefined;\n\n  /**\n   * The bootstrap mechanism for the server application.\n   */\n  private boostrap: AngularBootstrap | undefined;\n\n  /**\n   * Decorder used to convert a string to a Uint8Array.\n   */\n  private readonly textDecoder = new TextEncoder();\n\n  /**\n   * Cache for storing critical CSS for pages.\n   * Stores a maximum of MAX_INLINE_CSS_CACHE_ENTRIES entries.\n   *\n   * Uses an LRU (Least Recently Used) eviction policy, meaning that when the cache is full,\n   * the least recently accessed page's critical CSS will be removed to make space for new entries.\n   */\n  private readonly criticalCssLRUCache = new LRUCache<string, string>(MAX_INLINE_CSS_CACHE_ENTRIES);\n\n  /**\n   * Handles an incoming HTTP request by serving prerendered content, performing server-side rendering,\n   * or delivering a static file for client-side rendered routes based on the `RenderMode` setting.\n   *\n   * @param request - The HTTP request to handle.\n   * @param requestContext - Optional context for rendering, such as metadata associated with the request.\n   * @returns A promise that resolves to the resulting HTTP response object, or `null` if no matching Angular route is found.\n   *\n   * @remarks A request to `https://www.example.com/page/index.html` will serve or render the Angular route\n   * corresponding to `https://www.example.com/page`.\n   */\n  async handle(request: Request, requestContext?: unknown): Promise<Response | null> {\n    const url = new URL(request.url);\n    this.router ??= await ServerRouter.from(this.manifest, url);\n    const matchedRoute = this.router.match(url);\n\n    if (!matchedRoute) {\n      // Not a known Angular route.\n      return null;\n    }\n\n    const { redirectTo, status, renderMode } = matchedRoute;\n    if (redirectTo !== undefined) {\n      return createRedirectResponse(buildPathWithParams(redirectTo, url.pathname), status);\n    }\n\n    if (renderMode === RenderMode.Prerender) {\n      const response = await this.handleServe(request, matchedRoute);\n      if (response) {\n        return response;\n      }\n    }\n\n    return promiseWithAbort(\n      this.handleRendering(request, matchedRoute, requestContext),\n      request.signal,\n      `Request for: ${request.url}`,\n    );\n  }\n\n  /**\n   * Handles serving a prerendered static asset if available for the matched route.\n   *\n   * This method only supports `GET` and `HEAD` requests.\n   *\n   * @param request - The incoming HTTP request for serving a static page.\n   * @param matchedRoute - The metadata of the matched route for rendering.\n   * If not provided, the method attempts to find a matching route based on the request URL.\n   * @returns A promise that resolves to a `Response` object if the prerendered page is found, or `null`.\n   */\n  private async handleServe(\n    request: Request,\n    matchedRoute: RouteTreeNodeMetadata,\n  ): Promise<Response | null> {\n    const { headers, renderMode } = matchedRoute;\n    if (renderMode !== RenderMode.Prerender) {\n      return null;\n    }\n\n    const { method } = request;\n    if (method !== 'GET' && method !== 'HEAD') {\n      return null;\n    }\n\n    const assetPath = this.buildServerAssetPathFromRequest(request);\n    const {\n      manifest: { locale },\n      assets,\n    } = this;\n\n    if (!assets.hasServerAsset(assetPath)) {\n      return null;\n    }\n\n    const { text, hash, size } = assets.getServerAsset(assetPath);\n    const etag = `\"${hash}\"`;\n\n    return request.headers.get('if-none-match') === etag\n      ? new Response(undefined, { status: 304, statusText: 'Not Modified' })\n      : new Response(await text(), {\n          headers: {\n            'Content-Length': size.toString(),\n            'ETag': etag,\n            'Content-Type': 'text/html;charset=UTF-8',\n            ...(locale !== undefined ? { 'Content-Language': locale } : {}),\n            ...headers,\n          },\n        });\n  }\n\n  /**\n   * Handles the server-side rendering process for the given HTTP request.\n   * This method matches the request URL to a route and performs rendering if a matching route is found.\n   *\n   * @param request - The incoming HTTP request to be processed.\n   * @param matchedRoute - The metadata of the matched route for rendering.\n   * If not provided, the method attempts to find a matching route based on the request URL.\n   * @param requestContext - Optional additional context for rendering, such as request metadata.\n   *\n   * @returns A promise that resolves to the rendered response, or null if no matching route is found.\n   */\n  private async handleRendering(\n    request: Request,\n    matchedRoute: RouteTreeNodeMetadata,\n    requestContext?: unknown,\n  ): Promise<Response | null> {\n    const { renderMode, headers, status, preload } = matchedRoute;\n\n    if (!this.allowStaticRouteRender && renderMode === RenderMode.Prerender) {\n      return null;\n    }\n\n    const url = new URL(request.url);\n    const platformProviders: StaticProvider[] = [];\n\n    const {\n      manifest: { bootstrap, locale },\n      assets,\n    } = this;\n\n    // Initialize the response with status and headers if available.\n    const responseInit = {\n      status,\n      headers: new Headers({\n        'Content-Type': 'text/html;charset=UTF-8',\n        ...(locale !== undefined ? { 'Content-Language': locale } : {}),\n        ...headers,\n      }),\n    };\n\n    if (renderMode === RenderMode.Server) {\n      // Configure platform providers for request and response only for SSR.\n      platformProviders.push(\n        {\n          provide: REQUEST,\n          useValue: request,\n        },\n        {\n          provide: REQUEST_CONTEXT,\n          useValue: requestContext,\n        },\n        {\n          provide: RESPONSE_INIT,\n          useValue: responseInit,\n        },\n      );\n    } else if (renderMode === RenderMode.Client) {\n      // Serve the client-side rendered version if the route is configured for CSR.\n      let html = await this.assets.getServerAsset('index.csr.html').text();\n      html = await this.runTransformsOnHtml(html, url, preload);\n\n      return new Response(html, responseInit);\n    }\n\n    if (locale !== undefined) {\n      platformProviders.push({\n        provide: LOCALE_ID,\n        useValue: locale,\n      });\n    }\n\n    this.boostrap ??= await bootstrap();\n    let html = await assets.getIndexServerHtml().text();\n    html = await this.runTransformsOnHtml(html, url, preload);\n\n    const result = await renderAngular(\n      html,\n      this.boostrap,\n      url,\n      platformProviders,\n      SERVER_CONTEXT_VALUE[renderMode],\n    );\n\n    if (result.hasNavigationError) {\n      return null;\n    }\n\n    if (result.redirectTo) {\n      return createRedirectResponse(result.redirectTo, status);\n    }\n\n    const { inlineCriticalCssProcessor, criticalCssLRUCache, textDecoder } = this;\n\n    // Use a stream to send the response before finishing rendering and inling critical CSS, improving performance via header flushing.\n    const stream = new ReadableStream({\n      async start(controller) {\n        const renderedHtml = await result.content();\n\n        if (!inlineCriticalCssProcessor) {\n          controller.enqueue(textDecoder.encode(renderedHtml));\n          controller.close();\n\n          return;\n        }\n\n        let htmlWithCriticalCss;\n        try {\n          if (renderMode === RenderMode.Server) {\n            const cacheKey = await sha256(renderedHtml);\n            htmlWithCriticalCss = criticalCssLRUCache.get(cacheKey);\n            if (!htmlWithCriticalCss) {\n              htmlWithCriticalCss = await inlineCriticalCssProcessor.process(renderedHtml);\n              criticalCssLRUCache.put(cacheKey, htmlWithCriticalCss);\n            }\n          } else {\n            htmlWithCriticalCss = await inlineCriticalCssProcessor.process(renderedHtml);\n          }\n        } catch (error) {\n          // eslint-disable-next-line no-console\n          console.error(`An error occurred while inlining critical CSS for: ${url}.`, error);\n        }\n\n        controller.enqueue(textDecoder.encode(htmlWithCriticalCss ?? renderedHtml));\n        controller.close();\n      },\n    });\n\n    return new Response(stream, responseInit);\n  }\n\n  /**\n   * Constructs the asset path on the server based on the provided HTTP request.\n   *\n   * This method processes the incoming request URL to derive a path corresponding\n   * to the requested asset. It ensures the path points to the correct file (e.g.,\n   * `index.html`) and removes any base href if it is not part of the asset path.\n   *\n   * @param request - The incoming HTTP request object.\n   * @returns The server-relative asset path derived from the request.\n   */\n  private buildServerAssetPathFromRequest(request: Request): string {\n    let { pathname: assetPath } = new URL(request.url);\n    if (!assetPath.endsWith('/index.html')) {\n      // Append \"index.html\" to build the default asset path.\n      assetPath = joinUrlParts(assetPath, 'index.html');\n    }\n\n    const { baseHref } = this.manifest;\n    // Check if the asset path starts with the base href and the base href is not (`/` or ``).\n    if (baseHref.length > 1 && assetPath.startsWith(baseHref)) {\n      // Remove the base href from the start of the asset path to align with server-asset expectations.\n      assetPath = assetPath.slice(baseHref.length);\n    }\n\n    return stripLeadingSlash(assetPath);\n  }\n\n  /**\n   * Runs the registered transform hooks on the given HTML content.\n   *\n   * @param html - The raw HTML content to be transformed.\n   * @param url - The URL associated with the HTML content, used for context during transformations.\n   * @param preload - An array of URLs representing the JavaScript resources to preload.\n   * @returns A promise that resolves to the transformed HTML string.\n   */\n  private async runTransformsOnHtml(\n    html: string,\n    url: URL,\n    preload: readonly string[] | undefined,\n  ): Promise<string> {\n    if (this.hooks.has('html:transform:pre')) {\n      html = await this.hooks.run('html:transform:pre', { html, url });\n    }\n\n    if (preload?.length) {\n      html = appendPreloadHintsToHtml(html, preload);\n    }\n\n    return html;\n  }\n}\n\nlet angularServerApp: AngularServerApp | undefined;\n\n/**\n * Retrieves or creates an instance of `AngularServerApp`.\n * - If an instance of `AngularServerApp` already exists, it will return the existing one.\n * - If no instance exists, it will create a new one with the provided options.\n *\n * @param options Optional configuration options for the server application.\n *\n * @returns The existing or newly created instance of `AngularServerApp`.\n */\nexport function getOrCreateAngularServerApp(\n  options?: Readonly<AngularServerAppOptions>,\n): AngularServerApp {\n  return (angularServerApp ??= new AngularServerApp(options));\n}\n\n/**\n * Destroys the existing `AngularServerApp` instance, releasing associated resources and resetting the\n * reference to `undefined`.\n *\n * This function is primarily used to enable the recreation of the `AngularServerApp` instance,\n * typically when server configuration or application state needs to be refreshed.\n */\nexport function destroyAngularServerApp(): void {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    // Need to clean up GENERATED_COMP_IDS map in `@angular/core`.\n    // Otherwise an incorrect component ID generation collision detected warning will be displayed in development.\n    // See: https://github.com/angular/angular-cli/issues/25924\n    ɵresetCompiledComponents();\n  }\n\n  angularServerApp = undefined;\n}\n\n/**\n * Appends module preload hints to an HTML string for specified JavaScript resources.\n * This function enhances the HTML by injecting `<link rel=\"modulepreload\">` elements\n * for each provided resource, allowing browsers to preload the specified JavaScript\n * modules for better performance.\n *\n * @param html - The original HTML string to which preload hints will be added.\n * @param preload - An array of URLs representing the JavaScript resources to preload.\n * @returns The modified HTML string with the preload hints injected before the closing `</body>` tag.\n *          If `</body>` is not found, the links are not added.\n */\nfunction appendPreloadHintsToHtml(html: string, preload: readonly string[]): string {\n  const bodyCloseIdx = html.lastIndexOf('</body>');\n  if (bodyCloseIdx === -1) {\n    return html;\n  }\n\n  // Note: Module preloads should be placed at the end before the closing body tag to avoid a performance penalty.\n  // Placing them earlier can cause the browser to prioritize downloading these modules\n  // over other critical page resources like images, CSS, and fonts.\n  return [\n    html.slice(0, bodyCloseIdx),\n    ...preload.map((val) => `<link rel=\"modulepreload\" href=\"${val}\">`),\n    html.slice(bodyCloseIdx),\n  ].join('\\n');\n}\n\n/**\n * Creates an HTTP redirect response with a specified location and status code.\n *\n * @param location - The URL to which the response should redirect.\n * @param status - The HTTP status code for the redirection. Defaults to 302 (Found).\n *                 See: https://developer.mozilla.org/en-US/docs/Web/API/Response/redirect_static#status\n * @returns A `Response` object representing the HTTP redirect.\n */\nfunction createRedirectResponse(location: string, status = 302): Response {\n  return new Response(null, {\n    status,\n    headers: {\n      'Location': location,\n    },\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Extracts a potential locale ID from a given URL based on the specified base path.\n *\n * This function parses the URL to locate a potential locale identifier that immediately\n * follows the base path segment in the URL's pathname. If the URL does not contain a valid\n * locale ID, an empty string is returned.\n *\n * @param url - The full URL from which to extract the locale ID.\n * @param basePath - The base path used as the reference point for extracting the locale ID.\n * @returns The extracted locale ID if present, or an empty string if no valid locale ID is found.\n *\n * @example\n * ```js\n * const url = new URL('https://example.com/base/en/page');\n * const basePath = '/base';\n * const localeId = getPotentialLocaleIdFromUrl(url, basePath);\n * console.log(localeId); // Output: 'en'\n * ```\n */\nexport function getPotentialLocaleIdFromUrl(url: URL, basePath: string): string {\n  const { pathname } = url;\n\n  // Move forward of the base path section.\n  let start = basePath.length;\n  if (pathname[start] === '/') {\n    start++;\n  }\n\n  // Find the next forward slash.\n  let end = pathname.indexOf('/', start);\n  if (end === -1) {\n    end = pathname.length;\n  }\n\n  // Extract the potential locale id.\n  return pathname.slice(start, end);\n}\n\n/**\n * Parses the `Accept-Language` header and returns a list of locale preferences with their respective quality values.\n *\n * The `Accept-Language` header is typically a comma-separated list of locales, with optional quality values\n * in the form of `q=<value>`. If no quality value is specified, a default quality of `1` is assumed.\n * Special case: if the header is `*`, it returns the default locale with a quality of `1`.\n *\n * @param header - The value of the `Accept-Language` header, typically a comma-separated list of locales\n *                  with optional quality values (e.g., `en-US;q=0.8,fr-FR;q=0.9`). If the header is `*`,\n *                  it represents a wildcard for any language, returning the default locale.\n *\n * @returns A `ReadonlyMap` where the key is the locale (e.g., `en-US`, `fr-FR`), and the value is\n *          the associated quality value (a number between 0 and 1). If no quality value is provided,\n *          a default of `1` is used.\n *\n * @example\n * ```js\n * parseLanguageHeader('en-US;q=0.8,fr-FR;q=0.9')\n * // returns new Map([['en-US', 0.8], ['fr-FR', 0.9]])\n\n * parseLanguageHeader('*')\n * // returns new Map([['*', 1]])\n * ```\n */\nfunction parseLanguageHeader(header: string): ReadonlyMap<string, number> {\n  if (header === '*') {\n    return new Map([['*', 1]]);\n  }\n\n  const parsedValues = header\n    .split(',')\n    .map((item) => {\n      const [locale, qualityValue] = item.split(';', 2).map((v) => v.trim());\n\n      let quality = qualityValue?.startsWith('q=') ? parseFloat(qualityValue.slice(2)) : undefined;\n      if (typeof quality !== 'number' || isNaN(quality) || quality < 0 || quality > 1) {\n        quality = 1; // Invalid quality value defaults to 1\n      }\n\n      return [locale, quality] as const;\n    })\n    .sort(([_localeA, qualityA], [_localeB, qualityB]) => qualityB - qualityA);\n\n  return new Map(parsedValues);\n}\n\n/**\n * Gets the preferred locale based on the highest quality value from the provided `Accept-Language` header\n * and the set of available locales.\n *\n * This function adheres to the HTTP `Accept-Language` header specification as defined in\n * [RFC 7231](https://datatracker.ietf.org/doc/html/rfc7231#section-5.3.5), including:\n * - Case-insensitive matching of language tags.\n * - Quality value handling (e.g., `q=1`, `q=0.8`). If no quality value is provided, it defaults to `q=1`.\n * - Prefix matching (e.g., `en` matching `en-US` or `en-GB`).\n *\n * @param header - The `Accept-Language` header string to parse and evaluate. It may contain multiple\n *                 locales with optional quality values, for example: `'en-US;q=0.8,fr-FR;q=0.9'`.\n * @param supportedLocales - An array of supported locales (e.g., `['en-US', 'fr-FR']`),\n *                           representing the locales available in the application.\n * @returns The best matching locale from the supported languages, or `undefined` if no match is found.\n *\n * @example\n * ```js\n * getPreferredLocale('en-US;q=0.8,fr-FR;q=0.9', ['en-US', 'fr-FR', 'de-DE'])\n * // returns 'fr-FR'\n *\n * getPreferredLocale('en;q=0.9,fr-FR;q=0.8', ['en-US', 'fr-FR', 'de-DE'])\n * // returns 'en-US'\n *\n * getPreferredLocale('es-ES;q=0.7', ['en-US', 'fr-FR', 'de-DE'])\n * // returns undefined\n * ```\n */\nexport function getPreferredLocale(\n  header: string,\n  supportedLocales: ReadonlyArray<string>,\n): string | undefined {\n  if (supportedLocales.length < 2) {\n    return supportedLocales[0];\n  }\n\n  const parsedLocales = parseLanguageHeader(header);\n\n  // Handle edge cases:\n  // - No preferred locales provided.\n  // - Only one supported locale.\n  // - Wildcard preference.\n  if (parsedLocales.size === 0 || (parsedLocales.size === 1 && parsedLocales.has('*'))) {\n    return supportedLocales[0];\n  }\n\n  // Create a map for case-insensitive lookup of supported locales.\n  // Keys are normalized (lowercase) locale values, values are original casing.\n  const normalizedSupportedLocales = new Map<string, string>();\n  for (const locale of supportedLocales) {\n    normalizedSupportedLocales.set(normalizeLocale(locale), locale);\n  }\n\n  // Iterate through parsed locales in descending order of quality.\n  let bestMatch: string | undefined;\n  const qualityZeroNormalizedLocales = new Set<string>();\n  for (const [locale, quality] of parsedLocales) {\n    const normalizedLocale = normalizeLocale(locale);\n    if (quality === 0) {\n      qualityZeroNormalizedLocales.add(normalizedLocale);\n      continue; // Skip locales with quality value of 0.\n    }\n\n    // Exact match found.\n    if (normalizedSupportedLocales.has(normalizedLocale)) {\n      return normalizedSupportedLocales.get(normalizedLocale);\n    }\n\n    // If an exact match is not found, try prefix matching (e.g., \"en\" matches \"en-US\").\n    // Store the first prefix match encountered, as it has the highest quality value.\n    if (bestMatch !== undefined) {\n      continue;\n    }\n\n    const [languagePrefix] = normalizedLocale.split('-', 1);\n    for (const supportedLocale of normalizedSupportedLocales.keys()) {\n      if (supportedLocale.startsWith(languagePrefix)) {\n        bestMatch = normalizedSupportedLocales.get(supportedLocale);\n        break; // No need to continue searching for this locale.\n      }\n    }\n  }\n\n  if (bestMatch !== undefined) {\n    return bestMatch;\n  }\n\n  // Return the first locale that is not quality zero.\n  for (const [normalizedLocale, locale] of normalizedSupportedLocales) {\n    if (!qualityZeroNormalizedLocales.has(normalizedLocale)) {\n      return locale;\n    }\n  }\n}\n\n/**\n * Normalizes a locale string by converting it to lowercase.\n *\n * @param locale - The locale string to normalize.\n * @returns The normalized locale string in lowercase.\n *\n * @example\n * ```ts\n * const normalized = normalizeLocale('EN-US');\n * console.log(normalized); // Output: \"en-us\"\n * ```\n */\nfunction normalizeLocale(locale: string): string {\n  return locale.toLowerCase();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type { AngularServerApp, getOrCreateAngularServerApp } from './app';\nimport { Hooks } from './hooks';\nimport { getPotentialLocaleIdFromUrl, getPreferredLocale } from './i18n';\nimport { EntryPointExports, getAngularAppEngineManifest } from './manifest';\nimport { joinUrlParts } from './utils/url';\n\n/**\n * Angular server application engine.\n * Manages Angular server applications (including localized ones), handles rendering requests,\n * and optionally transforms index HTML before rendering.\n *\n * @remarks This class should be instantiated once and used as a singleton across the server-side\n * application to ensure consistent handling of rendering requests and resource management.\n */\nexport class AngularAppEngine {\n  /**\n   * A flag to enable or disable the rendering of prerendered routes.\n   *\n   * Typically used during development to avoid prerendering all routes ahead of time,\n   * allowing them to be rendered on the fly as requested.\n   *\n   * @private\n   */\n  static ɵallowStaticRouteRender = false;\n\n  /**\n   * Hooks for extending or modifying the behavior of the server application.\n   * These hooks are used by the Angular CLI when running the development server and\n   * provide extensibility points for the application lifecycle.\n   *\n   * @private\n   */\n  static ɵhooks = /* #__PURE__*/ new Hooks();\n\n  /**\n   * The manifest for the server application.\n   */\n  private readonly manifest = getAngularAppEngineManifest();\n\n  /**\n   * A map of supported locales from the server application's manifest.\n   */\n  private readonly supportedLocales: ReadonlyArray<string> = Object.keys(\n    this.manifest.supportedLocales,\n  );\n\n  /**\n   * A cache that holds entry points, keyed by their potential locale string.\n   */\n  private readonly entryPointsCache = new Map<string, Promise<EntryPointExports>>();\n\n  /**\n   * Handles an incoming HTTP request by serving prerendered content, performing server-side rendering,\n   * or delivering a static file for client-side rendered routes based on the `RenderMode` setting.\n   *\n   * @param request - The HTTP request to handle.\n   * @param requestContext - Optional context for rendering, such as metadata associated with the request.\n   * @returns A promise that resolves to the resulting HTTP response object, or `null` if no matching Angular route is found.\n   *\n   * @remarks A request to `https://www.example.com/page/index.html` will serve or render the Angular route\n   * corresponding to `https://www.example.com/page`.\n   */\n  async handle(request: Request, requestContext?: unknown): Promise<Response | null> {\n    const serverApp = await this.getAngularServerAppForRequest(request);\n\n    if (serverApp) {\n      return serverApp.handle(request, requestContext);\n    }\n\n    if (this.supportedLocales.length > 1) {\n      // Redirect to the preferred language if i18n is enabled.\n      return this.redirectBasedOnAcceptLanguage(request);\n    }\n\n    return null;\n  }\n\n  /**\n   * Handles requests for the base path when i18n is enabled.\n   * Redirects the user to a locale-specific path based on the `Accept-Language` header.\n   *\n   * @param request The incoming request.\n   * @returns A `Response` object with a 302 redirect, or `null` if i18n is not enabled\n   *          or the request is not for the base path.\n   */\n  private redirectBasedOnAcceptLanguage(request: Request): Response | null {\n    const { basePath, supportedLocales } = this.manifest;\n\n    // If the request is not for the base path, it's not our responsibility to handle it.\n    const { pathname } = new URL(request.url);\n    if (pathname !== basePath) {\n      return null;\n    }\n\n    // For requests to the base path (typically '/'), attempt to extract the preferred locale\n    // from the 'Accept-Language' header.\n    const preferredLocale = getPreferredLocale(\n      request.headers.get('Accept-Language') || '*',\n      this.supportedLocales,\n    );\n\n    if (preferredLocale) {\n      const subPath = supportedLocales[preferredLocale];\n      if (subPath !== undefined) {\n        return new Response(null, {\n          status: 302, // Use a 302 redirect as language preference may change.\n          headers: {\n            'Location': joinUrlParts(pathname, subPath),\n            'Vary': 'Accept-Language',\n          },\n        });\n      }\n    }\n\n    return null;\n  }\n\n  /**\n   * Retrieves the Angular server application instance for a given request.\n   *\n   * This method checks if the request URL corresponds to an Angular application entry point.\n   * If so, it initializes or retrieves an instance of the Angular server application for that entry point.\n   * Requests that resemble file requests (except for `/index.html`) are skipped.\n   *\n   * @param request - The incoming HTTP request object.\n   * @returns A promise that resolves to an `AngularServerApp` instance if a valid entry point is found,\n   * or `null` if no entry point matches the request URL.\n   */\n  private async getAngularServerAppForRequest(request: Request): Promise<AngularServerApp | null> {\n    // Skip if the request looks like a file but not `/index.html`.\n    const url = new URL(request.url);\n    const entryPoint = await this.getEntryPointExportsForUrl(url);\n    if (!entryPoint) {\n      return null;\n    }\n\n    // Note: Using `instanceof` is not feasible here because `AngularServerApp` will\n    // be located in separate bundles, making `instanceof` checks unreliable.\n    const ɵgetOrCreateAngularServerApp =\n      entryPoint.ɵgetOrCreateAngularServerApp as typeof getOrCreateAngularServerApp;\n\n    const serverApp = ɵgetOrCreateAngularServerApp({\n      allowStaticRouteRender: AngularAppEngine.ɵallowStaticRouteRender,\n      hooks: AngularAppEngine.ɵhooks,\n    });\n\n    return serverApp;\n  }\n\n  /**\n   * Retrieves the exports for a specific entry point, caching the result.\n   *\n   * @param potentialLocale - The locale string used to find the corresponding entry point.\n   * @returns A promise that resolves to the entry point exports or `undefined` if not found.\n   */\n  private getEntryPointExports(potentialLocale: string): Promise<EntryPointExports> | undefined {\n    const cachedEntryPoint = this.entryPointsCache.get(potentialLocale);\n    if (cachedEntryPoint) {\n      return cachedEntryPoint;\n    }\n\n    const { entryPoints } = this.manifest;\n    const entryPoint = entryPoints[potentialLocale];\n    if (!entryPoint) {\n      return undefined;\n    }\n\n    const entryPointExports = entryPoint();\n    this.entryPointsCache.set(potentialLocale, entryPointExports);\n\n    return entryPointExports;\n  }\n\n  /**\n   * Retrieves the entry point for a given URL by determining the locale and mapping it to\n   * the appropriate application bundle.\n   *\n   * This method determines the appropriate entry point and locale for rendering the application by examining the URL.\n   * If there is only one entry point available, it is returned regardless of the URL.\n   * Otherwise, the method extracts a potential locale identifier from the URL and looks up the corresponding entry point.\n   *\n   * @param url - The URL of the request.\n   * @returns A promise that resolves to the entry point exports or `undefined` if not found.\n   */\n  private getEntryPointExportsForUrl(url: URL): Promise<EntryPointExports> | undefined {\n    const { basePath } = this.manifest;\n    if (this.supportedLocales.length === 1) {\n      return this.getEntryPointExports('');\n    }\n\n    const potentialLocale = getPotentialLocaleIdFromUrl(url, basePath);\n\n    return this.getEntryPointExports(potentialLocale) ?? this.getEntryPointExports('');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Function for handling HTTP requests in a web environment.\n *\n * @param request - The incoming HTTP request object.\n * @returns A Promise resolving to a `Response` object, `null`, or directly a `Response`,\n * supporting both synchronous and asynchronous handling.\n */\nexport type RequestHandlerFunction = (\n  request: Request,\n) => Promise<Response | null> | null | Response;\n\n/**\n * Annotates a request handler function with metadata, marking it as a special\n * handler.\n *\n * @param handler - The request handler function to be annotated.\n * @returns The same handler function passed in, with metadata attached.\n *\n * @example\n * Example usage in a Hono application:\n * ```ts\n * const app = new Hono();\n * export default createRequestHandler(app.fetch);\n * ```\n *\n * @example\n * Example usage in a H3 application:\n * ```ts\n * const app = createApp();\n * const handler = toWebHandler(app);\n * export default createRequestHandler(handler);\n * ```\n */\nexport function createRequestHandler(handler: RequestHandlerFunction): RequestHandlerFunction {\n  (handler as RequestHandlerFunction & { __ng_request_handler__?: boolean })[\n    '__ng_request_handler__'\n  ] = true;\n\n  return handler;\n}\n"], "names": ["ɵConsole", "SERVER_CONTEXT", "renderInternal", "provideServerRenderingPlatformServer", "loadChildrenHelper", "ɵENABLE_ROOT_COMPONENT_BOOTSTRAP", "ɵresetCompiledComponents"], "mappings": ";;;;;;AAUA;;AAEG;MACU,YAAY,CAAA;AAMM,IAAA,QAAA;AAL7B;;;;AAIG;AACH,IAAA,WAAA,CAA6B,QAA4B,EAAA;QAA5B,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAErC;;;;;;AAMG;AACH,IAAA,cAAc,CAAC,IAAY,EAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAA,iBAAA,CAAmB,CAAC;;AAG3D,QAAA,OAAO,KAAK;;AAGd;;;;;AAKG;AACH,IAAA,cAAc,CAAC,IAAY,EAAA;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;;AAGrC;;;;;AAKG;IACH,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC;;AAElD;;AC9CD;;AAEG;AACH,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC,yCAAyC,CAAC,CAAC;AAEzE;;;;;AAKG;AACG,MAAO,OAAQ,SAAQA,QAAQ,CAAA;AACnC;;;;;;;;AAQG;AACM,IAAA,GAAG,CAAC,OAAe,EAAA;QAC1B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC9B,YAAA,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;;AAGvB;;ACqGD;;;AAGG;AACH,IAAI,kBAAkD;AAEtD;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,QAA4B,EAAA;IAChE,kBAAkB,GAAG,QAAQ;AAC/B;AAEA;;;;;AAKG;SACa,qBAAqB,GAAA;IACnC,IAAI,CAAC,kBAAkB,EAAE;QACvB,MAAM,IAAI,KAAK,CACb,mCAAmC;AACjC,YAAA,CAAA,sGAAA,CAAwG,CAC3G;;AAGH,IAAA,OAAO,kBAAkB;AAC3B;AAEA;;;AAGG;AACH,IAAI,wBAA8D;AAElE;;;;AAIG;AACG,SAAU,2BAA2B,CAAC,QAAkC,EAAA;IAC5E,wBAAwB,GAAG,QAAQ;AACrC;AAEA;;;;;AAKG;SACa,2BAA2B,GAAA;IACzC,IAAI,CAAC,wBAAwB,EAAE;QAC7B,MAAM,IAAI,KAAK,CACb,0CAA0C;AACxC,YAAA,CAAA,sGAAA,CAAwG,CAC3G;;AAGH,IAAA,OAAO,wBAAwB;AACjC;;AC/LA;;;;;;;;;;;;;AAaG;AAMH;;;;;;;;;;;;;AAaG;AACG,SAAU,iBAAiB,CAAC,GAAW,EAAA;;IAE3C,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;AAC9D;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,eAAe,CAAC,GAAW,EAAA;;AAEzC,IAAA,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAI,CAAA,EAAA,GAAG,EAAE;AACzC;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,gBAAgB,CAAC,GAAW,EAAA;;IAE1C,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAG,EAAA,GAAG,GAAG;AACtD;AAEA;;;;;;;;;;;;;;;AAeG;AACa,SAAA,YAAY,CAAC,GAAG,KAAe,EAAA;IAC7C,MAAM,cAAc,GAAa,EAAE;AACnC,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,QAAA,IAAI,IAAI,KAAK,EAAE,EAAE;;YAEf;;QAGF,IAAI,cAAc,GAAG,IAAI;AACzB,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACnB,YAAA,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;;QAE1C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACjC,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;AAE9C,QAAA,IAAI,cAAc,KAAK,EAAE,EAAE;AACzB,YAAA,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;;;IAIvC,OAAO,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD;AAEA;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,qBAAqB,CAAC,GAAQ,EAAA;IAC5C,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACxC,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;;AAEhC,QAAA,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE,CAAC;AAErF,QAAA,OAAO,WAAW;;AAGpB,IAAA,OAAO,GAAG;AACZ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA,mBAAmB,CAAC,MAAc,EAAE,QAAgB,EAAA;AAClE,IAAA,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACrB,QAAA,MAAM,IAAI,KAAK,CAAC,gEAAgE,MAAM,CAAA,CAAA,CAAG,CAAC;;AAG5F,IAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACvB,QAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,QAAQ,CAAA,CAAA,CAAG,CAAC;;IAGhG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1B,QAAA,OAAO,MAAM;;IAGf,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;IACzC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;AACrC,IAAA,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAChD,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CACzD;AAED,IAAA,OAAO,YAAY,CAAC,GAAG,aAAa,CAAC;AACvC;;AClKA;;;;;;;;;;;;;;;AAeG;AACI,eAAe,aAAa,CACjC,IAAY,EACZ,SAA2B,EAC3B,GAAQ,EACR,iBAAmC,EACnC,aAAqB,EAAA;;IAGrB,MAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACzD,MAAM,WAAW,GAAG,cAAc,CAAC;AACjC,QAAA;AACE,YAAA,OAAO,EAAE,cAAc;AACvB,YAAA,QAAQ,EAAE;AACR,gBAAA,GAAG,EAAE,WAAW;AAChB,gBAAA,QAAQ,EAAE,IAAI;AACf,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAEC,eAAc;AACvB,YAAA,QAAQ,EAAE,aAAa;AACxB,SAAA;AACD,QAAA;;AAEE,YAAA,OAAO,EAAED,QAAQ;;;;AAIjB,YAAA,UAAU,EAAE,MAAM,IAAI,OAAO,EAAE;AAChC,SAAA;AACD,QAAA,GAAG,iBAAiB;AACrB,KAAA,CAAC;AAEF,IAAA,IAAI,UAA8B;IAClC,IAAI,kBAAkB,GAAG,IAAI;AAE7B,IAAA,IAAI;AACF,QAAA,IAAI,cAA8B;AAClC,QAAA,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE;YACzB,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC;YAC9D,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;;aAClD;AACL,YAAA,cAAc,GAAG,MAAM,SAAS,EAAE;;;AAIpC,QAAA,MAAM,cAAc,CAAC,UAAU,EAAE;;AAGjC,QAAA,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ;AAC3C,QAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;QAChE,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC;AACtC,QAAA,MAAM,wBAAwB,GAAG,MAAM,CAAC,wBAAwB;QAEhE,IAAI,CAAC,gBAAgB,EAAE;YACrB,kBAAkB,GAAG,KAAK;;AACrB,aAAA,IAAI,wBAAwB,EAAE,QAAQ,EAAE;YAC7C,kBAAkB,GAAG,KAAK;AAE1B,YAAA,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,wBAAwB;AACzD,YAAA,MAAM,mBAAmB,GAAG,QAAQ,CAAC,QAAQ,EAAE;AAE/C,YAAA,IAAI,UAAU,CAAC,QAAQ,EAAE,KAAK,mBAAmB,EAAE;AACjD,gBAAA,MAAM,QAAQ,GACZ,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACxD,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,EAAE;AAExD,gBAAA,UAAU,GAAG,YAAY,CAAC,QAAQ,EAAE,mBAAmB,CAAC;;;QAI5D,OAAO;YACL,kBAAkB;YAClB,UAAU;AACV,YAAA,OAAO,EAAE,MACP,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;;gBAEtC,UAAU,CAAC,MAAK;AACd,oBAAAE,eAAc,CAAC,WAAW,EAAE,cAAc;yBACvC,IAAI,CAAC,OAAO;yBACZ,KAAK,CAAC,MAAM;yBACZ,OAAO,CAAC,MAAM,KAAK,oBAAoB,CAAC,WAAW,CAAC,CAAC;iBACzD,EAAE,CAAC,CAAC;AACP,aAAC,CAAC;SACL;;IACD,OAAO,KAAK,EAAE;AACd,QAAA,MAAM,oBAAoB,CAAC,WAAW,CAAC;AAEvC,QAAA,MAAM,KAAK;;YACH;AACR,QAAA,IAAI,kBAAkB,IAAI,UAAU,EAAE;AACpC,YAAA,KAAK,oBAAoB,CAAC,WAAW,CAAC;;;AAG5C;AAEA;;;;;;;AAOG;AACG,SAAU,UAAU,CAAC,KAAuB,EAAA;IAChD,OAAO,MAAM,IAAI,KAAK;AACxB;AAEA;;;;;AAKG;AACH,SAAS,oBAAoB,CAAC,WAAwB,EAAA;AACpD,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;QAC7B,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC1B,WAAW,CAAC,OAAO,EAAE;;AAGvB,YAAA,OAAO,EAAE;SACV,EAAE,CAAC,CAAC;AACP,KAAC,CAAC;AACJ;;ACtKA;;;;;;;;;;;;AAYG;SACa,gBAAgB,CAC9B,OAAmB,EACnB,MAAmB,EACnB,kBAA0B,EAAA;IAE1B,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,KAAI;QACxC,MAAM,YAAY,GAAG,MAAK;AACxB,YAAA,MAAM,CACJ,IAAI,YAAY,CAAC,GAAG,kBAAkB,CAAA,eAAA,EAAkB,MAAM,CAAC,MAAM,CAAE,CAAA,EAAE,YAAY,CAAC,CACvF;AACH,SAAC;;AAGD,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,YAAA,YAAY,EAAE;YAEd;;AAGF,QAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAE9D;aACG,IAAI,CAAC,OAAO;aACZ,KAAK,CAAC,MAAM;aACZ,OAAO,CAAC,MAAK;AACZ,YAAA,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC;AACnD,SAAC,CAAC;AACN,KAAC,CAAC;AACJ;;AC7BA;;;AAGG;AACH,MAAM,eAAe,GAAG,cAAc;AAEtC;;;AAGG;AACH,IAAK,0BAGJ;AAHD,CAAA,UAAK,0BAA0B,EAAA;AAC7B,IAAA,0BAAA,CAAA,0BAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,0BAAA,CAAA,0BAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;AACd,CAAC,EAHI,0BAA0B,KAA1B,0BAA0B,GAG9B,EAAA,CAAA,CAAA;AAWD;;;;AAIG;IACS;AAAZ,CAAA,UAAY,UAAU,EAAA;;AAEpB,IAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;;AAGN,IAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;;AAGN,IAAA,UAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACX,CAAC,EATW,UAAU,KAAV,UAAU,GASrB,EAAA,CAAA,CAAA;AAED;;;;AAIG;IACS;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AAEN;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AAEN;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACN,CAAC,EAlBW,iBAAiB,KAAjB,iBAAiB,GAkB5B,EAAA,CAAA,CAAA;AAkID;;;AAGG;AACI,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAAqB,sBAAsB,CAAC;AAElG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCG;AACG,SAAU,UAAU,CACxB,MAAqB,EAAA;AAErB,IAAA,MAAM,MAAM,GAAuB,EAAE,MAAM,EAAE;IAE7C,OAAO;QACL,KAAK,EAAE,0BAA0B,CAAC,YAAY;AAC9C,QAAA,UAAU,EAAE;AACV,YAAA;AACE,gBAAA,OAAO,EAAE,oBAAoB;AAC7B,gBAAA,QAAQ,EAAE,MAAM;AACjB,aAAA;AACF,SAAA;KACF;AACH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCG;AACG,SAAU,YAAY,CAC1B,SAAwF,EAAA;AAExF,IAAA,MAAM,WAAW,GAAU;AACzB,QAAA,IAAI,EAAE,eAAe;KACtB;AAED,IAAA,IAAI,MAAM,IAAI,SAAS,EAAE;AACvB,QAAA,WAAW,CAAC,SAAS,GAAG,SAA0B;;SAC7C;AACL,QAAA,WAAW,CAAC,aAAa,GAAG,SAAyC;;IAGvE,OAAO;QACL,KAAK,EAAE,0BAA0B,CAAC,QAAQ;AAC1C,QAAA,UAAU,EAAE;AACV,YAAA;AACE,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,QAAQ,EAAE,WAAW;AACrB,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;YACD,6BAA6B,CAAC,MAAK;AACjC,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC;AAC3C,gBAAA,MAAM,CAAC,aAAa,GAAG,eAAe;AACxC,aAAC,CAAC;AACH,SAAA;KACF;AACH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;AACa,SAAA,sBAAsB,CACpC,GAAG,QAA8D,EAAA;IAEjE,IAAI,WAAW,GAAG,KAAK;IACvB,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAA,MAAM,SAAS,GAAwC,CAACC,wBAAoC,EAAE,CAAC;IAE/F,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,QAAQ,EAAE;AAC5C,QAAA,WAAW,KAAK,KAAK,KAAK,0BAA0B,CAAC,QAAQ;AAC7D,QAAA,eAAe,KAAK,KAAK,KAAK,0BAA0B,CAAC,YAAY;AACrE,QAAA,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;;AAG/B,IAAA,IAAI,CAAC,eAAe,IAAI,WAAW,EAAE;QACnC,MAAM,IAAI,KAAK,CACb,CAAoH,kHAAA,CAAA;AAClH,YAAA,CAAA,iEAAA,CAAmE,CACtE;;AAGH,IAAA,OAAO,wBAAwB,CAAC,SAAS,CAAC;AAC5C;;AClTA;;;;;;AAMG;MACU,SAAS,CAAA;AACpB;;;AAGG;AACc,IAAA,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE;AAEvD;;;;;;;AAOG;IACH,MAAM,CAAC,KAAa,EAAE,QAAgE,EAAA;AACpF,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QAC5C,MAAM,kBAAkB,GAAa,EAAE;AAEvC,QAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;;AAE9B,YAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,OAAO;YAC5D,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACpD,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,SAAS,GAAG,IAAI,CAAC,wBAAwB,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE,SAAS,CAAC;;YAGjD,IAAI,GAAG,SAAS;AAChB,YAAA,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC;;;QAI5C,IAAI,CAAC,QAAQ,GAAG;AACd,YAAA,GAAG,QAAQ;YACX,KAAK,EAAE,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACrD;;AAGH;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,KAAa,EAAA;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QAE5C,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,QAAQ;;AAGpD;;;;;;;AAOG;IACH,QAAQ,GAAA;QACN,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;;AAGpC;;;;;;;;;AASG;IACH,OAAO,UAAU,CAAC,KAAgC,EAAA;AAChD,QAAA,MAAM,IAAI,GAAG,IAAI,SAAS,EAAE;QAE5B,KAAK,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,EAAE,IAAI,KAAK,EAAE;AAC1C,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAG9B,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;AACH,IAAA,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,QAAQ;;QAGrB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE;YAC9C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;;;AAInC;;;;;AAKG;AACK,IAAA,eAAe,CAAC,KAAa,EAAA;QACnC,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;;AAGzC;;;;;;;;;;;;;AAaG;IACK,kBAAkB,CACxB,QAAkB,EAClB,IAAI,GAAG,IAAI,CAAC,IAAI,EAChB,YAAY,GAAG,CAAC,EAAA;AAEhB,QAAA,IAAI,YAAY,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnC,YAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;;AAGvD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvB,YAAA,OAAO,SAAS;;AAGlB,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC;;QAGtC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC;QAC7C,IAAI,UAAU,EAAE;AACd,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC;YAC7E,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,KAAK;;;;QAKhB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;QAC5C,IAAI,aAAa,EAAE;AACjB,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,GAAG,CAAC,CAAC;YAChF,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,KAAK;;;;QAKhB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;;AAGhC;;;;;AAKG;IACK,wBAAwB,GAAA;QAC9B,OAAO;YACL,QAAQ,EAAE,IAAI,GAAG,EAAE;SACpB;;AAEJ;;ACtOD;;;AAGG;AACH,MAAM,kBAAkB,GAAG,EAAE;AAE7B;;;AAGG;AACH,MAAM,gBAAgB,GAAG,WAAW;AAEpC;;AAEG;AACH,MAAM,oBAAoB,GAAG,kBAAkB;AAE/C;;AAEG;AACH,MAAM,6BAA6B,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAwDxE;;;;;AAKG;AACH,gBAAgB,WAAW,CAAC,OAU3B,EAAA;AACC,IAAA,IAAI;QACF,MAAM,EACJ,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,QAAQ,EACR,cAAc,EACd,qBAAqB,EACrB,0BAA0B,EAC1B,wBAAwB,EACxB,8BAA8B,GAC/B,GAAG,OAAO;AAEX,QAAA,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,KAAK;AAC/E,QAAA,IAAI,UAAU,IAAI,aAAa,EAAE;AAC/B,YAAA,uBAAuB,CAAC,UAAU,EAAE,0BAA0B,EAAE,QAAQ,CAAC;;QAG3E,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;YAChD,OAAO,cAAc,CACnB,qBAAqB,EACrB,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,SAAS,EACvD,QAAQ,EACR,cAAc,EACd,wBAAwB,EACxB,8BAA8B,CAC/B;;AACI,aAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AACnC,YAAA,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC1E,MAAM;AACJ,oBAAA,KAAK,EACH,CAAA,KAAA,EAAQ,QAAQ,CAAC,MAAM,CAAuD,qDAAA,CAAA;AAC9E,wBAAA,CAAA,yDAAA,EAA4D,CAAC,GAAG,6BAA6B,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAG,CAAA,CAAA;iBACxH;;AACI,iBAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;gBACzC,MAAM;AACJ,oBAAA,GAAG,QAAQ;oBACX,UAAU,EAAE,iBAAiB,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;iBAC1D;;iBACI;AACL,gBAAA,MAAM,QAAQ;;;aAEX;AACL,YAAA,MAAM,QAAQ;;;AAIhB,QAAA,IAAI,QAAQ,EAAE,MAAM,EAAE;YACpB,OAAO,oBAAoB,CAAC;AAC1B,gBAAA,GAAG,OAAO;AACV,gBAAA,MAAM,EAAE,QAAQ;AAChB,gBAAA,WAAW,EAAE,gBAAgB;gBAC7B,cAAc,EAAE,QAAQ,CAAC,OAAO;AACjC,aAAA,CAAC;;;QAIJ,IAAI,YAAY,EAAE;YAChB,IAAI,UAAU,EAAE;AACd,gBAAA,uBAAuB,CAAC,UAAU,EAAE,0BAA0B,EAAE,QAAQ,CAAC;;AAG3E,YAAA,MAAM,iBAAiB,GAAG,MAAMC,aAAkB,CAChD,KAAK,EACL,QAAQ,EACR,cAAc,CACf,CAAC,SAAS,EAAE;YAEb,IAAI,iBAAiB,EAAE;gBACrB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,GAAG,cAAc,EAAE,GAAG,iBAAiB;gBAC5E,OAAO,oBAAoB,CAAC;AAC1B,oBAAA,GAAG,OAAO;AACV,oBAAA,MAAM,EAAE,WAAW;AACnB,oBAAA,cAAc,EAAE,QAAQ;AACxB,oBAAA,WAAW,EAAE,gBAAgB;oBAC7B,cAAc,EAAE,QAAQ,CAAC,OAAO;AACjC,iBAAA,CAAC;;;;IAGN,OAAO,KAAK,EAAE;QACd,MAAM;YACJ,KAAK,EAAE,6BAA6B,OAAO,CAAC,gBAAgB,CAAO,GAAA,EAAA,KAAe,CAAC,OAAO,CAAE,CAAA;SAC7F;;AAEL;AAEA;;;;;;;;AAQG;AACH,gBAAgB,oBAAoB,CAAC,OAUpC,EAAA;AACC,IAAA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,qBAAqB,EAAE,GAAG,OAAO;AAE5F,IAAA,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE;AAChC,QAAA,MAAM,EAAE,OAAO,EAAE,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,KAAK;QACrD,MAAM,gBAAgB,GAAG,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;AAExD,QAAA,IAAI,OAAO,IAAI,qBAAqB,EAAE;YACpC,IAAI,UAAU,GAAG,KAAK;YACtB,KAAK,MAAM,eAAe,IAAI,qBAAqB,CAAC,QAAQ,EAAE,EAAE;gBAC9D,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;oBACvD;;gBAGF,UAAU,GAAG,IAAI;AACjB,gBAAA,eAAe,CAAC,qBAAqB,GAAG,IAAI;gBAE5C,IAAI,eAAe,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;oBACvD,MAAM;AACJ,wBAAA,KAAK,EACH,CAAc,WAAA,EAAA,iBAAiB,CAAC,gBAAgB,CAAC,CAAuD,qDAAA,CAAA;4BACxG,CAAwF,sFAAA,CAAA;qBAC3F;oBACD;;gBAGF,OAAO,WAAW,CAAC;AACjB,oBAAA,GAAG,OAAO;oBACV,gBAAgB;oBAChB,KAAK;AACL,oBAAA,QAAQ,EAAE;AACR,wBAAA,GAAG,eAAe;AAClB,wBAAA,OAAO,EAAE,cAAc;wBACvB,KAAK,EAAE,eAAe,CAAC,KAAK;AAC5B,wBAAA,qBAAqB,EAAE,SAAS;AACjC,qBAAA;AACF,iBAAA,CAAC;;YAGJ,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM;AACJ,oBAAA,KAAK,EACH,CAAc,WAAA,EAAA,iBAAiB,CAAC,gBAAgB,CAAC,CAAuC,qCAAA,CAAA;wBACxF,6HAA6H;iBAChI;;YAGH;;AAGF,QAAA,IAAI,eAA8D;QAClE,IAAI,qBAAqB,EAAE;AACzB,YAAA,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC,gBAAgB,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBACpB,MAAM;AACJ,oBAAA,KAAK,EACH,CAAQ,KAAA,EAAA,iBAAiB,CAAC,gBAAgB,CAAC,CAAgF,8EAAA,CAAA;wBAC3H,wEAAwE;iBAC3E;gBACD;;AAGF,YAAA,eAAe,CAAC,qBAAqB,GAAG,IAAI;;QAG9C,OAAO,WAAW,CAAC;AACjB,YAAA,GAAG,OAAO;AACV,YAAA,QAAQ,EAAE;gBACR,UAAU,EAAE,UAAU,CAAC,SAAS;AAChC,gBAAA,GAAG,eAAe;AAClB,gBAAA,OAAO,EAAE,cAAc;;;;AAIvB,gBAAA,KAAK,EAAE,IAAI,KAAK,EAAE,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;AAC1E,gBAAA,qBAAqB,EAAE,SAAS;AACjC,aAAA;YACD,gBAAgB;YAChB,KAAK;AACN,SAAA,CAAC;;AAEN;AAEA;;;;;;AAMG;AACH,SAAS,uBAAuB,CAC9B,SAAiB,EACjB,0BAAsD,EACtD,QAA2C,EAAA;AAE3C,IAAA,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE;IAC/C,IAAI,CAAC,0BAA0B,IAAI,gBAAgB,CAAC,MAAM,IAAI,kBAAkB,EAAE;QAChF;;AAGF,IAAA,MAAM,OAAO,GAAG,0BAA0B,CAAC,SAAS,CAAC;AACrD,IAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;QACpB;;;AAIF,IAAA,MAAM,gBAAgB,GAAgB,IAAI,GAAG,CAAC,gBAAgB,CAAC;AAC/D,IAAA,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;AAC1B,QAAA,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,gBAAgB,CAAC,IAAI,KAAK,kBAAkB,EAAE;YAChD;;;IAIJ,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACjD;AAEA;;;;;;;;;;;AAWG;AACH,gBAAgB,cAAc,CAC5B,qBAAqF,EACrF,UAA8B,EAC9B,QAA2C,EAC3C,cAAwB,EACxB,wBAAiC,EACjC,8BAAuC,EAAA;IAEvC,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;AAChD,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,8EAAA,CAAgF,CACjF;;AAGH,IAAA,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ;AAC/D,IAAA,MAAM,kBAAkB,GAAG,oBAAoB,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,GAAG,SAAS;AAE7F,IAAA,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC;;AAGnC,IAAA,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,EAAE,UAAU,CAAC;;IAGnE,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC/D,IAAA,IACE,CAAC,eAAe,IAAI,CAAC,kBAAkB;AACvC,SAAC,CAAC,eAAe,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAClE;;QAEA,MAAM;AACJ,YAAA,GAAG,IAAI;AACP,YAAA,KAAK,EAAE,gBAAgB;SACxB;QAED;;IAGF,IAAI,wBAAwB,EAAE;QAC5B,IAAI,CAAC,kBAAkB,EAAE;YACvB,MAAM;AACJ,gBAAA,KAAK,EACH,CAAQ,KAAA,EAAA,iBAAiB,CAAC,gBAAgB,CAAC,CAA8E,4EAAA,CAAA;oBACzH,CAA8G,4GAAA,CAAA;oBAC9G,CAAsC,oCAAA,CAAA;aACzC;YAED;;QAGF,IAAI,qBAAqB,EAAE;;YAEzB,MAAM,iBAAiB,GAAG;AACxB,kBAAE;AACF,kBAAE,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC;YACxC,MAAM,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAC5D,YAAA,IAAI,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS,IAAI,EAAE,oBAAoB,IAAI,KAAK,CAAC,EAAE;AAC1F,gBAAA,qBAAqB,CAAC,MAAM,CAAC,iBAAiB,EAAE;AAC9C,oBAAA,GAAG,KAAK;AACR,oBAAA,qBAAqB,EAAE,IAAI;oBAC3B,kBAAkB;AACnB,iBAAA,CAAC;;;AAIN,QAAA,MAAM,UAAU,GAAG,MAAM,qBAAqB,CAAC,cAAc,EAAE,MAAM,kBAAkB,EAAE,CAAC;AAC1F,QAAA,IAAI;AACF,YAAA,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;gBAC/B,MAAM,QAAQ,GAAG,gCAAgC,CAAC,MAAM,EAAE,gBAAgB,CAAC;gBAC3E,MAAM,uBAAuB,GAAG;AAC7B,qBAAA,OAAO,CAAC,oBAAoB,EAAE,QAAQ;AACtC,qBAAA,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC;gBAEtC,MAAM;AACJ,oBAAA,GAAG,IAAI;AACP,oBAAA,KAAK,EAAE,uBAAuB;oBAC9B,UAAU,EACR,UAAU,KAAK;AACb,0BAAE;AACF,0BAAE,iBAAiB,CAAC,uBAAuB,EAAE,UAAU,CAAC;iBAC7D;;;QAEH,OAAO,KAAK,EAAE;YACd,MAAM,EAAE,KAAK,EAAE,CAAA,EAAI,KAAe,CAAC,OAAO,CAAE,CAAA,EAAE;YAE9C;;;;AAKJ,IAAA,IACE,8BAA8B;SAC7B,QAAQ,KAAK,iBAAiB,CAAC,IAAI,IAAI,CAAC,wBAAwB,CAAC,EAClE;QACA,MAAM;AACJ,YAAA,GAAG,IAAI;AACP,YAAA,KAAK,EAAE,gBAAgB;AACvB,YAAA,UAAU,EAAE,QAAQ,KAAK,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM;SAC1F;;AAEL;AAEA;;;;;;;AAOG;AACH,SAAS,gCAAgC,CACvC,MAA8B,EAC9B,gBAAwB,EAAA;IAExB,OAAO,CAAC,KAAK,KAAI;QACf,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;AACnC,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,KAAK,CACb,CAAA,mDAAA,EAAsD,iBAAiB,CAAC,gBAAgB,CAAC,CAAU,QAAA,CAAA;AACjG,gBAAA,CAAA,2CAAA,EAA8C,aAAa,CAAK,GAAA,CAAA;gBAChE,CAAuF,qFAAA,CAAA;AACvF,gBAAA,0BAA0B,CAC7B;;AAGH,QAAA,OAAO,aAAa,KAAK,IAAI,GAAG,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,GAAG,KAAK;AACrD,KAAC;AACH;AAEA;;;;;;;;;;AAUG;AACH,SAAS,iBAAiB,CAAC,SAAiB,EAAE,UAAkB,EAAA;AAC9D,IAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;AAEzB,QAAA,OAAO,UAAU;;;AAInB,IAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;AACxE,IAAA,QAAQ,CAAC,GAAG,EAAE,CAAC;AAEf,IAAA,OAAO,YAAY,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC;AAC9C;AAEA;;;;;;;;;;AAUG;AACH,SAAS,0BAA0B,CAAC,EAAE,MAAM,EAAE,aAAa,EAAsB,EAAA;AAI/E,IAAA,MAAM,YAAY,GAAkB,CAAC,GAAG,MAAM,CAAC;AAC/C,IAAA,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,YAAY,CAAC,OAAO,CAAC;AACnB,YAAA,IAAI,EAAE,aAAa;YACnB,UAAU,EAAE,UAAU,CAAC,SAAS;AACjC,SAAA,CAAC;;AAGJ,IAAA,MAAM,qBAAqB,GAAG,IAAI,SAAS,EAA2C;IACtF,MAAM,MAAM,GAAa,EAAE;IAE3B,KAAK,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,EAAE,IAAI,YAAY,EAAE;AAChD,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACnB,YAAA,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,CAAA,0DAAA,CAA4D,CAAC;YAEzF;;QAGF,IAAI,oBAAoB,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE;AACrF,YAAA,MAAM,CAAC,IAAI,CACT,YAAY,IAAI,CAAA,4EAAA,CAA8E,CAC/F;YACD;;AAGF,QAAA,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC;;AAG9C,IAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAE;AAC1C;AAEA;;;;;;;;;;;;;;;;;;AAkBG;AACI,eAAe,gCAAgC,CACpD,SAA2B,EAC3B,QAAgB,EAChB,GAAQ,EACR,wBAAwB,GAAG,KAAK,EAChC,8BAA8B,GAAG,IAAI,EACrC,6BAAqE,SAAS,EAAA;AAE9E,IAAA,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG;;IAG9B,MAAM,WAAW,GAAG,cAAc,CAAC;AACjC,QAAA;AACE,YAAA,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAA,EAAG,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAA,CAAA,CAAG,EAAE;AACrD,SAAA;AACD,QAAA;;AAEE,YAAA,OAAO,EAAEJ,QAAQ;;;;AAIjB,YAAA,UAAU,EAAE,MAAM,IAAI,OAAO,EAAE;AAChC,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAEK,gCAAgC;AACzC,YAAA,QAAQ,EAAE,KAAK;AAChB,SAAA;AACF,KAAA,CAAC;AAEF,IAAA,IAAI;AACF,QAAA,IAAI,cAA8B;AAElC,QAAA,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE;YACzB,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC;YAC9D,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;;aAClD;AACL,YAAA,cAAc,GAAG,MAAM,SAAS,EAAE;;AAGpC,QAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ;QACxC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;;;;QAKlC,MAAc,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,EAAE,IAAI,IAAI;;AAGpE,QAAA,MAAM,cAAc,CAAC,UAAU,EAAE;QAEjC,MAAM,MAAM,GAAa,EAAE;AAE3B,QAAA,MAAM,WAAW,GACf,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YACrD,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,EAAE;AACrD,QAAA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC;QAEvE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;AACvC,QAAA,MAAM,kBAAkB,GAAG,QAAQ,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACvF,QAAA,IAAI,qBAAqF;QAEzF,IAAI,kBAAkB,EAAE;AACtB,YAAA,MAAM,MAAM,GAAG,0BAA0B,CAAC,kBAAkB,CAAC;AAC7D,YAAA,qBAAqB,GAAG,MAAM,CAAC,qBAAqB;YACpD,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;;AAG/B,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,OAAO;gBACL,QAAQ;AACR,gBAAA,MAAM,EAAE,EAAE;gBACV,MAAM;aACP;;QAGH,MAAM,aAAa,GAA4B,EAAE;AACjD,QAAA,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;;YAExB,MAAM,cAAc,GAAG,oBAAoB,CAAC;gBAC1C,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ;AACR,gBAAA,cAAc,EAAE,QAAQ;AACxB,gBAAA,WAAW,EAAE,EAAE;gBACf,qBAAqB;gBACrB,wBAAwB;gBACxB,8BAA8B;gBAC9B,0BAA0B;AAC3B,aAAA,CAAC;AAEF,YAAA,MAAM,UAAU,GAAgB,IAAI,GAAG,EAAE;AACzC,YAAA,WAAW,MAAM,aAAa,IAAI,cAAc,EAAE;AAChD,gBAAA,IAAI,OAAO,IAAI,aAAa,EAAE;AAC5B,oBAAA,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;oBAChC;;;;AAKF,gBAAA,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK;gBACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AAC9B,oBAAA,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;AACjC,oBAAA,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;;;;;AAM7B,YAAA,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAEtD,IAAI,qBAAqB,EAAE;AACzB,gBAAA,KAAK,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,IAAI,qBAAqB,CAAC,QAAQ,EAAE,EAAE;oBAC/E,IAAI,qBAAqB,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;;wBAElD;;oBAGF,MAAM,CAAC,IAAI,CACT,CAAA,KAAA,EAAQ,iBAAiB,CAAC,KAAK,CAAC,CAAkE,gEAAA,CAAA;wBAChG,CAAoF,kFAAA,CAAA;AACpF,wBAAA,mGAAmG,CACtG;;;;aAGA;YACL,MAAM,iBAAiB,GAAG,qBAAqB,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI;AAC5D,gBAAA,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,UAAU,CAAC,SAAS;aACjC;YAED,aAAa,CAAC,IAAI,CAAC;AACjB,gBAAA,GAAG,iBAAiB;;;AAGpB,gBAAA,KAAK,EAAE,EAAE;AACV,aAAA,CAAC;;QAGJ,OAAO;YACL,QAAQ;AACR,YAAA,MAAM,EAAE,aAAa;YACrB,MAAM;YACN,aAAa,EAAE,kBAAkB,EAAE,aAAa;SACjD;;YACO;QACR,WAAW,CAAC,OAAO,EAAE;;AAEzB;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACG,SAAU,+BAA+B,CAAC,OAM/C,EAAA;IACC,MAAM,EACJ,GAAG,EACH,QAAQ,GAAG,qBAAqB,EAAE,EAClC,wBAAwB,GAAG,KAAK,EAChC,8BAA8B,GAAG,IAAI,EACrC,MAAM,GACP,GAAG,OAAO;AAEX,IAAA,eAAe,OAAO,GAAA;AAKpB,QAAA,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE;AACjC,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE;AAC7E,QAAA,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,SAAS,EAAE;QAC5C,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,gCAAgC,CACxF,SAAS,EACT,QAAQ,EACR,GAAG,EACH,wBAAwB,EACxB,8BAA8B,EAC9B,QAAQ,CAAC,0BAA0B,CACpC;QAED,KAAK,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,EAAE,IAAI,MAAM,EAAE;AAC3C,YAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;gBACrC,QAAQ,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC;;;;AAKnE,YAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACnD,gBAAA,IAAI,KAAK,KAAK,SAAS,EAAE;;AAEvB,oBAAA,OAAQ,QAAgB,CAAC,GAAG,CAAC;;;YAIjC,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC/C,YAAA,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC;;QAGvC,OAAO;YACL,aAAa;YACb,SAAS;YACT,MAAM;SACP;;AAGH,IAAA,OAAO,MAAM,GAAG,gBAAgB,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,mBAAmB,CAAC,GAAG,OAAO,EAAE;AACtF;;ACrxBA;;;AAGG;MACU,KAAK,CAAA;AAChB;;;AAGG;AACc,IAAA,KAAK,GAAG,IAAI,GAAG,EAAwB;AAExD;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,MAAM,GAAG,CACP,IAAU,EACV,OAA0C,EAAA;QAE1C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QAClC,QAAQ,IAAI;YACV,KAAK,oBAAoB,EAAE;gBACzB,IAAI,CAAC,KAAK,EAAE;oBACV,OAAO,OAAO,CAAC,IAA+C;;AAGhE,gBAAA,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,EAAE;AAC1B,gBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACxB,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC;;gBAG5B,OAAO,GAAG,CAAC,IAA+C;;AAE5D,YAAA;AACE,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAA,mBAAA,CAAqB,CAAC;;;AAIjE;;;;;;;;;;;;;;;;;;;AAmBG;IACH,EAAE,CAAwB,IAAU,EAAE,OAA2B,EAAA;QAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,KAAK,EAAE;AACT,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;aACd;YACL,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;;;AAInC;;;;;AAKG;AACH,IAAA,GAAG,CAAC,IAAc,EAAA;AAChB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM;;AAExC;;AC7GD;;;;;AAKG;MACU,YAAY,CAAA;AAOc,IAAA,SAAA;AANrC;;;;;AAKG;AACH,IAAA,WAAA,CAAqC,SAAoB,EAAA;QAApB,IAAS,CAAA,SAAA,GAAT,SAAS;;AAE9C;;AAEG;IACH,OAAO,kBAAkB;AAEzB;;;;;;;;;;;;;AAaG;AACH,IAAA,OAAO,IAAI,CAAC,QAA4B,EAAE,GAAQ,EAAA;AAChD,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;YAEvD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;;;;QAKrD,YAAY,CAAC,kBAAkB,KAAK,+BAA+B,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE;aAClF,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,KAAI;AAC9B,YAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,IAAI,KAAK,CACb,8CAA8C;AAC5C,oBAAA,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAA,EAAA,EAAK,KAAK,CAAA,CAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjD;;AAGH,YAAA,OAAO,IAAI,YAAY,CAAC,SAAS,CAAC;AACpC,SAAC;aACA,OAAO,CAAC,MAAK;AACZ,YAAA,YAAY,CAAC,kBAAkB,GAAG,SAAS;AAC7C,SAAC,CAAC;QAEJ,OAAO,YAAY,CAAC,kBAAkB;;AAGxC;;;;;;;;;AASG;AACH,IAAA,KAAK,CAAC,GAAQ,EAAA;;;QAGZ,MAAM,EAAE,QAAQ,EAAE,GAAG,qBAAqB,CAAC,GAAG,CAAC;QAE/C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;;AAE5D;;ACnFD;;;;;;AAMG;AACI,eAAe,MAAM,CAAC,IAAY,EAAA;IACvC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;AAClD,IAAA,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC;IACrE,MAAM,SAAS,GAAa,EAAE;IAE9B,KAAK,MAAM,CAAC,IAAI,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;AAC1C,QAAA,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;AAGjD,IAAA,OAAO,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3B;;ACfA;;AAEG;AACH,MAAM,yBAAyB,GAAG,8BAA8B;AAEhE;;AAEG;AACH,MAAM,cAAc,GAAG,YAAY;AAEnC;;;;;;;;;;;;;AAaG;AACH,MAAM,wBAAwB,mBAAmB,CAAC,MAAM,CAAA;4BAC5B,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;AAuBpC,KAAA,CAAA,GAAG;AAiCT,MAAM,YAAa,SAAQ,QAAQ,CAAA;AAAG;AACtC;AAEM,MAAO,0BAA2B,SAAQ,YAAY,CAAA;AAKxC,IAAA,QAAA;AACP,IAAA,UAAA;AALH,IAAA,wBAAwB,GAAG,IAAI,OAAO,EAAmB;AACzD,IAAA,cAAc,GAAG,IAAI,OAAO,EAAkC;IAEtE,WACkB,CAAA,QAA2C,EAClD,UAAmB,EAAA;AAE5B,QAAA,KAAK,CAAC;AACJ,YAAA,MAAM,EAAE;;gBAEN,IAAI,EAAE,CAAC,CAAS,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;;gBAEpC,KAAK,EAAE,CAAC,CAAS,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,gBAAA,IAAI,EAAE,MAAK,GAAG;AACf,aAAA;AACD,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,kBAAkB,EAAE,KAAK;AACzB,YAAA,gBAAgB,EAAE,KAAK;;;AAGvB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,gBAAgB,EAAE,IAAI;AACtB,YAAA,WAAW,EAAE,IAAI;AAClB,SAAA,CAAC;QAvBc,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACf,IAAU,CAAA,UAAA,GAAV,UAAU;;AAyBrB;;;AAGG;AACM,IAAA,MAAM,qBAAqB,CAClC,IAAwB,EACxB,QAAyB,EAAA;AAEzB,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,UAAU,EAAE;;;AAG5E,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,yBAAyB,CAAC;YAC3E,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACpC,gBAAA,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;;;QAIxB,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC;QACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QAE5C,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,yBAAyB,CAAC;YAEnF,IAAI,aAAa,EAAE;;;;;AAKjB,gBAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,mCAAmC,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;;;;;;YAOpE,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvC,gBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;AAC7D,oBAAA,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC;;AAEzC,aAAC,CAAC;;AAGJ,QAAA,OAAO,WAAW;;AAGpB;;AAEG;AACK,IAAA,YAAY,CAAC,QAAyB,EAAA;QAC5C,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;;YAErC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE;;;QAI3C,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,4BAA4B,CAAC;AACzE,QAAA,MAAM,QAAQ,GACZ,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,IAAI,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,IAAI,IAAI;QAE9F,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAE3C,QAAA,OAAO,QAAQ;;AAGjB;;;AAGG;AACK,IAAA,mCAAmC,CACzC,QAAyB,EACzB,KAAa,EACb,IAAwB,EAAA;QAExB,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC/C;;QAGF,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,wBAAwB,CAAC,EAAE;;AAEhE,YAAA,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;YAE3C;;QAGF,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC/C,QAAA,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AACnC,QAAA,MAAM,CAAC,WAAW,GAAG,wBAAwB;;;QAG7C,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;AACxC,QAAA,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAE9C;;AC3MD;;;;;;;;AAQG;MACU,QAAQ,CAAA;AACnB;;AAEG;AACH,IAAA,QAAQ;AAER;;AAEG;AACc,IAAA,KAAK,GAAG,IAAI,GAAG,EAAyB;AAEzD;;AAEG;AACK,IAAA,IAAI;AAEZ;;AAEG;AACK,IAAA,IAAI;AAEZ;;;AAGG;AACH,IAAA,WAAA,CAAY,QAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;AAG1B;;;;AAIG;AACH,IAAA,GAAG,CAAC,GAAQ,EAAA;QACV,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QAChC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAErB,OAAO,IAAI,CAAC,KAAK;;AAGnB,QAAA,OAAO,SAAS;;AAGlB;;;;;;AAMG;IACH,GAAG,CAAC,GAAQ,EAAE,KAAY,EAAA;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,EAAE;;AAEd,YAAA,UAAU,CAAC,KAAK,GAAG,KAAK;AACxB,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;YAE3B;;;AAIF,QAAA,MAAM,OAAO,GAAqB,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QAClF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QAEvB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE;;AAEnC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE;YAC9B,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;;;;AAKjC;;;AAGG;AACK,IAAA,SAAS,CAAC,IAAsB,EAAA;AACtC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,SAAS;AAErB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI;;AAGvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;AAEhB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;;AAIpB;;;AAGG;AACK,IAAA,UAAU,CAAC,IAAsB,EAAA;AACvC,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;aACrB;AACL,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;AAGvB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;aACrB;AACL,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;;AAIzB;;;AAGG;AACK,IAAA,UAAU,CAAC,IAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;AAGtB;;;AAGG;IACK,UAAU,GAAA;AAChB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;QACtB,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGvB,QAAA,OAAO,IAAI;;AAEd;;ACpID;;;AAGG;AACH,MAAM,4BAA4B,GAAG,EAAE;AAEvC;;;;;;;;;;AAUG;AACH,MAAM,oBAAoB,GAA+B;AACvD,IAAA,CAAC,UAAU,CAAC,SAAS,GAAG,KAAK;AAC7B,IAAA,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK;AAC1B,IAAA,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE;CACxB;AA0BD;;;;AAIG;MACU,gBAAgB,CAAA;AAoBE,IAAA,OAAA;AAnB7B;;;;AAIG;AACc,IAAA,sBAAsB;AAEvC;;;;AAIG;AACM,IAAA,KAAK;AAEd;;;;AAIG;AACH,IAAA,WAAA,CAA6B,UAA6C,EAAE,EAAA;QAA/C,IAAO,CAAA,OAAA,GAAP,OAAO;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,KAAK;QAC1E,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE;AAEzC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YACnC,IAAI,CAAC,0BAA0B,GAAG,IAAI,0BAA0B,CAAC,CAAC,IAAY,KAAI;AAChF,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI;gBAE9C,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE;AACpD,aAAC,CAAC;;;AAIN;;AAEG;IACc,QAAQ,GAAG,qBAAqB,EAAE;AAEnD;;AAEG;IACc,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;AAEzD;;AAEG;AACK,IAAA,MAAM;AAEd;;AAEG;AACK,IAAA,0BAA0B;AAElC;;AAEG;AACK,IAAA,QAAQ;AAEhB;;AAEG;AACc,IAAA,WAAW,GAAG,IAAI,WAAW,EAAE;AAEhD;;;;;;AAMG;AACc,IAAA,mBAAmB,GAAG,IAAI,QAAQ,CAAiB,4BAA4B,CAAC;AAEjG;;;;;;;;;;AAUG;AACH,IAAA,MAAM,MAAM,CAAC,OAAgB,EAAE,cAAwB,EAAA;QACrD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;AAChC,QAAA,IAAI,CAAC,MAAM,KAAK,MAAM,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;QAE3C,IAAI,CAAC,YAAY,EAAE;;AAEjB,YAAA,OAAO,IAAI;;QAGb,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY;AACvD,QAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,YAAA,OAAO,sBAAsB,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;;AAGtF,QAAA,IAAI,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;YACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC;YAC9D,IAAI,QAAQ,EAAE;AACZ,gBAAA,OAAO,QAAQ;;;QAInB,OAAO,gBAAgB,CACrB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,CAAC,EAC3D,OAAO,CAAC,MAAM,EACd,CAAA,aAAA,EAAgB,OAAO,CAAC,GAAG,CAAE,CAAA,CAC9B;;AAGH;;;;;;;;;AASG;AACK,IAAA,MAAM,WAAW,CACvB,OAAgB,EAChB,YAAmC,EAAA;AAEnC,QAAA,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,YAAY;AAC5C,QAAA,IAAI,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;AACvC,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO;QAC1B,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,EAAE;AACzC,YAAA,OAAO,IAAI;;QAGb,MAAM,SAAS,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC;QAC/D,MAAM,EACJ,QAAQ,EAAE,EAAE,MAAM,EAAE,EACpB,MAAM,GACP,GAAG,IAAI;QAER,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AACrC,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;AAC7D,QAAA,MAAM,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,GAAG;QAExB,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK;AAC9C,cAAE,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,cAAc,EAAE;AACrE,cAAE,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,EAAE;AACzB,gBAAA,OAAO,EAAE;AACP,oBAAA,gBAAgB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjC,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,cAAc,EAAE,yBAAyB;AACzC,oBAAA,IAAI,MAAM,KAAK,SAAS,GAAG,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D,oBAAA,GAAG,OAAO;AACX,iBAAA;AACF,aAAA,CAAC;;AAGR;;;;;;;;;;AAUG;AACK,IAAA,MAAM,eAAe,CAC3B,OAAgB,EAChB,YAAmC,EACnC,cAAwB,EAAA;QAExB,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,YAAY;QAE7D,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,UAAU,KAAK,UAAU,CAAC,SAAS,EAAE;AACvE,YAAA,OAAO,IAAI;;QAGb,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,MAAM,iBAAiB,GAAqB,EAAE;AAE9C,QAAA,MAAM,EACJ,QAAQ,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,EAC/B,MAAM,GACP,GAAG,IAAI;;AAGR,QAAA,MAAM,YAAY,GAAG;YACnB,MAAM;YACN,OAAO,EAAE,IAAI,OAAO,CAAC;AACnB,gBAAA,cAAc,EAAE,yBAAyB;AACzC,gBAAA,IAAI,MAAM,KAAK,SAAS,GAAG,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D,gBAAA,GAAG,OAAO;aACX,CAAC;SACH;AAED,QAAA,IAAI,UAAU,KAAK,UAAU,CAAC,MAAM,EAAE;;YAEpC,iBAAiB,CAAC,IAAI,CACpB;AACE,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,QAAQ,EAAE,OAAO;aAClB,EACD;AACE,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,QAAQ,EAAE,cAAc;aACzB,EACD;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,QAAQ,EAAE,YAAY;AACvB,aAAA,CACF;;AACI,aAAA,IAAI,UAAU,KAAK,UAAU,CAAC,MAAM,EAAE;;AAE3C,YAAA,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE;AACpE,YAAA,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC;AAEzD,YAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;;AAGzC,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,iBAAiB,CAAC,IAAI,CAAC;AACrB,gBAAA,OAAO,EAAE,SAAS;AAClB,gBAAA,QAAQ,EAAE,MAAM;AACjB,aAAA,CAAC;;AAGJ,QAAA,IAAI,CAAC,QAAQ,KAAK,MAAM,SAAS,EAAE;QACnC,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE;AACnD,QAAA,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC;QAEzD,MAAM,MAAM,GAAG,MAAM,aAAa,CAChC,IAAI,EACJ,IAAI,CAAC,QAAQ,EACb,GAAG,EACH,iBAAiB,EACjB,oBAAoB,CAAC,UAAU,CAAC,CACjC;AAED,QAAA,IAAI,MAAM,CAAC,kBAAkB,EAAE;AAC7B,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,OAAO,sBAAsB,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;;QAG1D,MAAM,EAAE,0BAA0B,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,IAAI;;AAG7E,QAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC;YAChC,MAAM,KAAK,CAAC,UAAU,EAAA;AACpB,gBAAA,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE;gBAE3C,IAAI,CAAC,0BAA0B,EAAE;oBAC/B,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;oBACpD,UAAU,CAAC,KAAK,EAAE;oBAElB;;AAGF,gBAAA,IAAI,mBAAmB;AACvB,gBAAA,IAAI;AACF,oBAAA,IAAI,UAAU,KAAK,UAAU,CAAC,MAAM,EAAE;AACpC,wBAAA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC;AAC3C,wBAAA,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC;wBACvD,IAAI,CAAC,mBAAmB,EAAE;4BACxB,mBAAmB,GAAG,MAAM,0BAA0B,CAAC,OAAO,CAAC,YAAY,CAAC;AAC5E,4BAAA,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC;;;yBAEnD;wBACL,mBAAmB,GAAG,MAAM,0BAA0B,CAAC,OAAO,CAAC,YAAY,CAAC;;;gBAE9E,OAAO,KAAK,EAAE;;oBAEd,OAAO,CAAC,KAAK,CAAC,CAAA,mDAAA,EAAsD,GAAG,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC;;AAGpF,gBAAA,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,mBAAmB,IAAI,YAAY,CAAC,CAAC;gBAC3E,UAAU,CAAC,KAAK,EAAE;aACnB;AACF,SAAA,CAAC;AAEF,QAAA,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC;;AAG3C;;;;;;;;;AASG;AACK,IAAA,+BAA+B,CAAC,OAAgB,EAAA;AACtD,QAAA,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;;AAEtC,YAAA,SAAS,GAAG,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC;;AAGnD,QAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;;AAElC,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;;YAEzD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;;AAG9C,QAAA,OAAO,iBAAiB,CAAC,SAAS,CAAC;;AAGrC;;;;;;;AAOG;AACK,IAAA,MAAM,mBAAmB,CAC/B,IAAY,EACZ,GAAQ,EACR,OAAsC,EAAA;QAEtC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;AACxC,YAAA,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;;AAGlE,QAAA,IAAI,OAAO,EAAE,MAAM,EAAE;AACnB,YAAA,IAAI,GAAG,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC;;AAGhD,QAAA,OAAO,IAAI;;AAEd;AAED,IAAI,gBAA8C;AAElD;;;;;;;;AAQG;AACG,SAAU,2BAA2B,CACzC,OAA2C,EAAA;IAE3C,QAAQ,gBAAgB,KAAK,IAAI,gBAAgB,CAAC,OAAO,CAAC;AAC5D;AAEA;;;;;;AAMG;SACa,uBAAuB,GAAA;AACrC,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;;AAIjD,QAAAC,wBAAwB,EAAE;;IAG5B,gBAAgB,GAAG,SAAS;AAC9B;AAEA;;;;;;;;;;AAUG;AACH,SAAS,wBAAwB,CAAC,IAAY,EAAE,OAA0B,EAAA;IACxE,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;AAChD,IAAA,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI;;;;;IAMb,OAAO;AACL,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC;AAC3B,QAAA,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA,gCAAA,EAAmC,GAAG,CAAA,EAAA,CAAI,CAAC;AACnE,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AACzB,KAAA,CAAC,IAAI,CAAC,IAAI,CAAC;AACd;AAEA;;;;;;;AAOG;AACH,SAAS,sBAAsB,CAAC,QAAgB,EAAE,MAAM,GAAG,GAAG,EAAA;AAC5D,IAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;QACxB,MAAM;AACN,QAAA,OAAO,EAAE;AACP,YAAA,UAAU,EAAE,QAAQ;AACrB,SAAA;AACF,KAAA,CAAC;AACJ;;AC9eA;;;;;;;;;;;;;;;;;;AAkBG;AACa,SAAA,2BAA2B,CAAC,GAAQ,EAAE,QAAgB,EAAA;AACpE,IAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG;;AAGxB,IAAA,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC3B,IAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;AAC3B,QAAA,KAAK,EAAE;;;IAIT,IAAI,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;AACtC,IAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;AACd,QAAA,GAAG,GAAG,QAAQ,CAAC,MAAM;;;IAIvB,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;AACnC;AAEA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACH,SAAS,mBAAmB,CAAC,MAAc,EAAA;AACzC,IAAA,IAAI,MAAM,KAAK,GAAG,EAAE;QAClB,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;IAG5B,MAAM,YAAY,GAAG;SAClB,KAAK,CAAC,GAAG;AACT,SAAA,GAAG,CAAC,CAAC,IAAI,KAAI;QACZ,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QAEtE,IAAI,OAAO,GAAG,YAAY,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;AAC5F,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;AAC/E,YAAA,OAAO,GAAG,CAAC,CAAC;;AAGd,QAAA,OAAO,CAAC,MAAM,EAAE,OAAO,CAAU;AACnC,KAAC;SACA,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,QAAQ,GAAG,QAAQ,CAAC;AAE5E,IAAA,OAAO,IAAI,GAAG,CAAC,YAAY,CAAC;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACa,SAAA,kBAAkB,CAChC,MAAc,EACd,gBAAuC,EAAA;AAEvC,IAAA,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,QAAA,OAAO,gBAAgB,CAAC,CAAC,CAAC;;AAG5B,IAAA,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC;;;;;IAMjD,IAAI,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI,KAAK,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AACpF,QAAA,OAAO,gBAAgB,CAAC,CAAC,CAAC;;;;AAK5B,IAAA,MAAM,0BAA0B,GAAG,IAAI,GAAG,EAAkB;AAC5D,IAAA,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;QACrC,0BAA0B,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;;;AAIjE,IAAA,IAAI,SAA6B;AACjC,IAAA,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAAU;IACtD,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,aAAa,EAAE;AAC7C,QAAA,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC;AAChD,QAAA,IAAI,OAAO,KAAK,CAAC,EAAE;AACjB,YAAA,4BAA4B,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAClD,YAAA,SAAS;;;AAIX,QAAA,IAAI,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACpD,YAAA,OAAO,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC;;;;AAKzD,QAAA,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B;;AAGF,QAAA,MAAM,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QACvD,KAAK,MAAM,eAAe,IAAI,0BAA0B,CAAC,IAAI,EAAE,EAAE;AAC/D,YAAA,IAAI,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;AAC9C,gBAAA,SAAS,GAAG,0BAA0B,CAAC,GAAG,CAAC,eAAe,CAAC;AAC3D,gBAAA,MAAM;;;;AAKZ,IAAA,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,QAAA,OAAO,SAAS;;;IAIlB,KAAK,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,0BAA0B,EAAE;QACnE,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACvD,YAAA,OAAO,MAAM;;;AAGnB;AAEA;;;;;;;;;;;AAWG;AACH,SAAS,eAAe,CAAC,MAAc,EAAA;AACrC,IAAA,OAAO,MAAM,CAAC,WAAW,EAAE;AAC7B;;AC3LA;;;;;;;AAOG;MACU,gBAAgB,CAAA;AAC3B;;;;;;;AAOG;AACH,IAAA,OAAO,uBAAuB,GAAG,KAAK;AAEtC;;;;;;AAMG;IACH,OAAO,MAAM,kBAAkB,IAAI,KAAK,EAAE;AAE1C;;AAEG;IACc,QAAQ,GAAG,2BAA2B,EAAE;AAEzD;;AAEG;IACc,gBAAgB,GAA0B,MAAM,CAAC,IAAI,CACpE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC/B;AAED;;AAEG;AACc,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAsC;AAEjF;;;;;;;;;;AAUG;AACH,IAAA,MAAM,MAAM,CAAC,OAAgB,EAAE,cAAwB,EAAA;QACrD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;QAEnE,IAAI,SAAS,EAAE;YACb,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC;;QAGlD,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEpC,YAAA,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;;AAGpD,QAAA,OAAO,IAAI;;AAGb;;;;;;;AAOG;AACK,IAAA,6BAA6B,CAAC,OAAgB,EAAA;QACpD,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,QAAQ;;QAGpD,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;AACzC,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,YAAA,OAAO,IAAI;;;;AAKb,QAAA,MAAM,eAAe,GAAG,kBAAkB,CACxC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,GAAG,EAC7C,IAAI,CAAC,gBAAgB,CACtB;QAED,IAAI,eAAe,EAAE;AACnB,YAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,eAAe,CAAC;AACjD,YAAA,IAAI,OAAO,KAAK,SAAS,EAAE;AACzB,gBAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACxB,MAAM,EAAE,GAAG;AACX,oBAAA,OAAO,EAAE;AACP,wBAAA,UAAU,EAAE,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC3C,wBAAA,MAAM,EAAE,iBAAiB;AAC1B,qBAAA;AACF,iBAAA,CAAC;;;AAIN,QAAA,OAAO,IAAI;;AAGb;;;;;;;;;;AAUG;IACK,MAAM,6BAA6B,CAAC,OAAgB,EAAA;;QAE1D,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC;QAC7D,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,OAAO,IAAI;;;;AAKb,QAAA,MAAM,4BAA4B,GAChC,UAAU,CAAC,4BAAkE;QAE/E,MAAM,SAAS,GAAG,4BAA4B,CAAC;YAC7C,sBAAsB,EAAE,gBAAgB,CAAC,uBAAuB;YAChE,KAAK,EAAE,gBAAgB,CAAC,MAAM;AAC/B,SAAA,CAAC;AAEF,QAAA,OAAO,SAAS;;AAGlB;;;;;AAKG;AACK,IAAA,oBAAoB,CAAC,eAAuB,EAAA;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAC;QACnE,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,gBAAgB;;AAGzB,QAAA,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,QAAQ;AACrC,QAAA,MAAM,UAAU,GAAG,WAAW,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,OAAO,SAAS;;AAGlB,QAAA,MAAM,iBAAiB,GAAG,UAAU,EAAE;QACtC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC;AAE7D,QAAA,OAAO,iBAAiB;;AAG1B;;;;;;;;;;AAUG;AACK,IAAA,0BAA0B,CAAC,GAAQ,EAAA;AACzC,QAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,YAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC;;QAGtC,MAAM,eAAe,GAAG,2BAA2B,CAAC,GAAG,EAAE,QAAQ,CAAC;AAElE,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC;;;;ACrLtF;;;;;;;;;;;;;;;;;;;;;AAqBG;AACG,SAAU,oBAAoB,CAAC,OAA+B,EAAA;AACjE,IAAA,OAAyE,CACxE,wBAAwB,CACzB,GAAG,IAAI;AAER,IAAA,OAAO,OAAO;AAChB;;;;"}