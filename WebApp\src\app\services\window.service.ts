import { Injectable } from '@angular/core';
import { PlatformService } from './platform.service';

@Injectable({
  providedIn: 'root'
})
export class WindowService {
  constructor(private platformService: PlatformService) {}

  open(url: string, target?: string, features?: string): Window | null {
    if (!this.platformService.isBrowser) {
      console.warn('Window.open called on server side, ignoring');
      return null;
    }

    const window = this.platformService.getWindow();
    if (window) {
      return window.open(url, target, features);
    }
    return null;
  }

  get location(): Location | null {
    return this.platformService.getLocation();
  }

  get innerWidth(): number {
    if (!this.platformService.isBrowser) return 0;
    const window = this.platformService.getWindow();
    return window ? window.innerWidth : 0;
  }

  get innerHeight(): number {
    if (!this.platformService.isBrowser) return 0;
    const window = this.platformService.getWindow();
    return window ? window.innerHeight : 0;
  }

  addEventListener(type: string, listener: EventListener, options?: boolean | AddEventListenerOptions): void {
    if (!this.platformService.isBrowser) return;
    const window = this.platformService.getWindow();
    if (window) {
      window.addEventListener(type, listener, options);
    }
  }

  removeEventListener(type: string, listener: EventListener, options?: boolean | EventListenerOptions): void {
    if (!this.platformService.isBrowser) return;
    const window = this.platformService.getWindow();
    if (window) {
      window.removeEventListener(type, listener, options);
    }
  }

  scrollTo(x: number, y: number): void {
    if (!this.platformService.isBrowser) return;
    const window = this.platformService.getWindow();
    if (window) {
      window.scrollTo(x, y);
    }
  }

  alert(message: string): void {
    if (!this.platformService.isBrowser) {
      console.log('Alert (server-side):', message);
      return;
    }
    const window = this.platformService.getWindow();
    if (window) {
      window.alert(message);
    }
  }

  confirm(message: string): boolean {
    if (!this.platformService.isBrowser) {
      console.log('Confirm (server-side):', message);
      return false;
    }
    const window = this.platformService.getWindow();
    return window ? window.confirm(message) : false;
  }
}
