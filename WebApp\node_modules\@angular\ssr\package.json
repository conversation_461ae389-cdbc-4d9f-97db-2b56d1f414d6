{"name": "@angular/ssr", "version": "20.0.4", "description": "Angular server side rendering utilities", "type": "module", "license": "MIT", "homepage": "https://github.com/angular/angular-cli", "keywords": ["angular", "ssr", "universal"], "ng-add": {"save": "dependencies"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": "^20.0.0", "@angular/core": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0"}, "peerDependenciesMeta": {"@angular/platform-server": {"optional": true}}, "devDependencies": {"@angular-devkit/schematics": "workspace:*", "@angular/common": "20.0.0", "@angular/compiler": "20.0.0", "@angular/core": "20.0.0", "@angular/platform-browser": "20.0.0", "@angular/platform-server": "20.0.0", "@angular/router": "20.0.0", "@schematics/angular": "workspace:*"}, "sideEffects": false, "schematics": "./schematics/collection.json", "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "module": "./fesm2022/ssr.mjs", "typings": "./index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ssr.mjs"}, "./node": {"types": "./node/index.d.ts", "default": "./fesm2022/node.mjs"}}}