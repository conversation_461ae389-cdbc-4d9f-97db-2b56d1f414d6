import { Injectable, inject } from '@angular/core';
import { PlatformService } from './platform.service';

@Injectable({
  providedIn: 'root'
})
export class TransferStateService {
  private platformService = inject(PlatformService);
  private serverData = new Map<string, any>(); // Simple server-side storage

  constructor() {
    if (this.platformService.isServer) {
      console.log('[TransferStateService] Initialized for server-side rendering');
    } else {
      console.log('[TransferStateService] Initialized for client-side');
    }
  }

  // Generic methods for any data type
  set<T>(key: string, value: T): void {
    if (this.platformService.isServer) {
      this.serverData.set(key, value);
      console.log(`[TransferStateService] Set data for key: ${key}`);
    } else {
      // On client, store in sessionStorage for simplicity
      try {
        sessionStorage.setItem(`transfer_${key}`, JSON.stringify(value));
      } catch (error) {
        console.warn(`[TransferStateService] Could not store data for key: ${key}`, error);
      }
    }
  }

  get<T>(key: string, defaultValue: T): T {
    if (this.platformService.isServer) {
      return this.serverData.get(key) || defaultValue;
    } else {
      try {
        const stored = sessionStorage.getItem(`transfer_${key}`);
        if (stored) {
          const value = JSON.parse(stored);
          // Remove after retrieval
          sessionStorage.removeItem(`transfer_${key}`);
          console.log(`[TransferStateService] Retrieved data for key: ${key}`);
          return value;
        }
      } catch (error) {
        console.warn(`[TransferStateService] Could not retrieve data for key: ${key}`, error);
      }
      return defaultValue;
    }
  }

  has(key: string): boolean {
    if (this.platformService.isServer) {
      return this.serverData.has(key);
    } else {
      return sessionStorage.getItem(`transfer_${key}`) !== null;
    }
  }

  remove(key: string): void {
    if (this.platformService.isServer) {
      this.serverData.delete(key);
    } else {
      sessionStorage.removeItem(`transfer_${key}`);
    }
    console.log(`[TransferStateService] Removed data for key: ${key}`);
  }

  // Specific methods for common data types
  setSubjects(subjects: any[]): void {
    this.set('subjects', subjects);
  }

  getSubjects(): any[] {
    return this.get('subjects', []);
  }

  hasSubjects(): boolean {
    return this.has('subjects');
  }

  setExams(exams: any[]): void {
    this.set('exams', exams);
  }

  getExams(): any[] {
    return this.get('exams', []);
  }

  hasExams(): boolean {
    return this.has('exams');
  }

  setUserProfile(profile: any): void {
    this.set('userProfile', profile);
  }

  getUserProfile(): any {
    return this.get('userProfile', null);
  }

  hasUserProfile(): boolean {
    return this.has('userProfile');
  }

  setAuthState(authState: any): void {
    this.set('authState', authState);
  }

  getAuthState(): any {
    return this.get('authState', { isAuthenticated: false, token: null, username: null });
  }

  hasAuthState(): boolean {
    return this.has('authState');
  }

  // Utility methods
  setApiResponse<T>(endpoint: string, data: T): void {
    const key = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    this.set(key, data);
  }

  getApiResponse<T>(endpoint: string, defaultValue: T): T {
    const key = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    return this.get(key, defaultValue);
  }

  hasApiResponse(endpoint: string): boolean {
    const key = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    return this.has(key);
  }

  // Clear all transfer state data (useful for cleanup)
  clearAll(): void {
    if (this.platformService.isBrowser) {
      // Get all keys and remove them
      const keys = ['subjects', 'exams', 'userProfile', 'authState'];
      keys.forEach(key => {
        if (this.has(key)) {
          this.remove(key);
        }
      });
      console.log('[TransferStateService] Cleared all transfer state data');
    }
  }

  // Debug method to log all stored keys
  logStoredKeys(): void {
    if (this.platformService.isBrowser) {
      const commonKeys = ['subjects', 'exams', 'userProfile', 'authState'];
      const storedKeys = commonKeys.filter(key => this.has(key));
      console.log('[TransferStateService] Stored keys:', storedKeys);
    }
  }
}
