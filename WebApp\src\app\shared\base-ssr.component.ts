import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { PlatformService } from '../services/platform.service';
import { SEOService } from '../services/seo.service';
import { TransferStateService } from '../services/transfer-state.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  template: ''
})
export abstract class BaseSSRComponent implements OnInit, OnDestroy {
  protected destroy$ = new Subject<void>();
  protected isLoading = false;
  protected isSSR = false;

  protected router = inject(Router);
  protected authService = inject(AuthService);
  protected platformService = inject(PlatformService);
  protected seoService = inject(SEOService);
  protected transferStateService = inject(TransferStateService);

  ngOnInit(): void {
    this.isSSR = this.platformService.isServer;

    // Set SEO data
    this.setSEOData();

    // Handle authentication on client-side only
    if (this.platformService.isBrowser) {
      this.handleClientSideAuth();
    }

    // Initialize component
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  protected abstract setSEOData(): void;

  protected abstract initializeComponent(): void;

  protected handleClientSideAuth(): void {
    // Override in components that need authentication
    if (!this.authService.isAuthenticated()) {
      console.log('[BaseSSR] User not authenticated, redirecting to login');
      this.router.navigate(['/login']);
    }
  }

  protected safeSubscribe<T>(observable: any, callback: (data: T) => void, errorCallback?: (error: any) => void, transferKey?: string): void {
    if (this.platformService.isBrowser) {
      // On client, check if data is available in transfer state first
      if (transferKey && this.transferStateService.has(transferKey)) {
        const cachedData = this.transferStateService.get<T>(transferKey, null as any);
        if (cachedData !== null) {
          console.log(`[SSR] Using cached data from transfer state: ${transferKey}`);
          callback(cachedData);
          return;
        }
      }

      // If no cached data, make the API call
      observable
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: callback,
          error: errorCallback || ((error: any) => {
            console.error('API Error:', error);
            this.isLoading = false;
          })
        });
    } else {
      // On server, don't make API calls but store mock data if needed
      console.log('[SSR] Skipping API call on server-side');
      if (transferKey) {
        // Store empty data to prevent client-side API calls
        this.transferStateService.set(transferKey, [] as any);
      }
    }
  }

  protected setLoadingState(loading: boolean): void {
    this.isLoading = loading;
  }
}
