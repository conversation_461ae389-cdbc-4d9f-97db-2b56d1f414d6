import { Compo<PERSON>, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { PlatformService } from '../services/platform.service';
import { SEOService } from '../services/seo.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  template: ''
})
export abstract class BaseSSRComponent implements OnInit, OnDestroy {
  protected destroy$ = new Subject<void>();
  protected isLoading = false;
  protected isSSR = false;
  
  protected router = inject(Router);
  protected authService = inject(AuthService);
  protected platformService = inject(PlatformService);
  protected seoService = inject(SEOService);

  ngOnInit(): void {
    this.isSSR = this.platformService.isServer;
    
    // Set SEO data
    this.setSEOData();
    
    // Handle authentication on client-side only
    if (this.platformService.isBrowser) {
      this.handleClientSideAuth();
    }
    
    // Initialize component
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  protected abstract setSEOData(): void;
  
  protected abstract initializeComponent(): void;

  protected handleClientSideAuth(): void {
    // Override in components that need authentication
    if (!this.authService.isAuthenticated()) {
      console.log('[BaseSSR] User not authenticated, redirecting to login');
      this.router.navigate(['/login']);
    }
  }

  protected safeSubscribe<T>(observable: any, callback: (data: T) => void, errorCallback?: (error: any) => void): void {
    if (this.platformService.isBrowser) {
      observable
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: callback,
          error: errorCallback || ((error: any) => {
            console.error('API Error:', error);
            this.isLoading = false;
          })
        });
    } else {
      // On server, don't make API calls
      console.log('[SSR] Skipping API call on server-side');
    }
  }

  protected setLoadingState(loading: boolean): void {
    this.isLoading = loading;
  }
}
