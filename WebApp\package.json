{"name": "web-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.2", "@angular/cdk": "^19.1.1", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/material": "^19.1.1", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@fortawesome/fontawesome-free": "^6.7.2", "@microsoft/signalr": "^8.0.7", "@primeng/themes": "^19.0.5", "@types/luxon": "^3.4.2", "luxon": "^3.5.0", "nswag": "^14.2.0", "primeicons": "^7.0.0", "primeng": "^19.0.5", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.3", "@angular/cli": "^19.1.3", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}