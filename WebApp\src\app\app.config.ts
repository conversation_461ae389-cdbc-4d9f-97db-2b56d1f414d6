import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withPreloading, PreloadAllModules } from '@angular/router';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { API_BASE_URL } from '../service-proxies/service-proxies';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { routes } from './app.routes';
import { ServiceProxyModule } from '../service-proxies/service-proxy.module';
import { AuthInterceptor } from './services/auth.interceptor';
import { ssrInterceptor } from './services/ssr.interceptor';

export function getRemoteServiceBaseUrl(): string {
  return 'https://examease.tryasp.net';
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withPreloading(PreloadAllModules)),
    provideAnimationsAsync(),
    { provide: API_BASE_URL, useFactory: getRemoteServiceBaseUrl },
    provideHttpClient(
      withFetch(),
      withInterceptors([ssrInterceptor, AuthInterceptor])
    ),
    ServiceProxyModule,
  ]
};
