
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "node_modules/@angular/animations/fesm2022/browser.mjs": [
    {
      "path": "chunk-EFP6A2FU.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 21033, hash: '9d02dc059b43db3d623a8225f65aa2311e825f8d1e23d27e54d1a241f19334c4', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 18790, hash: '0757507f675f31aad19512bd8769a504aa141467715ec224b805d2320ba1614e', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-S7CV2VSN.css': {size: 97999, hash: '69rJP+iGA28', text: () => import('./assets-chunks/styles-S7CV2VSN_css.mjs').then(m => m.default)}
  },
};
