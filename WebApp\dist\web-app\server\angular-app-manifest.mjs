
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "node_modules/@angular/animations/fesm2022/browser.mjs": [
    {
      "path": "chunk-EFP6A2FU.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 19445, hash: '9f222e2701ed9160fcab1bf6c5f0aded5f863aec6b4e1d5772adea8f2eaf9aba', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17202, hash: '072e57dc7d9d7c098fdd80f1c75d9e8d9aac92e6c8dee4bed76bf76133269f31', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-S7CV2VSN.css': {size: 97999, hash: '69rJP+iGA28', text: () => import('./assets-chunks/styles-S7CV2VSN_css.mjs').then(m => m.default)}
  },
};
