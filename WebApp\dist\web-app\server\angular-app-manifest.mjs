
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "node_modules/@angular/animations/fesm2022/browser.mjs": [
    {
      "path": "chunk-O2QMN4HF.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 19445, hash: 'c46b99d18a0d203ae9fb78eec4a22e4c327f57dd7ef0af56f36080a3330e4ab9', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17202, hash: '3eb578ef1bb54c63c41ecfa64abd08a3fece1d3385fd8f39fb0a1959156a60fd', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-S7CV2VSN.css': {size: 97999, hash: '69rJP+iGA28', text: () => import('./assets-chunks/styles-S7CV2VSN_css.mjs').then(m => m.default)}
  },
};
