
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "node_modules/@angular/animations/fesm2022/browser.mjs": [
    {
      "path": "chunk-O2QMN4HF.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 19445, hash: '1a8e294e583372d6e7d15cd8bef0c94edd7395b2ad8bb07570e9b85e71e53d17', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17202, hash: '45f4274899695e114719677b653bf0f637ae43285a0a0528097f6e490fcccfb7', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-S7CV2VSN.css': {size: 97999, hash: '69rJP+iGA28', text: () => import('./assets-chunks/styles-S7CV2VSN_css.mjs').then(m => m.default)}
  },
};
