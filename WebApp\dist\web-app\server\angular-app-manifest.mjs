
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "node_modules/@angular/animations/fesm2022/browser.mjs": [
    {
      "path": "chunk-EFP6A2FU.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 21033, hash: 'c7a305df46447d7d797bd48511b64ed7f10fece5aadaa0242e2d1a83ead112f8', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 18790, hash: '11510c80789b9b229d13c8088cff719a53159dfaab230218e33a1981d0a2bce4', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-S7CV2VSN.css': {size: 97999, hash: '69rJP+iGA28', text: () => import('./assets-chunks/styles-S7CV2VSN_css.mjs').then(m => m.default)}
  },
};
