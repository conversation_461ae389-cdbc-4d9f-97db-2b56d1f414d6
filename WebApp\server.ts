import { APP_BASE_HREF } from '@angular/common';
import { renderApplication } from '@angular/platform-server';
import express from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, join, resolve } from 'node:path';
import { readFileSync } from 'node:fs';
import bootstrap from './src/main.server';

// The Express server
const app = express();
const serverDistFolder = dirname(fileURLToPath(import.meta.url));
const browserDistFolder = resolve(serverDistFolder, '../browser');
const indexHtml = join(serverDistFolder, 'index.server.html');

app.set('view engine', 'html');
app.set('views', browserDistFolder);

// Example Express Rest API endpoints
// app.get('/api/**', (req, res) => { });
// Serve static files from /browser
app.get('*.*', express.static(browserDistFolder, {
  maxAge: '1y'
}));

// All regular routes use the Angular engine
app.get('*', async (req, res, _next) => {
  const { protocol, originalUrl, baseUrl, headers } = req;

  // Skip certain requests that shouldn't be handled by Angular
  if (originalUrl.includes('.well-known') ||
      originalUrl.includes('favicon.ico') ||
      originalUrl.includes('.js') ||
      originalUrl.includes('.css') ||
      originalUrl.includes('.map')) {
    res.status(404).send('Not found');
    return;
  }

  try {
    const html = await renderApplication(bootstrap, {
      document: readFileSync(indexHtml, 'utf-8'),
      url: `${protocol}://${headers.host}${originalUrl}`,
      platformProviders: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
    });
    res.send(html);
  } catch (err: any) {
    console.error('SSR Error:', err);
    // Send a fallback response instead of crashing
    res.status(500).send(`
      <!DOCTYPE html>
      <html>
        <head><title>Error</title></head>
        <body><h1>Server Error</h1><p>Please try again later.</p></body>
      </html>
    `);
  }
});

const PORT = process.env['PORT'] || 4000;

// Start up the Node server
app.listen(PORT, () => {
  console.log(`Node Express server listening on http://localhost:${PORT}`);
});

export default app;
