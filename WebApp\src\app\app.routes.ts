import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';

import { ExamListComponent } from './ExamSchedule/exam-list/exam-list.component';
import { CreateOrUpdateExamComponent } from './ExamSchedule/create-or-update-exam/create-or-update-exam.component';
import { ExamDetailsComponent } from './ExamSchedule/exam-details/exam-details.component';
import { StudyPlanComponent } from './study-plan/study-plan.component';
import { SubjectListComponent } from './subject/subject-list.component';
import { SubjectFormComponent } from './subject/subject-form.component';
import { SubjectViewComponent } from './subject/subject-view.component';
import { authGuard } from './guards/auth.guard';
import { LandingComponent } from './landing/landing.component';

export const routes: Routes = [
  { path: '', redirectTo: '/landing', pathMatch: 'full' },
  {
    path: 'landing',
    component: LandingComponent,
    title: 'ExamEase - Landing',
    data: { prerender: true }
  },
  {
    path: 'login',
    component: LoginComponent,
    title: 'ExamEase - Login',
    data: { prerender: true }
  },
  {
    path: 'register',
    component: RegisterComponent,
    title: 'ExamEase - Register',
    data: { prerender: true }
  },
  { path: 'exam', component: ExamListComponent, canActivate: [authGuard], title: 'ExamEase - Exams' },
  { path: 'exam-details/:id', component: ExamDetailsComponent, canActivate: [authGuard], title: 'ExamEase - Exam Details' },
  { path: 'create-exam', component: CreateOrUpdateExamComponent, canActivate: [authGuard], title: 'ExamEase - Create Exam' },
  { path: 'create-exam/:id', component: CreateOrUpdateExamComponent, canActivate: [authGuard], title: 'ExamEase - Edit Exam' },
  { path: 'studyplan', component: StudyPlanComponent, canActivate: [authGuard], title: 'ExamEase - Study Plan' },
  { path: 'studyplan/:id', component: StudyPlanComponent, canActivate: [authGuard], title: 'ExamEase - Study Plan Details' },
  { path: 'subjects', component: SubjectListComponent, canActivate: [authGuard], title: 'ExamEase - Subjects' },
  { path: 'subjects/create', component: SubjectFormComponent, canActivate: [authGuard], title: 'ExamEase - Create Subject' },
  { path: 'subjects/edit/:id', component: SubjectFormComponent, canActivate: [authGuard], title: 'ExamEase - Edit Subject' },
  { path: 'subjects/view/:id', component: SubjectViewComponent, canActivate: [authGuard], title: 'ExamEase - Subject Details' },
];
