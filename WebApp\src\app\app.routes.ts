import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';

import { ExamListComponent } from './ExamSchedule/exam-list/exam-list.component';
import { CreateOrUpdateExamComponent } from './ExamSchedule/create-or-update-exam/create-or-update-exam.component';
import { ExamDetailsComponent } from './ExamSchedule/exam-details/exam-details.component';
import { StudyPlanComponent } from './study-plan/study-plan.component';
import { SubjectListComponent } from './subject/subject-list.component';
import { SubjectFormComponent } from './subject/subject-form.component';
import { SubjectViewComponent } from './subject/subject-view.component';
import { authGuard } from './guards/auth.guard';
import { LandingComponent } from './landing/landing.component';

export const routes: Routes = [
  { path: '', redirectTo: '/landing', pathMatch: 'full' },
  {
    path: 'landing',
    component: LandingComponent,
    title: 'ExamEase - AI-Powered Study Planning Platform',
    data: {
      prerender: true,
      description: 'Transform your exam preparation with AI-powered study plans, intelligent scheduling, and personalized learning support.',
      keywords: 'exam preparation, study planning, AI tutoring, academic success, student tools',
      seo: {
        type: 'website',
        image: '/assets/images/landing-og.png'
      }
    }
  },
  {
    path: 'login',
    component: LoginComponent,
    title: 'Login - ExamEase',
    data: {
      prerender: true,
      description: 'Sign in to your ExamEase account to access personalized study plans and AI-powered exam preparation tools.',
      keywords: 'login, sign in, student account, exam preparation',
      seo: {
        type: 'website'
      }
    }
  },
  {
    path: 'register',
    component: RegisterComponent,
    title: 'Create Account - ExamEase',
    data: {
      prerender: true,
      description: 'Join ExamEase today and start your journey to academic success with AI-powered study planning.',
      keywords: 'register, sign up, create account, student registration',
      seo: {
        type: 'website'
      }
    }
  },
  {
    path: 'exam',
    component: ExamListComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Exams',
    data: { prerender: true, description: 'Manage your exam schedules and deadlines' }
  },
  {
    path: 'exam-details/:id',
    component: ExamDetailsComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Exam Details',
    data: { description: 'View detailed information about your exam' }
  },
  {
    path: 'create-exam',
    component: CreateOrUpdateExamComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Create Exam',
    data: { prerender: true, description: 'Create a new exam schedule' }
  },
  {
    path: 'create-exam/:id',
    component: CreateOrUpdateExamComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Edit Exam',
    data: { description: 'Edit your exam details' }
  },
  {
    path: 'studyplan',
    component: StudyPlanComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Study Plan',
    data: { prerender: true, description: 'AI-powered personalized study plans' }
  },
  {
    path: 'studyplan/:id',
    component: StudyPlanComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Study Plan Details',
    data: { description: 'View your detailed study plan' }
  },
  {
    path: 'subjects',
    component: SubjectListComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Subjects',
    data: { prerender: true, description: 'Manage your study subjects and materials' }
  },
  {
    path: 'subjects/create',
    component: SubjectFormComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Create Subject',
    data: { prerender: true, description: 'Add a new subject to your study plan' }
  },
  {
    path: 'subjects/edit/:id',
    component: SubjectFormComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Edit Subject',
    data: { description: 'Edit subject information and materials' }
  },
  {
    path: 'subjects/view/:id',
    component: SubjectViewComponent,
    canActivate: [authGuard],
    title: 'ExamEase - Subject Details',
    data: { description: 'View subject materials and AI assistant' }
  },
];
