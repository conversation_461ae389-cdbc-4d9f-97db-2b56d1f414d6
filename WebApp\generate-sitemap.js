const fs = require('fs');
const path = require('path');

// Configuration
const DOMAIN = 'https://examease.tryasp.net'; // Update with your domain
const OUTPUT_DIR = './dist/web-app/browser';
const ROUTES_FILE = './routes.txt';

// Read routes from routes.txt
function getRoutes() {
  try {
    const routesContent = fs.readFileSync(ROUTES_FILE, 'utf8');
    return routesContent
      .split('\n')
      .map(route => route.trim())
      .filter(route => route && !route.startsWith('#'));
  } catch (error) {
    console.error('Error reading routes file:', error);
    return [];
  }
}

// Generate sitemap XML
function generateSitemap(routes) {
  const currentDate = new Date().toISOString().split('T')[0];
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  routes.forEach(route => {
    const url = route === '/' ? DOMAIN : `${DOMAIN}${route}`;
    const priority = route === '/' || route === '/landing' ? '1.0' : '0.8';
    const changefreq = route === '/' || route === '/landing' ? 'daily' : 'weekly';
    
    sitemap += `
  <url>
    <loc>${url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

// Generate robots.txt
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

Sitemap: ${DOMAIN}/sitemap.xml`;
}

// Main function
function main() {
  const routes = getRoutes();
  
  if (routes.length === 0) {
    console.error('No routes found to generate sitemap');
    return;
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  // Generate and write sitemap
  const sitemap = generateSitemap(routes);
  fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap.xml'), sitemap);
  console.log('✅ Sitemap generated successfully');

  // Generate and write robots.txt
  const robotsTxt = generateRobotsTxt();
  fs.writeFileSync(path.join(OUTPUT_DIR, 'robots.txt'), robotsTxt);
  console.log('✅ Robots.txt generated successfully');

  console.log(`📊 Generated sitemap for ${routes.length} routes`);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { generateSitemap, generateRobotsTxt, getRoutes };
