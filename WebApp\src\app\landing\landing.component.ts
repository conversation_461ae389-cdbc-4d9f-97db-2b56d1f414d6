import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SEOService } from '../services/seo.service';
import { PlatformService } from '../services/platform.service';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css']
})
export class LandingComponent implements OnInit, OnDestroy {

  constructor(
    private seoService: SEOService,
    private platformService: PlatformService
  ) {}

  ngOnInit(): void {
    // Set SEO data for landing page
    this.seoService.setLandingPageSEO();

    // Add structured data for better SEO
    if (this.platformService.isBrowser) {
      this.addLandingPageStructuredData();
    }
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private addLandingPageStructuredData(): void {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "ExamEase",
      "description": "AI-powered study planning platform for exam preparation",
      "applicationCategory": "EducationalApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "AI-powered study planning",
        "Intelligent exam scheduling",
        "Personalized learning support",
        "Study material organization",
        "Progress tracking"
      ],
      "screenshot": "/assets/images/app-screenshot.png"
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    document.head.appendChild(script);
  }
}
