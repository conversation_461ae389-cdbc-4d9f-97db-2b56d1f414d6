var nh=Object.defineProperty,rh=Object.defineProperties;var oh=Object.getOwnPropertyDescriptors;var lr=Object.getOwnPropertySymbols;var Jl=Object.prototype.hasOwnProperty,Xl=Object.prototype.propertyIsEnumerable;var Yl=(t,e,n)=>e in t?nh(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,fe=(t,e)=>{for(var n in e||={})Jl.call(e,n)&&Yl(t,n,e[n]);if(lr)for(var n of lr(e))Xl.call(e,n)&&Yl(t,n,e[n]);return t},$e=(t,e)=>rh(t,oh(e));var FI=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,n)=>(typeof require<"u"?require:e)[n]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')});var eu=(t,e)=>{var n={};for(var r in t)Jl.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&lr)for(var r of lr(t))e.indexOf(r)<0&&Xl.call(t,r)&&(n[r]=t[r]);return n};var tu=(t,e,n)=>new Promise((r,o)=>{var i=l=>{try{a(n.next(l))}catch(u){o(u)}},s=l=>{try{a(n.throw(l))}catch(u){o(u)}},a=l=>l.done?r(l.value):Promise.resolve(l.value).then(i,s);a((n=n.apply(t,e)).next())});function xi(t,e){return Object.is(t,e)}var X=null,ur=!1,Ai=1,ye=Symbol("SIGNAL");function R(t){let e=X;return X=t,e}function nu(){return X}var An={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function dr(t){if(ur)throw new Error("");if(X===null)return;X.consumerOnSignalRead(t);let e=X.nextProducerIndex++;if(hr(X),e<X.producerNode.length&&X.producerNode[e]!==t&&xn(X)){let n=X.producerNode[e];pr(n,X.producerIndexOfThis[e])}X.producerNode[e]!==t&&(X.producerNode[e]=t,X.producerIndexOfThis[e]=xn(X)?iu(t,X,e):0),X.producerLastReadVersion[e]=t.version}function ih(){Ai++}function Ri(t){if(!(xn(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===Ai)){if(!t.producerMustRecompute(t)&&!Fi(t)){Ni(t);return}t.producerRecomputeValue(t),Ni(t)}}function ru(t){if(t.liveConsumerNode===void 0)return;let e=ur;ur=!0;try{for(let n of t.liveConsumerNode)n.dirty||sh(n)}finally{ur=e}}function ou(){return X?.consumerAllowSignalWrites!==!1}function sh(t){t.dirty=!0,ru(t),t.consumerMarkedDirty?.(t)}function Ni(t){t.dirty=!1,t.lastCleanEpoch=Ai}function fr(t){return t&&(t.nextProducerIndex=0),R(t)}function Oi(t,e){if(R(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(xn(t))for(let n=t.nextProducerIndex;n<t.producerNode.length;n++)pr(t.producerNode[n],t.producerIndexOfThis[n]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function Fi(t){hr(t);for(let e=0;e<t.producerNode.length;e++){let n=t.producerNode[e],r=t.producerLastReadVersion[e];if(r!==n.version||(Ri(n),r!==n.version))return!0}return!1}function ki(t){if(hr(t),xn(t))for(let e=0;e<t.producerNode.length;e++)pr(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function iu(t,e,n){if(su(t),t.liveConsumerNode.length===0&&au(t))for(let r=0;r<t.producerNode.length;r++)t.producerIndexOfThis[r]=iu(t.producerNode[r],t,r);return t.liveConsumerIndexOfThis.push(n),t.liveConsumerNode.push(e)-1}function pr(t,e){if(su(t),t.liveConsumerNode.length===1&&au(t))for(let r=0;r<t.producerNode.length;r++)pr(t.producerNode[r],t.producerIndexOfThis[r]);let n=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[n],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[n],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let r=t.liveConsumerIndexOfThis[e],o=t.liveConsumerNode[e];hr(o),o.producerIndexOfThis[r]=e}}function xn(t){return t.consumerIsAlwaysLive||(t?.liveConsumerNode?.length??0)>0}function hr(t){t.producerNode??=[],t.producerIndexOfThis??=[],t.producerLastReadVersion??=[]}function su(t){t.liveConsumerNode??=[],t.liveConsumerIndexOfThis??=[]}function au(t){return t.producerNode!==void 0}function lu(t){let e=Object.create(ah);e.computation=t;let n=()=>{if(Ri(e),dr(e),e.value===cr)throw e.error;return e.value};return n[ye]=e,n}var Ti=Symbol("UNSET"),Mi=Symbol("COMPUTING"),cr=Symbol("ERRORED"),ah=$e(fe({},An),{value:Ti,dirty:!0,error:null,equal:xi,kind:"computed",producerMustRecompute(t){return t.value===Ti||t.value===Mi},producerRecomputeValue(t){if(t.value===Mi)throw new Error("Detected cycle in computations.");let e=t.value;t.value=Mi;let n=fr(t),r,o=!1;try{r=t.computation(),R(null),o=e!==Ti&&e!==cr&&r!==cr&&t.equal(e,r)}catch(i){r=cr,t.error=i}finally{Oi(t,n)}if(o){t.value=e;return}t.value=r,t.version++}});function lh(){throw new Error}var uu=lh;function cu(){uu()}function du(t){uu=t}var uh=null;function fu(t){let e=Object.create(Pi);e.value=t;let n=()=>(dr(e),e.value);return n[ye]=e,n}function gr(t,e){ou()||cu(),t.equal(t.value,e)||(t.value=e,ch(t))}function pu(t,e){ou()||cu(),gr(t,e(t.value))}var Pi=$e(fe({},An),{equal:xi,value:void 0,kind:"signal"});function ch(t){t.version++,ih(),ru(t),uh?.()}function I(t){return typeof t=="function"}function nn(t){let n=t(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var mr=nn(t=>function(n){t(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Mt(t,e){if(t){let n=t.indexOf(e);0<=n&&t.splice(n,1)}}var K=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(I(r))try{r()}catch(i){e=i instanceof mr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{hu(i)}catch(s){e=e??[],s instanceof mr?e=[...e,...s.errors]:e.push(s)}}if(e)throw new mr(e)}}add(e){var n;if(e&&e!==this)if(this.closed)hu(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}}_hasParent(e){let{_parentage:n}=this;return n===e||Array.isArray(n)&&n.includes(e)}_addParent(e){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e}_removeParent(e){let{_parentage:n}=this;n===e?this._parentage=null:Array.isArray(n)&&Mt(n,e)}remove(e){let{_finalizers:n}=this;n&&Mt(n,e),e instanceof t&&e._removeParent(this)}};K.EMPTY=(()=>{let t=new K;return t.closed=!0,t})();var Li=K.EMPTY;function yr(t){return t instanceof K||t&&"closed"in t&&I(t.remove)&&I(t.add)&&I(t.unsubscribe)}function hu(t){I(t)?t():t.unsubscribe()}var Ae={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var rn={setTimeout(t,e,...n){let{delegate:r}=rn;return r?.setTimeout?r.setTimeout(t,e,...n):setTimeout(t,e,...n)},clearTimeout(t){let{delegate:e}=rn;return(e?.clearTimeout||clearTimeout)(t)},delegate:void 0};function vr(t){rn.setTimeout(()=>{let{onUnhandledError:e}=Ae;if(e)e(t);else throw t})}function Rn(){}var gu=Vi("C",void 0,void 0);function mu(t){return Vi("E",void 0,t)}function yu(t){return Vi("N",t,void 0)}function Vi(t,e,n){return{kind:t,value:e,error:n}}var Nt=null;function on(t){if(Ae.useDeprecatedSynchronousErrorHandling){let e=!Nt;if(e&&(Nt={errorThrown:!1,error:null}),t(),e){let{errorThrown:n,error:r}=Nt;if(Nt=null,n)throw r}}else t()}function vu(t){Ae.useDeprecatedSynchronousErrorHandling&&Nt&&(Nt.errorThrown=!0,Nt.error=t)}var xt=class extends K{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,yr(e)&&e.add(this)):this.destination=ph}static create(e,n,r){return new ct(e,n,r)}next(e){this.isStopped?Bi(yu(e),this):this._next(e)}error(e){this.isStopped?Bi(mu(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?Bi(gu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},dh=Function.prototype.bind;function ji(t,e){return dh.call(t,e)}var $i=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:n}=this;if(n.next)try{n.next(e)}catch(r){Dr(r)}}error(e){let{partialObserver:n}=this;if(n.error)try{n.error(e)}catch(r){Dr(r)}else Dr(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(n){Dr(n)}}},ct=class extends xt{constructor(e,n,r){super();let o;if(I(e)||!e)o={next:e??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ae.useDeprecatedNextContext?(i=Object.create(e),i.unsubscribe=()=>this.unsubscribe(),o={next:e.next&&ji(e.next,i),error:e.error&&ji(e.error,i),complete:e.complete&&ji(e.complete,i)}):o=e}this.destination=new $i(o)}};function Dr(t){Ae.useDeprecatedSynchronousErrorHandling?vu(t):vr(t)}function fh(t){throw t}function Bi(t,e){let{onStoppedNotification:n}=Ae;n&&rn.setTimeout(()=>n(t,e))}var ph={closed:!0,next:Rn,error:fh,complete:Rn};var sn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function se(t){return t}function hh(...t){return Hi(t)}function Hi(t){return t.length===0?se:t.length===1?t[0]:function(n){return t.reduce((r,o)=>o(r),n)}}var k=(()=>{class t{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new t;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=mh(n)?n:new ct(n,r,o);return on(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Du(r),new r((o,i)=>{let s=new ct({next:a=>{try{n(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[sn](){return this}pipe(...n){return Hi(n)(this)}toPromise(n){return n=Du(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return t.create=e=>new t(e),t})();function Du(t){var e;return(e=t??Ae.Promise)!==null&&e!==void 0?e:Promise}function gh(t){return t&&I(t.next)&&I(t.error)&&I(t.complete)}function mh(t){return t&&t instanceof xt||gh(t)&&yr(t)}function Ui(t){return I(t?.lift)}function C(t){return e=>{if(Ui(e))return e.lift(function(n){try{return t(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function b(t,e,n,r,o){return new zi(t,e,n,r,o)}var zi=class extends xt{constructor(e,n,r,o,i,s){super(e),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){e.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){e.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function qi(){return C((t,e)=>{let n=null;t._refCount++;let r=b(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){n=null;return}let o=t._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),e.unsubscribe()});t.subscribe(r),r.closed||(n=t.connect())})}var Gi=class extends k{constructor(e,n){super(),this.source=e,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ui(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e?.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new K;let n=this.getSubject();e.add(this.source.subscribe(b(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),e.closed&&(this._connection=null,e=K.EMPTY)}return e}refCount(){return qi()(this)}};var Eu=nn(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var He=(()=>{class t extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Er(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Eu}next(n){on(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){on(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){on(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Li:(this.currentObservers=null,i.push(n),new K(()=>{this.currentObservers=null,Mt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new k;return n.source=this,n}}return t.create=(e,n)=>new Er(e,n),t})(),Er=class extends He{constructor(e,n){super(),this.destination=e,this.source=n}next(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,e)}error(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,e)}complete(){var e,n;(n=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||n===void 0||n.call(e)}_subscribe(e){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(e))!==null&&r!==void 0?r:Li}};var On=class extends He{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let n=super._subscribe(e);return!n.closed&&e.next(this._value),n}getValue(){let{hasError:e,thrownError:n,_value:r}=this;if(e)throw n;return this._throwIfClosed(),r}next(e){super.next(this._value=e)}};var Wi={now(){return(Wi.delegate||Date).now()},delegate:void 0};var wr=class extends K{constructor(e,n){super()}schedule(e,n=0){return this}};var Fn={setInterval(t,e,...n){let{delegate:r}=Fn;return r?.setInterval?r.setInterval(t,e,...n):setInterval(t,e,...n)},clearInterval(t){let{delegate:e}=Fn;return(e?.clearInterval||clearInterval)(t)},delegate:void 0};var _r=class extends wr{constructor(e,n){super(e,n),this.scheduler=e,this.work=n,this.pending=!1}schedule(e,n=0){var r;if(this.closed)return this;this.state=e;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(e,n,r=0){return Fn.setInterval(e.flush.bind(e,this),r)}recycleAsyncId(e,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Fn.clearInterval(n)}execute(e,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(e,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,n){let r=!1,o;try{this.work(e)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:e,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Mt(r,this),e!=null&&(this.id=this.recycleAsyncId(n,e,null)),this.delay=null,super.unsubscribe()}}};var an=class t{constructor(e,n=t.now){this.schedulerActionCtor=e,this.now=n}schedule(e,n=0,r){return new this.schedulerActionCtor(this,e).schedule(r,n)}};an.now=Wi.now;var Ir=class extends an{constructor(e,n=an.now){super(e,n),this.actions=[],this._active=!1}flush(e){let{actions:n}=this;if(this._active){n.push(e);return}let r;this._active=!0;do if(r=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,r){for(;e=n.shift();)e.unsubscribe();throw r}}};var kn=new Ir(_r),wu=kn;var At=new k(t=>t.complete());function br(t){return t&&I(t.schedule)}function Qi(t){return t[t.length-1]}function Cr(t){return I(Qi(t))?t.pop():void 0}function Ue(t){return br(Qi(t))?t.pop():void 0}function _u(t,e){return typeof Qi(t)=="number"?t.pop():e}function bu(t,e,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(c){try{u(r.next(c))}catch(d){s(d)}}function l(c){try{u(r.throw(c))}catch(d){s(d)}}function u(c){c.done?i(c.value):o(c.value).then(a,l)}u((r=r.apply(t,e||[])).next())})}function Iu(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Rt(t){return this instanceof Rt?(this.v=t,this):new Rt(t)}function Cu(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(t,e||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(p){return Promise.resolve(p).then(f,d)}}function a(f,p){r[f]&&(o[f]=function(g){return new Promise(function(E,w){i.push([f,g,E,w])>1||l(f,g)})},p&&(o[f]=p(o[f])))}function l(f,p){try{u(r[f](p))}catch(g){h(i[0][3],g)}}function u(f){f.value instanceof Rt?Promise.resolve(f.value.v).then(c,d):h(i[0][2],f)}function c(f){l("next",f)}function d(f){l("throw",f)}function h(f,p){f(p),i.shift(),i.length&&l(i[0][0],i[0][1])}}function Su(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Iu=="function"?Iu(t):t[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=t[i]&&function(s){return new Promise(function(a,l){s=t[i](s),o(a,l,s.done,s.value)})}}function o(i,s,a,l){Promise.resolve(l).then(function(u){i({value:u,done:a})},s)}}var Sr=t=>t&&typeof t.length=="number"&&typeof t!="function";function Tr(t){return I(t?.then)}function Mr(t){return I(t[sn])}function Nr(t){return Symbol.asyncIterator&&I(t?.[Symbol.asyncIterator])}function xr(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function yh(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ar=yh();function Rr(t){return I(t?.[Ar])}function Or(t){return Cu(this,arguments,function*(){let n=t.getReader();try{for(;;){let{value:r,done:o}=yield Rt(n.read());if(o)return yield Rt(void 0);yield yield Rt(r)}}finally{n.releaseLock()}})}function Fr(t){return I(t?.getReader)}function U(t){if(t instanceof k)return t;if(t!=null){if(Mr(t))return vh(t);if(Sr(t))return Dh(t);if(Tr(t))return Eh(t);if(Nr(t))return Tu(t);if(Rr(t))return wh(t);if(Fr(t))return _h(t)}throw xr(t)}function vh(t){return new k(e=>{let n=t[sn]();if(I(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Dh(t){return new k(e=>{for(let n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function Eh(t){return new k(e=>{t.then(n=>{e.closed||(e.next(n),e.complete())},n=>e.error(n)).then(null,vr)})}function wh(t){return new k(e=>{for(let n of t)if(e.next(n),e.closed)return;e.complete()})}function Tu(t){return new k(e=>{Ih(t,e).catch(n=>e.error(n))})}function _h(t){return Tu(Or(t))}function Ih(t,e){var n,r,o,i;return bu(this,void 0,void 0,function*(){try{for(n=Su(t);r=yield n.next(),!r.done;){let s=r.value;if(e.next(s),e.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}e.complete()})}function pe(t,e,n,r=0,o=!1){let i=e.schedule(function(){n(),o?t.add(this.schedule(null,r)):this.unsubscribe()},r);if(t.add(i),!o)return i}function kr(t,e=0){return C((n,r)=>{n.subscribe(b(r,o=>pe(r,t,()=>r.next(o),e),()=>pe(r,t,()=>r.complete(),e),o=>pe(r,t,()=>r.error(o),e)))})}function Pr(t,e=0){return C((n,r)=>{r.add(t.schedule(()=>n.subscribe(r),e))})}function Mu(t,e){return U(t).pipe(Pr(e),kr(e))}function Nu(t,e){return U(t).pipe(Pr(e),kr(e))}function xu(t,e){return new k(n=>{let r=0;return e.schedule(function(){r===t.length?n.complete():(n.next(t[r++]),n.closed||this.schedule())})})}function Au(t,e){return new k(n=>{let r;return pe(n,e,()=>{r=t[Ar](),pe(n,e,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>I(r?.return)&&r.return()})}function Lr(t,e){if(!t)throw new Error("Iterable cannot be null");return new k(n=>{pe(n,e,()=>{let r=t[Symbol.asyncIterator]();pe(n,e,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Ru(t,e){return Lr(Or(t),e)}function Ou(t,e){if(t!=null){if(Mr(t))return Mu(t,e);if(Sr(t))return xu(t,e);if(Tr(t))return Nu(t,e);if(Nr(t))return Lr(t,e);if(Rr(t))return Au(t,e);if(Fr(t))return Ru(t,e)}throw xr(t)}function ze(t,e){return e?Ou(t,e):U(t)}function bh(...t){let e=Ue(t);return ze(t,e)}function Ch(t,e){let n=I(t)?t:()=>t,r=o=>o.error(n());return new k(e?o=>e.schedule(r,0,o):r)}function Sh(t){return!!t&&(t instanceof k||I(t.lift)&&I(t.subscribe))}var Je=nn(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function Th(t,e){let n=typeof e=="object";return new Promise((r,o)=>{let i=new ct({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(e.defaultValue):o(new Je)}});t.subscribe(i)})}function Fu(t){return t instanceof Date&&!isNaN(t)}function Ot(t,e){return C((n,r)=>{let o=0;n.subscribe(b(r,i=>{r.next(t.call(e,i,o++))}))})}var{isArray:Mh}=Array;function Nh(t,e){return Mh(e)?t(...e):t(e)}function Vr(t){return Ot(e=>Nh(t,e))}var{isArray:xh}=Array,{getPrototypeOf:Ah,prototype:Rh,keys:Oh}=Object;function jr(t){if(t.length===1){let e=t[0];if(xh(e))return{args:e,keys:null};if(Fh(e)){let n=Oh(e);return{args:n.map(r=>e[r]),keys:n}}}return{args:t,keys:null}}function Fh(t){return t&&typeof t=="object"&&Ah(t)===Rh}function Br(t,e){return t.reduce((n,r,o)=>(n[r]=e[o],n),{})}function kh(...t){let e=Ue(t),n=Cr(t),{args:r,keys:o}=jr(t);if(r.length===0)return ze([],e);let i=new k(Ph(r,e,o?s=>Br(o,s):se));return n?i.pipe(Vr(n)):i}function Ph(t,e,n=se){return r=>{ku(e,()=>{let{length:o}=t,i=new Array(o),s=o,a=o;for(let l=0;l<o;l++)ku(e,()=>{let u=ze(t[l],e),c=!1;u.subscribe(b(r,d=>{i[l]=d,c||(c=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ku(t,e,n){t?pe(n,t,e):e()}function Pu(t,e,n,r,o,i,s,a){let l=[],u=0,c=0,d=!1,h=()=>{d&&!l.length&&!u&&e.complete()},f=g=>u<r?p(g):l.push(g),p=g=>{i&&e.next(g),u++;let E=!1;U(n(g,c++)).subscribe(b(e,w=>{o?.(w),i?f(w):e.next(w)},()=>{E=!0},void 0,()=>{if(E)try{for(u--;l.length&&u<r;){let w=l.shift();s?pe(e,s,()=>p(w)):p(w)}h()}catch(w){e.error(w)}}))};return t.subscribe(b(e,f,()=>{d=!0,h()})),()=>{a?.()}}function Ft(t,e,n=1/0){return I(e)?Ft((r,o)=>Ot((i,s)=>e(r,i,o,s))(U(t(r,o))),n):(typeof e=="number"&&(n=e),C((r,o)=>Pu(r,o,t,n)))}function Pn(t=1/0){return Ft(se,t)}function Lu(){return Pn(1)}function $r(...t){return Lu()(ze(t,Ue(t)))}function Lh(t){return new k(e=>{U(t()).subscribe(e)})}function Vh(...t){let e=Cr(t),{args:n,keys:r}=jr(t),o=new k(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),l=s,u=s;for(let c=0;c<s;c++){let d=!1;U(n[c]).subscribe(b(i,h=>{d||(d=!0,u--),a[c]=h},()=>l--,void 0,()=>{(!l||!d)&&(u||i.next(r?Br(r,a):a),i.complete())}))}});return e?o.pipe(Vr(e)):o}function Vu(t=0,e,n=wu){let r=-1;return e!=null&&(br(e)?n=e:r=e),new k(o=>{let i=Fu(t)?+t-n.now():t;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function jh(...t){let e=Ue(t),n=_u(t,1/0),r=t;return r.length?r.length===1?U(r[0]):Pn(n)(ze(r,e)):At}function kt(t,e){return C((n,r)=>{let o=0;n.subscribe(b(r,i=>t.call(e,i,o++)&&r.next(i)))})}function ju(t){return C((e,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},l=()=>{i=null,s&&n.complete()};e.subscribe(b(n,u=>{r=!0,o=u,i||U(t(u)).subscribe(i=b(n,a,l))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Bh(t,e=kn){return ju(()=>Vu(t,e))}function Bu(t){return C((e,n)=>{let r=null,o=!1,i;r=e.subscribe(b(n,void 0,void 0,s=>{i=U(t(s,Bu(t)(e))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function $u(t,e,n,r,o){return(i,s)=>{let a=n,l=e,u=0;i.subscribe(b(s,c=>{let d=u++;l=a?t(l,c,d):(a=!0,c),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}function $h(t,e){return I(e)?Ft(t,e,1):Ft(t,1)}function Hh(t,e=kn){return C((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function l(){let u=s+t,c=e.now();if(c<u){o=this.schedule(void 0,u-c),r.add(o);return}a()}n.subscribe(b(r,u=>{i=u,s=e.now(),o||(o=e.schedule(l,t),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Ln(t){return C((e,n)=>{let r=!1;e.subscribe(b(n,o=>{r=!0,n.next(o)},()=>{r||n.next(t),n.complete()}))})}function Zi(t){return t<=0?()=>At:C((e,n)=>{let r=0;e.subscribe(b(n,o=>{++r<=t&&(n.next(o),t<=r&&n.complete())}))})}function Uh(t,e=se){return t=t??zh,C((n,r)=>{let o,i=!0;n.subscribe(b(r,s=>{let a=e(s);(i||!t(o,a))&&(i=!1,o=a,r.next(s))}))})}function zh(t,e){return t===e}function Hr(t=qh){return C((e,n)=>{let r=!1;e.subscribe(b(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(t())))})}function qh(){return new Je}function Gh(t){return C((e,n)=>{try{e.subscribe(n)}finally{n.add(t)}})}function Wh(t,e){let n=arguments.length>=2;return r=>r.pipe(t?kt((o,i)=>t(o,i,r)):se,Zi(1),n?Ln(e):Hr(()=>new Je))}function Ki(t){return t<=0?()=>At:C((e,n)=>{let r=[];e.subscribe(b(n,o=>{r.push(o),t<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Qh(t,e){let n=arguments.length>=2;return r=>r.pipe(t?kt((o,i)=>t(o,i,r)):se,Ki(1),n?Ln(e):Hr(()=>new Je))}function Zh(t,e){return C($u(t,e,arguments.length>=2,!0))}function Kh(t){return kt((e,n)=>t<=n)}function Yh(...t){let e=Ue(t);return C((n,r)=>{(e?$r(t,n,e):$r(t,n)).subscribe(r)})}function Jh(t,e){return C((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(b(r,l=>{o?.unsubscribe();let u=0,c=i++;U(t(l,c)).subscribe(o=b(r,d=>r.next(e?e(l,d,c,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Xh(t){return C((e,n)=>{U(t).subscribe(b(n,()=>n.complete(),Rn)),!n.closed&&e.subscribe(n)})}function eg(t,e,n){let r=I(t)||e||n?{next:t,error:e,complete:n}:t;return r?C((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(b(i,l=>{var u;(u=r.next)===null||u===void 0||u.call(r,l),i.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),i.complete()},l=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,l),i.error(l)},()=>{var l,u;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):se}var Ac="https://g.co/ng/security#xss",m=class extends Error{code;constructor(e,n){super(Rc(e,n)),this.code=e}};function Rc(t,e){return`${`NG0${Math.abs(t)}`}${e?": "+e:""}`}var Oc=Symbol("InputSignalNode#UNSET"),tg=$e(fe({},Pi),{transformFn:void 0,applyValueToInputSignal(t,e){gr(t,e)}});function Fc(t,e){let n=Object.create(tg);n.value=t,n.transformFn=e?.transform;function r(){if(dr(n),n.value===Oc)throw new m(-950,!1);return n.value}return r[ye]=n,r}function Wn(t){return{toString:t}.toString()}var Ur="__parameters__";function ng(t){return function(...n){if(t){let r=t(...n);for(let o in r)this[o]=r[o]}}}function kc(t,e,n){return Wn(()=>{let r=ng(e);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(l,u,c){let d=l.hasOwnProperty(Ur)?l[Ur]:Object.defineProperty(l,Ur,{value:[]})[Ur];for(;d.length<=c;)d.push(null);return(d[c]=d[c]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=t,o.annotationCls=o,o})}var ft=globalThis;function j(t){for(let e in t)if(t[e]===j)return e;throw Error("Could not find renamed property on target object.")}function rg(t,e){for(let n in e)e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n])}function le(t){if(typeof t=="string")return t;if(Array.isArray(t))return"["+t.map(le).join(", ")+"]";if(t==null)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;let e=t.toString();if(e==null)return""+e;let n=e.indexOf(`
`);return n===-1?e:e.substring(0,n)}function cs(t,e){return t==null||t===""?e===null?"":e:e==null||e===""?t:t+" "+e}var og=j({__forward_ref__:j});function Pc(t){return t.__forward_ref__=Pc,t.toString=function(){return le(this())},t}function ae(t){return Lc(t)?t():t}function Lc(t){return typeof t=="function"&&t.hasOwnProperty(og)&&t.__forward_ref__===Pc}function V(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function ma(t){return{providers:t.providers||[],imports:t.imports||[]}}function No(t){return Hu(t,Vc)||Hu(t,jc)}function _N(t){return No(t)!==null}function Hu(t,e){return t.hasOwnProperty(e)?t[e]:null}function ig(t){let e=t&&(t[Vc]||t[jc]);return e||null}function Uu(t){return t&&(t.hasOwnProperty(zu)||t.hasOwnProperty(sg))?t[zu]:null}var Vc=j({\u0275prov:j}),zu=j({\u0275inj:j}),jc=j({ngInjectableDef:j}),sg=j({ngInjectorDef:j}),O=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(e,n){this._desc=e,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=V({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Bc(t){return t&&!!t.\u0275providers}var ag=j({\u0275cmp:j}),lg=j({\u0275dir:j}),ug=j({\u0275pipe:j}),cg=j({\u0275mod:j}),to=j({\u0275fac:j}),$n=j({__NG_ELEMENT_ID__:j}),qu=j({__NG_ENV_ID__:j});function xo(t){return typeof t=="string"?t:t==null?"":String(t)}function dg(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():xo(t)}function fg(t,e){let n=e?`. Dependency path: ${e.join(" > ")} > ${t}`:"";throw new m(-200,t)}function ya(t,e){throw new m(-201,!1)}var x=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(x||{}),ds;function $c(){return ds}function he(t){let e=ds;return ds=t,e}function Hc(t,e,n){let r=No(t);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&x.Optional)return null;if(e!==void 0)return e;ya(t,"Injector")}var pg={},Hn=pg,fs="__NG_DI_FLAG__",no="ngTempTokenPath",hg="ngTokenPath",gg=/\n/gm,mg="\u0275",Gu="__source",fn;function yg(){return fn}function dt(t){let e=fn;return fn=t,e}function vg(t,e=x.Default){if(fn===void 0)throw new m(-203,!1);return fn===null?Hc(t,void 0,e):fn.get(t,e&x.Optional?null:void 0,e)}function Y(t,e=x.Default){return($c()||vg)(ae(t),e)}function S(t,e=x.Default){return Y(t,Ao(e))}function Ao(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function ps(t){let e=[];for(let n=0;n<t.length;n++){let r=ae(t[n]);if(Array.isArray(r)){if(r.length===0)throw new m(900,!1);let o,i=x.Default;for(let s=0;s<r.length;s++){let a=r[s],l=Dg(a);typeof l=="number"?l===-1?o=a.token:i|=l:o=a}e.push(Y(o,i))}else e.push(Y(r))}return e}function Uc(t,e){return t[fs]=e,t.prototype[fs]=e,t}function Dg(t){return t[fs]}function Eg(t,e,n,r){let o=t[no];throw e[Gu]&&o.unshift(e[Gu]),t.message=wg(`
`+t.message,o,n,r),t[hg]=o,t[no]=null,t}function wg(t,e,n,r=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==mg?t.slice(2):t;let o=le(e);if(Array.isArray(e))o=e.map(le).join(" -> ");else if(typeof e=="object"){let i=[];for(let s in e)if(e.hasOwnProperty(s)){let a=e[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):le(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${t.replace(gg,`
  `)}`}var _g=Uc(kc("Optional"),8);var Ig=Uc(kc("SkipSelf"),4);function Vt(t,e){let n=t.hasOwnProperty(to);return n?t[to]:null}function bg(t,e,n){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++){let o=t[r],i=e[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Cg(t){return t.flat(Number.POSITIVE_INFINITY)}function va(t,e){t.forEach(n=>Array.isArray(n)?va(n,e):e(n))}function zc(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function ro(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function Sg(t,e,n,r){let o=t.length;if(o==e)t.push(n,r);else if(o===1)t.push(r,t[0]),t[0]=n;else{for(o--,t.push(t[o-1],t[o]);o>e;){let i=o-2;t[o]=t[i],o--}t[e]=n,t[e+1]=r}}function Da(t,e,n){let r=Qn(t,e);return r>=0?t[r|1]=n:(r=~r,Sg(t,r,e,n)),r}function Yi(t,e){let n=Qn(t,e);if(n>=0)return t[n|1]}function Qn(t,e){return Tg(t,e,1)}function Tg(t,e,n){let r=0,o=t.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=t[i<<n];if(e===s)return i<<n;s>e?o=i:r=i+1}return~(o<<n)}var hn={},ge=[],oo=new O(""),qc=new O("",-1),Gc=new O(""),io=class{get(e,n=Hn){if(n===Hn){let r=new Error(`NullInjectorError: No provider for ${le(e)}!`);throw r.name="NullInjectorError",r}return n}};function Wc(t,e){let n=t[cg]||null;if(!n&&e===!0)throw new Error(`Type ${le(t)} does not have '\u0275mod' property.`);return n}function Xe(t){return t[ag]||null}function Qc(t){return t[lg]||null}function Zc(t){return t[ug]||null}function Mg(t){let e=Xe(t)||Qc(t)||Zc(t);return e!==null&&e.standalone}function Ng(t){return{\u0275providers:t}}function xg(...t){return{\u0275providers:Kc(!0,t),\u0275fromNgModule:!0}}function Kc(t,...e){let n=[],r=new Set,o,i=s=>{n.push(s)};return va(e,s=>{let a=s;hs(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Yc(o,i),n}function Yc(t,e){for(let n=0;n<t.length;n++){let{ngModule:r,providers:o}=t[n];Ea(o,i=>{e(i,r)})}}function hs(t,e,n,r){if(t=ae(t),!t)return!1;let o=null,i=Uu(t),s=!i&&Xe(t);if(!i&&!s){let l=t.ngModule;if(i=Uu(l),i)o=l;else return!1}else{if(s&&!s.standalone)return!1;o=t}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of l)hs(u,e,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{va(i.imports,c=>{hs(c,e,n,r)&&(u||=[],u.push(c))})}finally{}u!==void 0&&Yc(u,e)}if(!a){let u=Vt(o)||(()=>new o);e({provide:o,useFactory:u,deps:ge},o),e({provide:Gc,useValue:o,multi:!0},o),e({provide:oo,useValue:()=>Y(o),multi:!0},o)}let l=i.providers;if(l!=null&&!a){let u=t;Ea(l,c=>{e(c,u)})}}else return!1;return o!==t&&t.providers!==void 0}function Ea(t,e){for(let n of t)Bc(n)&&(n=n.\u0275providers),Array.isArray(n)?Ea(n,e):e(n)}var Ag=j({provide:String,useValue:j});function Jc(t){return t!==null&&typeof t=="object"&&Ag in t}function Rg(t){return!!(t&&t.useExisting)}function Og(t){return!!(t&&t.useFactory)}function gn(t){return typeof t=="function"}function Fg(t){return!!t.useClass}var Xc=new O(""),Zr={},kg={},Ji;function Ro(){return Ji===void 0&&(Ji=new io),Ji}var mt=class{},Un=class extends mt{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(e,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,ms(e,s=>this.processProvider(s)),this.records.set(qc,ln(void 0,this)),o.has("environment")&&this.records.set(mt,ln(void 0,this));let i=this.records.get(Xc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Gc,ge,x.Self))}destroy(){jn(this),this._destroyed=!0;let e=R(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),R(e)}}onDestroy(e){return jn(this),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){jn(this);let n=dt(this),r=he(void 0),o;try{return e()}finally{dt(n),he(r)}}get(e,n=Hn,r=x.Default){if(jn(this),e.hasOwnProperty(qu))return e[qu](this);r=Ao(r);let o,i=dt(this),s=he(void 0);try{if(!(r&x.SkipSelf)){let l=this.records.get(e);if(l===void 0){let u=Bg(e)&&No(e);u&&this.injectableDefInScope(u)?l=ln(gs(e),Zr):l=null,this.records.set(e,l)}if(l!=null)return this.hydrate(e,l)}let a=r&x.Self?Ro():this.parent;return n=r&x.Optional&&n===Hn?null:n,a.get(e,n)}catch(a){if(a.name==="NullInjectorError"){if((a[no]=a[no]||[]).unshift(le(e)),i)throw a;return Eg(a,e,"R3InjectorError",this.source)}else throw a}finally{he(s),dt(i)}}resolveInjectorInitializers(){let e=R(null),n=dt(this),r=he(void 0),o;try{let i=this.get(oo,ge,x.Self);for(let s of i)s()}finally{dt(n),he(r),R(e)}}toString(){let e=[],n=this.records;for(let r of n.keys())e.push(le(r));return`R3Injector[${e.join(", ")}]`}processProvider(e){e=ae(e);let n=gn(e)?e:ae(e&&e.provide),r=Lg(e);if(!gn(e)&&e.multi===!0){let o=this.records.get(n);o||(o=ln(void 0,Zr,!0),o.factory=()=>ps(o.multi),this.records.set(n,o)),n=e,o.multi.push(e)}this.records.set(n,r)}hydrate(e,n){let r=R(null);try{return n.value===Zr&&(n.value=kg,n.value=n.factory()),typeof n.value=="object"&&n.value&&jg(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{R(r)}}injectableDefInScope(e){if(!e.providedIn)return!1;let n=ae(e.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(e){let n=this._onDestroyHooks.indexOf(e);n!==-1&&this._onDestroyHooks.splice(n,1)}};function gs(t){let e=No(t),n=e!==null?e.factory:Vt(t);if(n!==null)return n;if(t instanceof O)throw new m(204,!1);if(t instanceof Function)return Pg(t);throw new m(204,!1)}function Pg(t){if(t.length>0)throw new m(204,!1);let n=ig(t);return n!==null?()=>n.factory(t):()=>new t}function Lg(t){if(Jc(t))return ln(void 0,t.useValue);{let e=ed(t);return ln(e,Zr)}}function ed(t,e,n){let r;if(gn(t)){let o=ae(t);return Vt(o)||gs(o)}else if(Jc(t))r=()=>ae(t.useValue);else if(Og(t))r=()=>t.useFactory(...ps(t.deps||[]));else if(Rg(t))r=()=>Y(ae(t.useExisting));else{let o=ae(t&&(t.useClass||t.provide));if(Vg(t))r=()=>new o(...ps(t.deps));else return Vt(o)||gs(o)}return r}function jn(t){if(t.destroyed)throw new m(205,!1)}function ln(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function Vg(t){return!!t.deps}function jg(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function Bg(t){return typeof t=="function"||typeof t=="object"&&t instanceof O}function ms(t,e){for(let n of t)Array.isArray(n)?ms(n,e):n&&Bc(n)?ms(n.\u0275providers,e):e(n)}function td(t,e){t instanceof Un&&jn(t);let n,r=dt(t),o=he(void 0);try{return e()}finally{dt(r),he(o)}}function nd(){return $c()!==void 0||yg()!=null}function rd(t){if(!nd())throw new m(-203,!1)}function $g(t){return typeof t=="function"}var rt=0,T=1,_=2,ie=3,Fe=4,Pe=5,so=6,ao=7,ke=8,mn=9,et=10,W=11,zn=12,Wu=13,Cn=14,Ge=15,jt=16,un=17,tt=18,Oo=19,od=20,ht=21,Xi=22,lo=23,ve=24,De=25,id=1;var Bt=7,uo=8,yn=9,Ee=10;function gt(t){return Array.isArray(t)&&typeof t[id]=="object"}function ot(t){return Array.isArray(t)&&t[id]===!0}function wa(t){return(t.flags&4)!==0}function Fo(t){return t.componentOffset>-1}function ko(t){return(t.flags&1)===1}function yt(t){return!!t.template}function ys(t){return(t[_]&512)!==0}function Zn(t){return(t[_]&256)===256}var vs=class{previousValue;currentValue;firstChange;constructor(e,n,r){this.previousValue=e,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function sd(t,e,n,r){e!==null?e.applyValueToInputSignal(e,r):t[n]=r}var Hg=(()=>{let t=()=>ad;return t.ngInherit=!0,t})();function ad(t){return t.type.prototype.ngOnChanges&&(t.setInput=zg),Ug}function Ug(){let t=ud(this),e=t?.current;if(e){let n=t.previous;if(n===hn)t.previous=e;else for(let r in e)n[r]=e[r];t.current=null,this.ngOnChanges(e)}}function zg(t,e,n,r,o){let i=this.declaredInputs[r],s=ud(t)||qg(t,{previous:hn,current:null}),a=s.current||(s.current={}),l=s.previous,u=l[i];a[i]=new vs(u&&u.currentValue,n,l===hn),sd(t,e,o,n)}var ld="__ngSimpleChanges__";function ud(t){return t[ld]||null}function qg(t,e){return t[ld]=e}var Qu=null;var Ce=function(t,e,n){Qu?.(t,e,n)},cd="svg",Gg="math";function We(t){for(;Array.isArray(t);)t=t[rt];return t}function dd(t,e){return We(e[t])}function Se(t,e){return We(e[t.index])}function fd(t,e){return t.data[e]}function pd(t,e){return t[e]}function _t(t,e){let n=e[t];return gt(n)?n:n[rt]}function Wg(t){return(t[_]&4)===4}function _a(t){return(t[_]&128)===128}function Qg(t){return ot(t[ie])}function vn(t,e){return e==null?null:t[e]}function hd(t){t[un]=0}function Ia(t){t[_]&1024||(t[_]|=1024,_a(t)&&Lo(t))}function Zg(t,e){for(;t>0;)e=e[Cn],t--;return e}function Po(t){return!!(t[_]&9216||t[ve]?.dirty)}function Ds(t){t[et].changeDetectionScheduler?.notify(9),t[_]&64&&(t[_]|=1024),Po(t)&&Lo(t)}function Lo(t){t[et].changeDetectionScheduler?.notify(0);let e=$t(t);for(;e!==null&&!(e[_]&8192||(e[_]|=8192,!_a(e)));)e=$t(e)}function gd(t,e){if(Zn(t))throw new m(911,!1);t[ht]===null&&(t[ht]=[]),t[ht].push(e)}function Kg(t,e){if(t[ht]===null)return;let n=t[ht].indexOf(e);n!==-1&&t[ht].splice(n,1)}function $t(t){let e=t[ie];return ot(e)?e[ie]:e}function md(t){return t[ao]??=[]}function yd(t){return t.cleanup??=[]}function Yg(t,e,n,r){let o=md(e);o.push(n),t.firstCreatePass&&yd(t).push(r,o.length-1)}var M={lFrame:Sd(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Es=!1;function Jg(){return M.lFrame.elementDepthCount}function Xg(){M.lFrame.elementDepthCount++}function em(){M.lFrame.elementDepthCount--}function vd(){return M.bindingsEnabled}function tm(){return M.skipHydrationRootTNode!==null}function nm(t){return M.skipHydrationRootTNode===t}function rm(){M.skipHydrationRootTNode=null}function A(){return M.lFrame.lView}function Q(){return M.lFrame.tView}function IN(t){return M.lFrame.contextLView=t,t[ke]}function bN(t){return M.lFrame.contextLView=null,t}function ne(){let t=Dd();for(;t!==null&&t.type===64;)t=t.parent;return t}function Dd(){return M.lFrame.currentTNode}function om(){let t=M.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function Zt(t,e){let n=M.lFrame;n.currentTNode=t,n.isParent=e}function ba(){return M.lFrame.isParent}function Ed(){M.lFrame.isParent=!1}function im(){return M.lFrame.contextLView}function wd(){return Es}function Zu(t){let e=Es;return Es=t,e}function Ca(){let t=M.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function sm(t){return M.lFrame.bindingIndex=t}function Kn(){return M.lFrame.bindingIndex++}function _d(t){let e=M.lFrame,n=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,n}function am(){return M.lFrame.inI18n}function lm(t,e){let n=M.lFrame;n.bindingIndex=n.bindingRootIndex=t,ws(e)}function um(){return M.lFrame.currentDirectiveIndex}function ws(t){M.lFrame.currentDirectiveIndex=t}function cm(t){let e=M.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function Id(){return M.lFrame.currentQueryIndex}function Sa(t){M.lFrame.currentQueryIndex=t}function dm(t){let e=t[T];return e.type===2?e.declTNode:e.type===1?t[Pe]:null}function bd(t,e,n){if(n&x.SkipSelf){let o=e,i=t;for(;o=o.parent,o===null&&!(n&x.Host);)if(o=dm(i),o===null||(i=i[Cn],o.type&10))break;if(o===null)return!1;e=o,t=i}let r=M.lFrame=Cd();return r.currentTNode=e,r.lView=t,!0}function Ta(t){let e=Cd(),n=t[T];M.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function Cd(){let t=M.lFrame,e=t===null?null:t.child;return e===null?Sd(t):e}function Sd(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function Td(){let t=M.lFrame;return M.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var Md=Td;function Ma(){let t=Td();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function fm(t){return(M.lFrame.contextLView=Zg(t,M.lFrame.contextLView))[ke]}function Kt(){return M.lFrame.selectedIndex}function Ht(t){M.lFrame.selectedIndex=t}function Vo(){let t=M.lFrame;return fd(t.tView,t.selectedIndex)}function CN(){M.lFrame.currentNamespace=cd}function pm(){return M.lFrame.currentNamespace}var Nd=!0;function jo(){return Nd}function Bo(t){Nd=t}function hm(t,e,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=e.type.prototype;if(r){let s=ad(e);(n.preOrderHooks??=[]).push(t,s),(n.preOrderCheckHooks??=[]).push(t,s)}o&&(n.preOrderHooks??=[]).push(0-t,o),i&&((n.preOrderHooks??=[]).push(t,i),(n.preOrderCheckHooks??=[]).push(t,i))}function $o(t,e){for(let n=e.directiveStart,r=e.directiveEnd;n<r;n++){let i=t.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:u,ngOnDestroy:c}=i;s&&(t.contentHooks??=[]).push(-n,s),a&&((t.contentHooks??=[]).push(n,a),(t.contentCheckHooks??=[]).push(n,a)),l&&(t.viewHooks??=[]).push(-n,l),u&&((t.viewHooks??=[]).push(n,u),(t.viewCheckHooks??=[]).push(n,u)),c!=null&&(t.destroyHooks??=[]).push(n,c)}}function Kr(t,e,n){xd(t,e,3,n)}function Yr(t,e,n,r){(t[_]&3)===n&&xd(t,e,n,r)}function es(t,e){let n=t[_];(n&3)===e&&(n&=16383,n+=1,t[_]=n)}function xd(t,e,n,r){let o=r!==void 0?t[un]&65535:0,i=r??-1,s=e.length-1,a=0;for(let l=o;l<s;l++)if(typeof e[l+1]=="number"){if(a=e[l],r!=null&&a>=r)break}else e[l]<0&&(t[un]+=65536),(a<i||i==-1)&&(gm(t,n,e,l),t[un]=(t[un]&**********)+l+2),l++}function Ku(t,e){Ce(4,t,e);let n=R(null);try{e.call(t)}finally{R(n),Ce(5,t,e)}}function gm(t,e,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=t[s];o?t[_]>>14<t[un]>>16&&(t[_]&3)===e&&(t[_]+=16384,Ku(a,i)):Ku(a,i)}var pn=-1,Ut=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(e,n,r){this.factory=e,this.canSeeViewProviders=n,this.injectImpl=r}};function mm(t){return t instanceof Ut}function ym(t){return(t.flags&8)!==0}function vm(t){return(t.flags&16)!==0}function Dm(t,e,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];t.setAttribute(e,s,a,i)}else{let i=o,s=n[++r];Em(i)?t.setProperty(e,i,s):t.setAttribute(e,i,s),r++}}return r}function Ad(t){return t===3||t===4||t===6}function Em(t){return t.charCodeAt(0)===64}function Dn(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let n=-1;for(let r=0;r<e.length;r++){let o=e[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Yu(t,n,o,null,e[++r]):Yu(t,n,o,null,null))}}return t}function Yu(t,e,n,r,o){let i=0,s=t.length;if(e===-1)s=-1;else for(;i<t.length;){let a=t[i++];if(typeof a=="number"){if(a===e){s=-1;break}else if(a>e){s=i-1;break}}}for(;i<t.length;){let a=t[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(t[i+1]=o);return}else if(r===t[i+1]){t[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(t.splice(s,0,e),i=s+1),t.splice(i++,0,n),r!==null&&t.splice(i++,0,r),o!==null&&t.splice(i++,0,o)}var ts={},_s=class{injector;parentInjector;constructor(e,n){this.injector=e,this.parentInjector=n}get(e,n,r){r=Ao(r);let o=this.injector.get(e,ts,r);return o!==ts||n===ts?o:this.parentInjector.get(e,n,r)}};function Rd(t){return t!==pn}function co(t){return t&32767}function wm(t){return t>>16}function fo(t,e){let n=wm(t),r=e;for(;n>0;)r=r[Cn],n--;return r}var Is=!0;function po(t){let e=Is;return Is=t,e}var _m=256,Od=_m-1,Fd=5,Im=0,qe={};function bm(t,e,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty($n)&&(r=n[$n]),r==null&&(r=n[$n]=Im++);let o=r&Od,i=1<<o;e.data[t+(o>>Fd)]|=i}function ho(t,e){let n=kd(t,e);if(n!==-1)return n;let r=e[T];r.firstCreatePass&&(t.injectorIndex=e.length,ns(r.data,t),ns(e,null),ns(r.blueprint,null));let o=Na(t,e),i=t.injectorIndex;if(Rd(o)){let s=co(o),a=fo(o,e),l=a[T].data;for(let u=0;u<8;u++)e[i+u]=a[s+u]|l[s+u]}return e[i+8]=o,i}function ns(t,e){t.push(0,0,0,0,0,0,0,0,e)}function kd(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function Na(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let n=0,r=null,o=e;for(;o!==null;){if(r=Bd(o),r===null)return pn;if(n++,o=o[Cn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return pn}function bs(t,e,n){bm(t,e,n)}function Cm(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let n=t.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Ad(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===e)return n[o+1];o=o+2}}}return null}function Pd(t,e,n){if(n&x.Optional||t!==void 0)return t;ya(e,"NodeInjector")}function Ld(t,e,n,r){if(n&x.Optional&&r===void 0&&(r=null),!(n&(x.Self|x.Host))){let o=t[mn],i=he(void 0);try{return o?o.get(e,r,n&x.Optional):Hc(e,r,n&x.Optional)}finally{he(i)}}return Pd(r,e,n)}function Vd(t,e,n,r=x.Default,o){if(t!==null){if(e[_]&2048&&!(r&x.Self)){let s=Nm(t,e,n,r,qe);if(s!==qe)return s}let i=jd(t,e,n,r,qe);if(i!==qe)return i}return Ld(e,n,r,o)}function jd(t,e,n,r,o){let i=Tm(n);if(typeof i=="function"){if(!bd(e,t,r))return r&x.Host?Pd(o,n,r):Ld(e,n,r,o);try{let s;if(s=i(r),s==null&&!(r&x.Optional))ya(n);else return s}finally{Md()}}else if(typeof i=="number"){let s=null,a=kd(t,e),l=pn,u=r&x.Host?e[Ge][Pe]:null;for((a===-1||r&x.SkipSelf)&&(l=a===-1?Na(t,e):e[a+8],l===pn||!Xu(r,!1)?a=-1:(s=e[T],a=co(l),e=fo(l,e)));a!==-1;){let c=e[T];if(Ju(i,a,c.data)){let d=Sm(a,e,n,s,r,u);if(d!==qe)return d}l=e[a+8],l!==pn&&Xu(r,e[T].data[a+8]===u)&&Ju(i,a,e)?(s=c,a=co(l),e=fo(l,e)):a=-1}}return o}function Sm(t,e,n,r,o,i){let s=e[T],a=s.data[t+8],l=r==null?Fo(a)&&Is:r!=s&&(a.type&3)!==0,u=o&x.Host&&i===a,c=Jr(a,s,n,l,u);return c!==null?zt(e,s,c,a):qe}function Jr(t,e,n,r,o){let i=t.providerIndexes,s=e.data,a=i&1048575,l=t.directiveStart,u=t.directiveEnd,c=i>>20,d=r?a:a+c,h=o?a+c:u;for(let f=d;f<h;f++){let p=s[f];if(f<l&&n===p||f>=l&&p.type===n)return f}if(o){let f=s[l];if(f&&yt(f)&&f.type===n)return l}return null}function zt(t,e,n,r){let o=t[n],i=e.data;if(mm(o)){let s=o;s.resolving&&fg(dg(i[n]));let a=po(s.canSeeViewProviders);s.resolving=!0;let l,u=s.injectImpl?he(s.injectImpl):null,c=bd(t,r,x.Default);try{o=t[n]=s.factory(void 0,i,t,r),e.firstCreatePass&&n>=r.directiveStart&&hm(n,i[n],e)}finally{u!==null&&he(u),po(a),s.resolving=!1,Md()}}return o}function Tm(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty($n)?t[$n]:void 0;return typeof e=="number"?e>=0?e&Od:Mm:e}function Ju(t,e,n){let r=1<<t;return!!(n[e+(t>>Fd)]&r)}function Xu(t,e){return!(t&x.Self)&&!(t&x.Host&&e)}var Lt=class{_tNode;_lView;constructor(e,n){this._tNode=e,this._lView=n}get(e,n,r){return Vd(this._tNode,this._lView,e,Ao(r),n)}};function Mm(){return new Lt(ne(),A())}function SN(t){return Wn(()=>{let e=t.prototype.constructor,n=e[to]||Cs(e),r=Object.prototype,o=Object.getPrototypeOf(t.prototype).constructor;for(;o&&o!==r;){let i=o[to]||Cs(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Cs(t){return Lc(t)?()=>{let e=Cs(ae(t));return e&&e()}:Vt(t)}function Nm(t,e,n,r,o){let i=t,s=e;for(;i!==null&&s!==null&&s[_]&2048&&!(s[_]&512);){let a=jd(i,s,n,r|x.Self,qe);if(a!==qe)return a;let l=i.parent;if(!l){let u=s[od];if(u){let c=u.get(n,qe,r);if(c!==qe)return c}l=Bd(s),s=s[Cn]}i=l}return o}function Bd(t){let e=t[T],n=e.type;return n===2?e.declTNode:n===1?t[Pe]:null}function xm(t){return Cm(ne(),t)}function ec(t,e=null,n=null,r){let o=$d(t,e,n,r);return o.resolveInjectorInitializers(),o}function $d(t,e=null,n=null,r,o=new Set){let i=[n||ge,xg(t)];return r=r||(typeof t=="object"?void 0:le(t)),new Un(i,e||Ro(),r||null,o)}var vt=class t{static THROW_IF_NOT_FOUND=Hn;static NULL=new io;static create(e,n){if(Array.isArray(e))return ec({name:""},n,e,"");{let r=e.name??"";return ec({name:r},e.parent,e.providers,r)}}static \u0275prov=V({token:t,providedIn:"any",factory:()=>Y(qc)});static __NG_ELEMENT_ID__=-1};var Am=new O("");Am.__NG_ELEMENT_ID__=t=>{let e=ne();if(e===null)throw new m(204,!1);if(e.type&2)return e.value;if(t&x.Optional)return null;throw new m(204,!1)};var Hd=!1,xa=(()=>{class t{static __NG_ELEMENT_ID__=Rm;static __NG_ENV_ID__=n=>n}return t})(),Ss=class extends xa{_lView;constructor(e){super(),this._lView=e}onDestroy(e){return gd(this._lView,e),()=>Kg(this._lView,e)}};function Rm(){return new Ss(A())}var En=class{},Aa=new O("",{providedIn:"root",factory:()=>!1});var Ud=new O(""),zd=new O(""),Ho=(()=>{class t{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new On(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=V({token:t,providedIn:"root",factory:()=>new t})}return t})();var Ts=class extends He{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(e=!1){super(),this.__isAsync=e,nd()&&(this.destroyRef=S(xa,{optional:!0})??void 0,this.pendingTasks=S(Ho,{optional:!0})??void 0)}emit(e){let n=R(null);try{super.next(e)}finally{R(n)}}subscribe(e,n,r){let o=e,i=n||(()=>null),s=r;if(e&&typeof e=="object"){let l=e;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return e instanceof K&&e.add(a),a}wrapInTimeout(e){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{e(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},pt=Ts;function go(...t){}function qd(t){let e,n;function r(){t=go;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),e!==void 0&&clearTimeout(e)}catch{}}return e=setTimeout(()=>{t(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{t(),r()})),()=>r()}function tc(t){return queueMicrotask(()=>t()),()=>{t=go}}var Ra="isAngularZone",mo=Ra+"_ID",Om=0,ue=class t{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new pt(!1);onMicrotaskEmpty=new pt(!1);onStable=new pt(!1);onError=new pt(!1);constructor(e){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Hd}=e;if(typeof Zone>"u")throw new m(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Pm(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ra)===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new m(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new m(909,!1)}run(e,n,r){return this._inner.run(e,n,r)}runTask(e,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,e,Fm,go,go);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(e,n,r){return this._inner.runGuarded(e,n,r)}runOutsideAngular(e){return this._outer.run(e)}},Fm={};function Oa(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function km(t){if(t.isCheckStableRunning||t.callbackScheduled)return;t.callbackScheduled=!0;function e(){qd(()=>{t.callbackScheduled=!1,Ms(t),t.isCheckStableRunning=!0,Oa(t),t.isCheckStableRunning=!1})}t.scheduleInRootZone?Zone.root.run(()=>{e()}):t._outer.run(()=>{e()}),Ms(t)}function Pm(t){let e=()=>{km(t)},n=Om++;t._inner=t._inner.fork({name:"angular",properties:{[Ra]:!0,[mo]:n,[mo+n]:!0},onInvokeTask:(r,o,i,s,a,l)=>{if(Lm(l))return r.invokeTask(i,s,a,l);try{return nc(t),r.invokeTask(i,s,a,l)}finally{(t.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),rc(t)}},onInvoke:(r,o,i,s,a,l,u)=>{try{return nc(t),r.invoke(i,s,a,l,u)}finally{t.shouldCoalesceRunChangeDetection&&!t.callbackScheduled&&!Vm(l)&&e(),rc(t)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(t._hasPendingMicrotasks=s.microTask,Ms(t),Oa(t)):s.change=="macroTask"&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}function Ms(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.callbackScheduled===!0?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function nc(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function rc(t){t._nesting--,Oa(t)}var Ns=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new pt;onMicrotaskEmpty=new pt;onStable=new pt;onError=new pt;run(e,n,r){return e.apply(n,r)}runGuarded(e,n,r){return e.apply(n,r)}runOutsideAngular(e){return e()}runTask(e,n,r,o){return e.apply(n,r)}};function Lm(t){return Gd(t,"__ignore_ng_zone__")}function Vm(t){return Gd(t,"__scheduler_tick__")}function Gd(t,e){return!Array.isArray(t)||t.length!==1?!1:t[0]?.data?.[e]===!0}var wn=class{_console=console;handleError(e){this._console.error("ERROR",e)}},jm=new O("",{providedIn:"root",factory:()=>{let t=S(ue),e=S(wn);return n=>t.runOutsideAngular(()=>e.handleError(n))}});function oc(t,e){return Fc(t,e)}function Bm(t){return Fc(Oc,t)}var TN=(oc.required=Bm,oc);function $m(){return Sn(ne(),A())}function Sn(t,e){return new Yt(Se(t,e))}var Yt=(()=>{class t{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=$m}return t})();function Hm(t){return t instanceof Yt?t.nativeElement:t}var ic=new Set;function Jt(t){ic.has(t)||(ic.add(t),performance?.mark?.("mark_feature_usage",{detail:{feature:t}}))}function Um(t){return typeof t=="function"&&t[ye]!==void 0}function MN(t,e){Jt("NgSignals");let n=fu(t),r=n[ye];return e?.equal&&(r.equal=e.equal),n.set=o=>gr(r,o),n.update=o=>pu(r,o),n.asReadonly=zm.bind(n),n}function zm(){let t=this[ye];if(t.readonlyFn===void 0){let e=()=>this();e[ye]=t,t.readonlyFn=e}return t.readonlyFn}function Wd(t){return Um(t)&&typeof t.set=="function"}function qm(){return this._results[Symbol.iterator]()}var xs=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new He}constructor(e=!1){this._emitDistinctChangesOnly=e}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,n){return this._results.reduce(e,n)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,n){this.dirty=!1;let r=Cg(e);(this._changesDetected=!bg(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(e){this._onDirty=e}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=qm};function Qd(t){return(t.flags&128)===128}var Zd=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(Zd||{}),Kd=new Map,Gm=0;function Wm(){return Gm++}function Qm(t){Kd.set(t[Oo],t)}function As(t){Kd.delete(t[Oo])}var sc="__ngContext__";function Dt(t,e){gt(e)?(t[sc]=e[Oo],Qm(e)):t[sc]=e}function Yd(t){return Xd(t[zn])}function Jd(t){return Xd(t[Fe])}function Xd(t){for(;t!==null&&!ot(t);)t=t[Fe];return t}var Rs;function NN(t){Rs=t}function Zm(){if(Rs!==void 0)return Rs;if(typeof document<"u")return document;throw new m(210,!1)}var xN=new O("",{providedIn:"root",factory:()=>Km}),Km="ng",Ym=new O(""),Jm=new O("",{providedIn:"platform",factory:()=>"unknown"});var AN=new O(""),RN=new O("",{providedIn:"root",factory:()=>Zm().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Xm="h",ey="b";var ef=!1,ty=new O("",{providedIn:"root",factory:()=>ef});var Fa=function(t){return t[t.CHANGE_DETECTION=0]="CHANGE_DETECTION",t[t.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",t}(Fa||{}),Uo=new O("");var cn=function(t){return t[t.EarlyRead=0]="EarlyRead",t[t.Write=1]="Write",t[t.MixedReadWrite=2]="MixedReadWrite",t[t.Read=3]="Read",t}(cn||{}),tf=(()=>{class t{impl=null;execute(){this.impl?.execute()}static \u0275prov=V({token:t,providedIn:"root",factory:()=>new t})}return t})(),ny=[cn.EarlyRead,cn.Write,cn.MixedReadWrite,cn.Read],ry=(()=>{class t{ngZone=S(ue);scheduler=S(En);errorHandler=S(wn,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){S(Uo,{optional:!0})}execute(){this.executing=!0;for(let n of ny)for(let r of this.sequences)if(!(r.erroredOrDestroyed||!r.hooks[n]))try{r.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>r.hooks[n](r.pipelinedValue),r.snapshot))}catch(o){r.erroredOrDestroyed=!0,this.errorHandler?.handleError(o)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(8),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(7))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Fa.AFTER_NEXT_RENDER,n):n()}static \u0275prov=V({token:t,providedIn:"root",factory:()=>new t})}return t})(),Os=class{impl;hooks;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(e,n,r,o,i=null){this.impl=e,this.hooks=n,this.once=r,this.snapshot=i,this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function oy(t,e){!e?.injector&&rd(oy);let n=e?.injector??S(vt);return Jt("NgAfterRender"),nf(t,n,e,!1)}function iy(t,e){!e?.injector&&rd(iy);let n=e?.injector??S(vt);return Jt("NgAfterNextRender"),nf(t,n,e,!0)}function sy(t,e){if(t instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[e]=t,n}else return[t.earlyRead,t.write,t.mixedReadWrite,t.read]}function nf(t,e,n,r){let o=e.get(tf);o.impl??=e.get(ry);let i=e.get(Uo,null,{optional:!0}),s=n?.phase??cn.MixedReadWrite,a=n?.manualCleanup!==!0?e.get(xa):null,l=new Os(o.impl,sy(t,s),r,a,i?.snapshot(null));return o.impl.register(l),l}var ay=()=>null;function ka(t,e,n=!1){return ay(t,e,n)}function rf(t,e){let n=t.contentQueries;if(n!==null){let r=R(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=t.data[s];Sa(i),a.contentQueries(2,e[s],s)}}}finally{R(r)}}}function Fs(t,e,n){Sa(0);let r=R(null);try{e(t,n)}finally{R(r)}}function Pa(t,e,n){if(wa(e)){let r=R(null);try{let o=e.directiveStart,i=e.directiveEnd;for(let s=o;s<i;s++){let a=t.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{R(r)}}}var qn=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(qn||{}),zr;function ly(){if(zr===void 0&&(zr=null,ft.trustedTypes))try{zr=ft.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return zr}function zo(t){return ly()?.createHTML(t)||t}var qr;function uy(){if(qr===void 0&&(qr=null,ft.trustedTypes))try{qr=ft.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return qr}function ac(t){return uy()?.createScriptURL(t)||t}var nt=class{changingThisBreaksApplicationSecurity;constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ac})`}},ks=class extends nt{getTypeName(){return"HTML"}},Ps=class extends nt{getTypeName(){return"Style"}},Ls=class extends nt{getTypeName(){return"Script"}},Vs=class extends nt{getTypeName(){return"URL"}},js=class extends nt{getTypeName(){return"ResourceURL"}};function Yn(t){return t instanceof nt?t.changingThisBreaksApplicationSecurity:t}function of(t,e){let n=cy(t);if(n!=null&&n!==e){if(n==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${n} (see ${Ac})`)}return n===e}function cy(t){return t instanceof nt&&t.getTypeName()||null}function ON(t){return new ks(t)}function FN(t){return new Ps(t)}function kN(t){return new Ls(t)}function PN(t){return new Vs(t)}function LN(t){return new js(t)}function dy(t){let e=new $s(t);return fy()?new Bs(e):e}var Bs=class{inertDocumentHelper;constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(zo(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):(n.firstChild?.remove(),n)}catch{return null}}},$s=class{defaultDoc;inertDocument;constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let n=this.inertDocument.createElement("template");return n.innerHTML=zo(e),n}};function fy(){try{return!!new window.DOMParser().parseFromString(zo(""),"text/html")}catch{return!1}}var py=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function sf(t){return t=String(t),t.match(py)?t:"unsafe:"+t}function it(t){let e={};for(let n of t.split(","))e[n]=!0;return e}function Jn(...t){let e={};for(let n of t)for(let r in n)n.hasOwnProperty(r)&&(e[r]=!0);return e}var af=it("area,br,col,hr,img,wbr"),lf=it("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),uf=it("rp,rt"),hy=Jn(uf,lf),gy=Jn(lf,it("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),my=Jn(uf,it("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),lc=Jn(af,gy,my,hy),cf=it("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),yy=it("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),vy=it("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Dy=Jn(cf,yy,vy),Ey=it("script,style,template"),Hs=class{sanitizedSomething=!1;buf=[];sanitizeChildren(e){let n=e.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Iy(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=_y(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(e){let n=uc(e).toLowerCase();if(!lc.hasOwnProperty(n))return this.sanitizedSomething=!0,!Ey.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=e.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Dy.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;cf[a]&&(l=sf(l)),this.buf.push(" ",s,'="',cc(l),'"')}return this.buf.push(">"),!0}endElement(e){let n=uc(e).toLowerCase();lc.hasOwnProperty(n)&&!af.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(e){this.buf.push(cc(e))}};function wy(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function _y(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw df(e);return e}function Iy(t){let e=t.firstChild;if(e&&wy(t,e))throw df(e);return e}function uc(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function df(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var by=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Cy=/([^\#-~ |!])/g;function cc(t){return t.replace(/&/g,"&amp;").replace(by,function(e){let n=e.charCodeAt(0),r=e.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Cy,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Gr;function VN(t,e){let n=null;try{Gr=Gr||dy(t);let r=e?String(e):"";n=Gr.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Gr.getInertBodyElement(r)}while(r!==i);let a=new Hs().sanitizeChildren(dc(n)||n);return zo(a)}finally{if(n){let r=dc(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function dc(t){return"content"in t&&Sy(t)?t.content:null}function Sy(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var La=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(La||{});function Ty(t){let e=ff();return e?e.sanitize(La.URL,t)||"":of(t,"URL")?Yn(t):sf(xo(t))}function My(t){let e=ff();if(e)return ac(e.sanitize(La.RESOURCE_URL,t)||"");if(of(t,"ResourceURL"))return ac(Yn(t));throw new m(904,!1)}function Ny(t,e){return e==="src"&&(t==="embed"||t==="frame"||t==="iframe"||t==="media"||t==="script")||e==="href"&&(t==="base"||t==="link")?My:Ty}function jN(t,e,n){return Ny(e,n)(t)}function ff(){let t=A();return t&&t[et].sanitizer}var xy=/^>|^->|<!--|-->|--!>|<!-$/g,Ay=/(<|>)/g,Ry="\u200B$1\u200B";function Oy(t){return t.replace(xy,e=>e.replace(Ay,Ry))}function BN(t){return t.ownerDocument}function pf(t){return t instanceof Function?t():t}var Et=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(Et||{});function Fy(t,e,n){let r=t.length;for(;;){let o=t.indexOf(e,n);if(o===-1)return o;if(o===0||t.charCodeAt(o-1)<=32){let i=e.length;if(o+i===r||t.charCodeAt(o+i)<=32)return o}n=o+1}}var hf="ng-template";function ky(t,e,n,r){let o=0;if(r){for(;o<e.length&&typeof e[o]=="string";o+=2)if(e[o]==="class"&&Fy(e[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Va(t))return!1;if(o=e.indexOf(1,o),o>-1){let i;for(;++o<e.length&&typeof(i=e[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Va(t){return t.type===4&&t.value!==hf}function Py(t,e,n){let r=t.type===4&&!n?hf:t.value;return e===r}function Ly(t,e,n){let r=4,o=t.attrs,i=o!==null?By(o):0,s=!1;for(let a=0;a<e.length;a++){let l=e[a];if(typeof l=="number"){if(!s&&!Re(r)&&!Re(l))return!1;if(s&&Re(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!Py(t,l,n)||l===""&&e.length===1){if(Re(r))return!1;s=!0}}else if(r&8){if(o===null||!ky(t,o,l,n)){if(Re(r))return!1;s=!0}}else{let u=e[++a],c=Vy(l,o,Va(t),n);if(c===-1){if(Re(r))return!1;s=!0;continue}if(u!==""){let d;if(c>i?d="":d=o[c+1].toLowerCase(),r&2&&u!==d){if(Re(r))return!1;s=!0}}}}return Re(r)||s}function Re(t){return(t&1)===0}function Vy(t,e,n,r){if(e===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<e.length;){let s=e[o];if(s===t)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=e[++o];for(;typeof a=="string";)a=e[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return $y(e,t)}function jy(t,e,n=!1){for(let r=0;r<e.length;r++)if(Ly(t,e[r],n))return!0;return!1}function By(t){for(let e=0;e<t.length;e++){let n=t[e];if(Ad(n))return e}return t.length}function $y(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){let r=t[n];if(typeof r=="number")return-1;if(r===e)return n;n++}return-1}function fc(t,e){return t?":not("+e.trim()+")":e}function Hy(t){let e=t[0],n=1,r=2,o="",i=!1;for(;n<t.length;){let s=t[n];if(typeof s=="string")if(r&2){let a=t[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Re(s)&&(e+=fc(i,o),o=""),r=s,i=i||!Re(r);n++}return o!==""&&(e+=fc(i,o)),e}function Uy(t){return t.map(Hy).join(",")}function zy(t){let e=[],n=[],r=1,o=2;for(;r<t.length;){let i=t[r];if(typeof i=="string")o===2?i!==""&&e.push(i,t[++r]):o===8&&n.push(i);else{if(!Re(o))break;o=i}r++}return{attrs:e,classes:n}}var Ze={};function $N(t=1){gf(Q(),A(),Kt()+t,!1)}function gf(t,e,n,r){if(!r)if((e[_]&3)===3){let i=t.preOrderCheckHooks;i!==null&&Kr(e,i,n)}else{let i=t.preOrderHooks;i!==null&&Yr(e,i,0,n)}Ht(n)}function ce(t,e=x.Default){let n=A();if(n===null)return Y(t,e);let r=ne();return Vd(r,n,ae(t),e)}function HN(){let t="invalid";throw new Error(t)}function mf(t,e,n,r,o,i){let s=R(null);try{let a=null;o&Et.SignalBased&&(a=e[r][ye]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Et.HasDecoratorInputTransform&&(i=t.inputTransforms[r].call(e,i)),t.setInput!==null?t.setInput(e,a,i,n,r):sd(e,a,r,i)}finally{R(s)}}function qy(t,e){return t.createText(e)}function Gy(t,e,n){t.setValue(e,n)}function Wy(t,e){return t.createComment(Oy(e))}function yf(t,e,n){return t.createElement(e,n)}function yo(t,e,n,r,o){t.insertBefore(e,n,r,o)}function vf(t,e,n){t.appendChild(e,n)}function pc(t,e,n,r,o){r!==null?yo(t,e,n,r,o):vf(t,e,n)}function Qy(t,e,n){t.removeChild(null,e,n)}function Zy(t,e,n){t.setAttribute(e,"style",n)}function Ky(t,e,n){n===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n)}function Df(t,e,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Dm(t,e,r),o!==null&&Ky(t,e,o),i!==null&&Zy(t,e,i)}function qo(t,e,n,r,o,i,s,a,l,u,c){let d=e.blueprint.slice();return d[rt]=o,d[_]=r|4|128|8|64|1024,(u!==null||t&&t[_]&2048)&&(d[_]|=2048),hd(d),d[ie]=d[Cn]=t,d[ke]=n,d[et]=s||t&&t[et],d[W]=a||t&&t[W],d[mn]=l||t&&t[mn]||null,d[Pe]=i,d[Oo]=Wm(),d[so]=c,d[od]=u,d[Ge]=e.type==2?t[Ge]:d,d}function Ef(t,e,n,r){if(n===0)return-1;let o=e.length;for(let i=0;i<n;i++)e.push(r),t.blueprint.push(r),t.data.push(null);return o}function wf(t,e,n,r,o){let i=Kt(),s=r&2;try{Ht(-1),s&&e.length>De&&gf(t,e,De,!1),Ce(s?2:0,o),n(r,o)}finally{Ht(i),Ce(s?3:1,o)}}function ja(t,e,n){vd()&&(sv(t,e,n,Se(n,e)),(n.flags&64)===64&&bf(t,e,n))}function Ba(t,e,n=Se){let r=e.localNames;if(r!==null){let o=e.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(e,t):t[s];t[o++]=a}}}function _f(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=$a(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function $a(t,e,n,r,o,i,s,a,l,u,c){let d=De+r,h=d+o,f=Yy(d,h),p=typeof u=="function"?u():u;return f[T]={type:t,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:e,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:p,incompleteFirstPass:!1,ssrId:c}}function Yy(t,e){let n=[];for(let r=0;r<e;r++)n.push(r<t?null:Ze);return n}function Jy(t,e,n,r){let i=r.get(ty,ef)||n===qn.ShadowDom,s=t.selectRootElement(e,i);return Xy(s),s}function Xy(t){ev(t)}var ev=()=>null;function hc(t,e,n,r,o){for(let i in e){if(!e.hasOwnProperty(i))continue;let s=e[i];if(s===void 0)continue;r??={};let a,l=Et.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let u=i;if(o!==null){if(!o.hasOwnProperty(i))continue;u=o[i]}t===0?gc(r,n,u,a,l):gc(r,n,u,a)}return r}function gc(t,e,n,r,o){let i;t.hasOwnProperty(n)?(i=t[n]).push(e,r):i=t[n]=[e,r],o!==void 0&&i.push(o)}function tv(t,e,n){let r=e.directiveStart,o=e.directiveEnd,i=t.data,s=e.attrs,a=[],l=null,u=null;for(let c=r;c<o;c++){let d=i[c],h=n?n.get(d):null,f=h?h.inputs:null,p=h?h.outputs:null;l=hc(0,d.inputs,c,l,f),u=hc(1,d.outputs,c,u,p);let g=l!==null&&s!==null&&!Va(e)?yv(l,c,s):null;a.push(g)}l!==null&&(l.hasOwnProperty("class")&&(e.flags|=8),l.hasOwnProperty("style")&&(e.flags|=16)),e.initialInputs=a,e.inputs=l,e.outputs=u}function nv(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function Ha(t,e,n,r,o,i,s,a){let l=Se(e,n),u=e.inputs,c;!a&&u!=null&&(c=u[r])?(za(t,n,c,r,o),Fo(e)&&rv(n,e.index)):e.type&3?(r=nv(r),o=s!=null?s(o,e.value||"",r):o,i.setProperty(l,r,o)):e.type&12}function rv(t,e){let n=_t(e,t);n[_]&16||(n[_]|=64)}function Ua(t,e,n,r){if(vd()){let o=r===null?null:{"":-1},i=lv(t,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&If(t,e,n,s,o,a),o&&uv(n,r,o)}n.mergedAttrs=Dn(n.mergedAttrs,n.attrs)}function If(t,e,n,r,o,i){for(let u=0;u<r.length;u++)bs(ho(n,e),t,r[u].type);dv(n,t.data.length,r.length);for(let u=0;u<r.length;u++){let c=r[u];c.providersResolver&&c.providersResolver(c)}let s=!1,a=!1,l=Ef(t,e,r.length,null);for(let u=0;u<r.length;u++){let c=r[u];n.mergedAttrs=Dn(n.mergedAttrs,c.hostAttrs),fv(t,n,e,l,c),cv(l,c,o),c.contentQueries!==null&&(n.flags|=4),(c.hostBindings!==null||c.hostAttrs!==null||c.hostVars!==0)&&(n.flags|=64);let d=c.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((t.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((t.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}tv(t,n,i)}function ov(t,e,n,r,o){let i=o.hostBindings;if(i){let s=t.hostBindingOpCodes;s===null&&(s=t.hostBindingOpCodes=[]);let a=~e.index;iv(s)!=a&&s.push(a),s.push(n,r,i)}}function iv(t){let e=t.length;for(;e>0;){let n=t[--e];if(typeof n=="number"&&n<0)return n}return 0}function sv(t,e,n,r){let o=n.directiveStart,i=n.directiveEnd;Fo(n)&&pv(e,n,t.data[o+n.componentOffset]),t.firstCreatePass||ho(n,e),Dt(r,e);let s=n.initialInputs;for(let a=o;a<i;a++){let l=t.data[a],u=zt(e,t,a,n);if(Dt(u,e),s!==null&&mv(e,a-o,u,l,n,s),yt(l)){let c=_t(n.index,e);c[ke]=zt(e,t,a,n)}}}function bf(t,e,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=um();try{Ht(i);for(let a=r;a<o;a++){let l=t.data[a],u=e[a];ws(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&av(l,u)}}finally{Ht(-1),ws(s)}}function av(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function lv(t,e){let n=t.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(jy(e,s.selectors,!1))if(r||(r=[]),yt(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let l=a.length;Us(t,e,l)}else r.unshift(s),Us(t,e,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Us(t,e,n){e.componentOffset=n,(t.components??=[]).push(e.index)}function uv(t,e,n){if(e){let r=t.localNames=[];for(let o=0;o<e.length;o+=2){let i=n[e[o+1]];if(i==null)throw new m(-301,!1);r.push(e[o],i)}}}function cv(t,e,n){if(n){if(e.exportAs)for(let r=0;r<e.exportAs.length;r++)n[e.exportAs[r]]=t;yt(e)&&(n[""]=t)}}function dv(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function fv(t,e,n,r,o){t.data[r]=o;let i=o.factory||(o.factory=Vt(o.type,!0)),s=new Ut(i,yt(o),ce);t.blueprint[r]=s,n[r]=s,ov(t,e,r,Ef(t,n,o.hostVars,Ze),o)}function Cf(t){let e=16;return t.signals?e=4096:t.onPush&&(e=64),e}function pv(t,e,n){let r=Se(e,t),o=_f(n),i=t[et].rendererFactory,s=Go(t,qo(t,o,null,Cf(n),r,e,null,i.createRenderer(r,n),null,null,null));t[e.index]=s}function hv(t,e,n,r,o,i){let s=Se(t,e);gv(e[W],s,i,t.value,n,r,o)}function gv(t,e,n,r,o,i,s){if(i==null)t.removeAttribute(e,o,n);else{let a=s==null?xo(i):s(i,r||"",o);t.setAttribute(e,o,a,n)}}function mv(t,e,n,r,o,i){let s=i[e];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],u=s[a++],c=s[a++],d=s[a++];mf(r,n,l,u,c,d)}}function yv(t,e,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(t.hasOwnProperty(i)){r===null&&(r=[]);let s=t[i];for(let a=0;a<s.length;a+=3)if(s[a]===e){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function Sf(t,e,n,r){return[t,!0,0,e,null,r,null,n,null,null]}function Go(t,e){return t[zn]?t[Wu][Fe]=e:t[zn]=e,t[Wu]=e,e}function Tf(t,e){let n=t[mn],r=n?n.get(wn,null):null;r&&r.handleError(e)}function za(t,e,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],l=n[i++],u=e[s],c=t.data[s];mf(c,u,r,a,l,o)}}function vv(t,e){let n=_t(e,t),r=n[T];Dv(r,n);let o=n[rt];o!==null&&n[so]===null&&(n[so]=ka(o,n[mn])),qa(r,n,n[ke])}function Dv(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}function qa(t,e,n){Ta(e);try{let r=t.viewQuery;r!==null&&Fs(1,r,n);let o=t.template;o!==null&&wf(t,e,o,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),e[tt]?.finishViewCreation(t),t.staticContentQueries&&rf(t,e),t.staticViewQueries&&Fs(2,t.viewQuery,n);let i=t.components;i!==null&&Ev(e,i)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),r}finally{e[_]&=-5,Ma()}}function Ev(t,e){for(let n=0;n<e.length;n++)vv(t,e[n])}var vo=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(vo||{}),wv;function Ga(t,e){return wv(t,e)}function dn(t,e,n,r,o){if(r!=null){let i,s=!1;ot(r)?i=r:gt(r)&&(s=!0,r=r[rt]);let a=We(r);t===0&&n!==null?o==null?vf(e,n,a):yo(e,n,a,o||null,!0):t===1&&n!==null?yo(e,n,a,o||null,!0):t===2?Qy(e,a,s):t===3&&e.destroyNode(a),i!=null&&Fv(e,t,i,n,o)}}function _v(t,e){Mf(t,e),e[rt]=null,e[Pe]=null}function Iv(t,e,n,r,o,i){r[rt]=o,r[Pe]=e,Qo(t,r,n,1,o,i)}function Mf(t,e){e[et].changeDetectionScheduler?.notify(10),Qo(t,e,e[W],2,null,null)}function bv(t){let e=t[zn];if(!e)return rs(t[T],t);for(;e;){let n=null;if(gt(e))n=e[zn];else{let r=e[Ee];r&&(n=r)}if(!n){for(;e&&!e[Fe]&&e!==t;)gt(e)&&rs(e[T],e),e=e[ie];e===null&&(e=t),gt(e)&&rs(e[T],e),n=e&&e[Fe]}e=n}}function Cv(t,e,n,r){let o=Ee+r,i=n.length;r>0&&(n[o-1][Fe]=e),r<i-Ee?(e[Fe]=n[o],zc(n,Ee+r,e)):(n.push(e),e[Fe]=null),e[ie]=n;let s=e[jt];s!==null&&n!==s&&Nf(s,e);let a=e[tt];a!==null&&a.insertView(t),Ds(e),e[_]|=128}function Nf(t,e){let n=t[yn],r=e[ie];if(gt(r))t[_]|=2;else{let o=r[ie][Ge];e[Ge]!==o&&(t[_]|=2)}n===null?t[yn]=[e]:n.push(e)}function Wa(t,e){let n=t[yn],r=n.indexOf(e);n.splice(r,1)}function zs(t,e){if(t.length<=Ee)return;let n=Ee+e,r=t[n];if(r){let o=r[jt];o!==null&&o!==t&&Wa(o,r),e>0&&(t[n-1][Fe]=r[Fe]);let i=ro(t,Ee+e);_v(r[T],r);let s=i[tt];s!==null&&s.detachView(i[T]),r[ie]=null,r[Fe]=null,r[_]&=-129}return r}function xf(t,e){if(Zn(e))return;let n=e[W];n.destroyNode&&Qo(t,e,n,3,null,null),bv(e)}function rs(t,e){if(Zn(e))return;let n=R(null);try{e[_]&=-129,e[_]|=256,e[ve]&&ki(e[ve]),Tv(t,e),Sv(t,e),e[T].type===1&&e[W].destroy();let r=e[jt];if(r!==null&&ot(e[ie])){r!==e[ie]&&Wa(r,e);let o=e[tt];o!==null&&o.detachView(t)}As(e)}finally{R(n)}}function Sv(t,e){let n=t.cleanup,r=e[ao];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(e[ao]=null);let o=e[ht];if(o!==null){e[ht]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=e[lo];if(i!==null){e[lo]=null;for(let s of i)s.destroy()}}function Tv(t,e){let n;if(t!=null&&(n=t.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=e[n[r]];if(!(o instanceof Ut)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],l=i[s+1];Ce(4,a,l);try{l.call(a)}finally{Ce(5,a,l)}}else{Ce(4,o,i);try{i.call(o)}finally{Ce(5,o,i)}}}}}function Mv(t,e,n){return Nv(t,e.parent,n)}function Nv(t,e,n){let r=e;for(;r!==null&&r.type&168;)e=r,r=e.parent;if(r===null)return n[rt];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=t.data[r.directiveStart+o];if(i===qn.None||i===qn.Emulated)return null}return Se(r,n)}}function xv(t,e,n){return Rv(t,e,n)}function Av(t,e,n){return t.type&40?Se(t,n):null}var Rv=Av,mc;function Wo(t,e,n,r){let o=Mv(t,r,e),i=e[W],s=r.parent||e[Pe],a=xv(s,r,e);if(o!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)pc(i,o,n[l],a,!1);else pc(i,o,n,a,!1);mc!==void 0&&mc(i,r,e,n,o)}function Bn(t,e){if(e!==null){let n=e.type;if(n&3)return Se(e,t);if(n&4)return qs(-1,t[e.index]);if(n&8){let r=e.child;if(r!==null)return Bn(t,r);{let o=t[e.index];return ot(o)?qs(-1,o):We(o)}}else{if(n&128)return Bn(t,e.next);if(n&32)return Ga(e,t)()||We(t[e.index]);{let r=Af(t,e);if(r!==null){if(Array.isArray(r))return r[0];let o=$t(t[Ge]);return Bn(o,r)}else return Bn(t,e.next)}}}return null}function Af(t,e){if(e!==null){let r=t[Ge][Pe],o=e.projection;return r.projection[o]}return null}function qs(t,e){let n=Ee+t+1;if(n<e.length){let r=e[n],o=r[T].firstChild;if(o!==null)return Bn(r,o)}return e[Bt]}function Qa(t,e,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&e===0&&(a&&Dt(We(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)Qa(t,e,n.child,r,o,i,!1),dn(e,t,o,a,i);else if(l&32){let u=Ga(n,r),c;for(;c=u();)dn(e,t,o,c,i);dn(e,t,o,a,i)}else l&16?Ov(t,e,r,n,o,i):dn(e,t,o,a,i);n=s?n.projectionNext:n.next}}function Qo(t,e,n,r,o,i){Qa(n,r,t.firstChild,e,o,i,!1)}function Ov(t,e,n,r,o,i){let s=n[Ge],l=s[Pe].projection[r.projection];if(Array.isArray(l))for(let u=0;u<l.length;u++){let c=l[u];dn(e,t,o,c,i)}else{let u=l,c=s[ie];Qd(r)&&(u.flags|=128),Qa(t,e,u,c,o,i,!0)}}function Fv(t,e,n,r,o){let i=n[Bt],s=We(n);i!==s&&dn(e,t,r,i,o);for(let a=Ee;a<n.length;a++){let l=n[a];Qo(l[T],l,t,e,r,i)}}function kv(t,e,n,r,o){if(e)o?t.addClass(n,r):t.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:vo.DashCase;o==null?t.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=vo.Important),t.setStyle(n,r,o,i))}}function Pv(t,e,n,r){let o=R(null);try{let i=e.tView,a=t[_]&4096?4096:16,l=qo(t,i,n,a,null,e,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=t[e.index];l[jt]=u;let c=t[tt];return c!==null&&(l[tt]=c.createEmbeddedView(i)),qa(i,l,n),l}finally{R(o)}}function yc(t,e){return!e||e.firstChild===null||Qd(t)}function Lv(t,e,n,r=!0){let o=e[T];if(Cv(o,e,t,n),r){let s=qs(n,t),a=e[W],l=a.parentNode(t[Bt]);l!==null&&Iv(o,t[Pe],a,e,l,s)}let i=e[so];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Do(t,e,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=e[n.index];i!==null&&r.push(We(i)),ot(i)&&Vv(i,r);let s=n.type;if(s&8)Do(t,e,n.child,r);else if(s&32){let a=Ga(n,e),l;for(;l=a();)r.push(l)}else if(s&16){let a=Af(e,n);if(Array.isArray(a))r.push(...a);else{let l=$t(e[Ge]);Do(l[T],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Vv(t,e){for(let n=Ee;n<t.length;n++){let r=t[n],o=r[T].firstChild;o!==null&&Do(r[T],r,o,e)}t[Bt]!==t[rt]&&e.push(t[Bt])}var Rf=[];function jv(t){return t[ve]??Bv(t)}function Bv(t){let e=Rf.pop()??Object.create(Hv);return e.lView=t,e}function $v(t){t.lView[ve]!==t&&(t.lView=null,Rf.push(t))}var Hv=$e(fe({},An),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{Lo(t.lView)},consumerOnSignalRead(){this.lView[ve]=this}});function Uv(t){let e=t[ve]??Object.create(zv);return e.lView=t,e}var zv=$e(fe({},An),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{let e=$t(t.lView);for(;e&&!Of(e[T]);)e=$t(e);e&&Ia(e)},consumerOnSignalRead(){this.lView[ve]=this}});function Of(t){return t.type!==2}function Ff(t){if(t[lo]===null)return;let e=!0;for(;e;){let n=!1;for(let r of t[lo])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));e=n&&!!(t[_]&8192)}}var qv=100;function kf(t,e=!0,n=0){let o=t[et].rendererFactory,i=!1;i||o.begin?.();try{Gv(t,n)}catch(s){throw e&&Tf(t,s),s}finally{i||o.end?.()}}function Gv(t,e){let n=wd();try{Zu(!0),Gs(t,e);let r=0;for(;Po(t);){if(r===qv)throw new m(103,!1);r++,Gs(t,1)}}finally{Zu(n)}}function Wv(t,e,n,r){if(Zn(e))return;let o=e[_],i=!1,s=!1;Ta(e);let a=!0,l=null,u=null;i||(Of(t)?(u=jv(e),l=fr(u)):nu()===null?(a=!1,u=Uv(e),l=fr(u)):e[ve]&&(ki(e[ve]),e[ve]=null));try{hd(e),sm(t.bindingStartIndex),n!==null&&wf(t,e,n,2,r);let c=(o&3)===3;if(!i)if(c){let f=t.preOrderCheckHooks;f!==null&&Kr(e,f,null)}else{let f=t.preOrderHooks;f!==null&&Yr(e,f,0,null),es(e,0)}if(s||Qv(e),Ff(e),Pf(e,0),t.contentQueries!==null&&rf(t,e),!i)if(c){let f=t.contentCheckHooks;f!==null&&Kr(e,f)}else{let f=t.contentHooks;f!==null&&Yr(e,f,1),es(e,1)}Kv(t,e);let d=t.components;d!==null&&Vf(e,d,0);let h=t.viewQuery;if(h!==null&&Fs(2,h,r),!i)if(c){let f=t.viewCheckHooks;f!==null&&Kr(e,f)}else{let f=t.viewHooks;f!==null&&Yr(e,f,2),es(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[Xi]){for(let f of e[Xi])f();e[Xi]=null}i||(e[_]&=-73)}catch(c){throw i||Lo(e),c}finally{u!==null&&(Oi(u,l),a&&$v(u)),Ma()}}function Pf(t,e){for(let n=Yd(t);n!==null;n=Jd(n))for(let r=Ee;r<n.length;r++){let o=n[r];Lf(o,e)}}function Qv(t){for(let e=Yd(t);e!==null;e=Jd(e)){if(!(e[_]&2))continue;let n=e[yn];for(let r=0;r<n.length;r++){let o=n[r];Ia(o)}}}function Zv(t,e,n){let r=_t(e,t);Lf(r,n)}function Lf(t,e){_a(t)&&Gs(t,e)}function Gs(t,e){let r=t[T],o=t[_],i=t[ve],s=!!(e===0&&o&16);if(s||=!!(o&64&&e===0),s||=!!(o&1024),s||=!!(i?.dirty&&Fi(i)),s||=!1,i&&(i.dirty=!1),t[_]&=-9217,s)Wv(r,t,r.template,t[ke]);else if(o&8192){Ff(t),Pf(t,1);let a=r.components;a!==null&&Vf(t,a,1)}}function Vf(t,e,n){for(let r=0;r<e.length;r++)Zv(t,e[r],n)}function Kv(t,e){let n=t.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Ht(~o);else{let i=o,s=n[++r],a=n[++r];lm(s,i);let l=e[i];Ce(24,l),a(2,l),Ce(25,l)}}}finally{Ht(-1)}}function Za(t,e){let n=wd()?64:1088;for(t[et].changeDetectionScheduler?.notify(e);t;){t[_]|=n;let r=$t(t);if(ys(t)&&!r)return t;t=r}return null}var qt=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let e=this._lView,n=e[T];return Do(n,e,n.firstChild,[])}constructor(e,n,r=!0){this._lView=e,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[ke]}get dirty(){return!!(this._lView[_]&9280)||!!this._lView[ve]?.dirty}set context(e){this._lView[ke]=e}get destroyed(){return Zn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[ie];if(ot(e)){let n=e[uo],r=n?n.indexOf(this):-1;r>-1&&(zs(e,r),ro(n,r))}this._attachedToViewContainer=!1}xf(this._lView[T],this._lView)}onDestroy(e){gd(this._lView,e)}markForCheck(){Za(this._cdRefInjectingView||this._lView,4)}markForRefresh(){Ia(this._cdRefInjectingView||this._lView)}detach(){this._lView[_]&=-129}reattach(){Ds(this._lView),this._lView[_]|=128}detectChanges(){this._lView[_]|=1024,kf(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new m(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let e=ys(this._lView),n=this._lView[jt];n!==null&&!e&&Wa(n,this._lView),Mf(this._lView[T],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new m(902,!1);this._appRef=e;let n=ys(this._lView),r=this._lView[jt];r!==null&&!n&&Nf(r,this._lView),Ds(this._lView)}},Gt=(()=>{class t{static __NG_ELEMENT_ID__=Xv}return t})(),Yv=Gt,Jv=class extends Yv{_declarationLView;_declarationTContainer;elementRef;constructor(e,n,r){super(),this._declarationLView=e,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(e,n){return this.createEmbeddedViewImpl(e,n)}createEmbeddedViewImpl(e,n,r){let o=Pv(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:n,dehydratedView:r});return new qt(o)}};function Xv(){return Zo(ne(),A())}function Zo(t,e){return t.type&4?new Jv(e,t,Sn(t,e)):null}function Xn(t,e,n,r,o){let i=t.data[e];if(i===null)i=eD(t,e,n,r,o),am()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=om();i.injectorIndex=s===null?-1:s.injectorIndex}return Zt(i,!0),i}function eD(t,e,n,r,o){let i=Dd(),s=ba(),a=s?i:i&&i.parent,l=t.data[e]=nD(t,a,n,e,r,o);return tD(t,l,i,s),l}function tD(t,e,n,r){t.firstChild===null&&(t.firstChild=e),n!==null&&(r?n.child==null&&e.parent!==null&&(n.child=e):n.next===null&&(n.next=e,e.prev=n))}function nD(t,e,n,r,o,i){let s=e?e.injectorIndex:-1,a=0;return tm()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var zN=new RegExp(`^(\\d+)*(${ey}|${Xm})*(.*)`);var rD=()=>null;function vc(t,e){return rD(t,e)}var Ws=class{},Eo=class{},Qs=class{resolveComponentFactory(e){throw Error(`No component factory found for ${le(e)}.`)}},_n=class{static NULL=new Qs},wo=class{},Ka=(()=>{class t{destroyNode=null;static __NG_ELEMENT_ID__=()=>oD()}return t})();function oD(){let t=A(),e=ne(),n=_t(e.index,t);return(gt(n)?n:t)[W]}var iD=(()=>{class t{static \u0275prov=V({token:t,providedIn:"root",factory:()=>null})}return t})();function _o(t,e,n){let r=n?t.styles:null,o=n?t.classes:null,i=0;if(e!==null)for(let s=0;s<e.length;s++){let a=e[s];if(typeof a=="number")i=a;else if(i==1)o=cs(o,a);else if(i==2){let l=a,u=e[++s];r=cs(r,l+": "+u+";")}}n?t.styles=r:t.stylesWithoutHost=r,n?t.classes=o:t.classesWithoutHost=o}var Io=class extends _n{ngModule;constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let n=Xe(e);return new Wt(n,this.ngModule)}};function Dc(t,e){let n=[];for(let r in t){if(!t.hasOwnProperty(r))continue;let o=t[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:Et.None;e?n.push({propName:s,templateName:r,isSignal:(a&Et.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function sD(t){let e=t.toLowerCase();return e==="svg"?cd:e==="math"?Gg:null}function aD(t){let{attrs:e,classes:n}=zy(t),r=e;return n.length&&r.push(1,...n),r}var Wt=class extends Eo{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;get inputs(){let e=this.componentDef,n=e.inputTransforms,r=Dc(e.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Dc(this.componentDef.outputs,!1)}constructor(e,n){super(),this.componentDef=e,this.ngModule=n,this.componentType=e.type,this.selector=Uy(e.selectors),this.ngContentSelectors=e.ngContentSelectors?e.ngContentSelectors:[],this.isBoundToModule=!!n}create(e,n,r,o){let i=R(null);try{o=o||this.ngModule;let s=o instanceof mt?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new _s(e,s):e,l=a.get(wo,null);if(l===null)throw new m(407,!1);let u=a.get(iD,null),c=a.get(En,null),d={rendererFactory:l,sanitizer:u,changeDetectionScheduler:c},h=l.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",p=r?Jy(h,r,this.componentDef.encapsulation,a):yf(h,f,sD(f)),g=512;this.componentDef.signals?g|=4096:this.componentDef.onPush||(g|=16);let E=null;p!==null&&(E=ka(p,a,!0));let w=$a(0,null,null,1,0,null,null,null,null,null,null),P=qo(null,w,null,g,null,null,d,h,a,null,E);P[De]=p,Ta(P);let H,L=null;try{let z=this.componentDef,q,ee=null;z.findHostDirectiveDefs?(q=[],ee=new Map,z.findHostDirectiveDefs(z,q,ee),q.push(z)):q=[z];let je=r?["ng-version","19.1.3"]:aD(this.componentDef.selectors[0]),re=Xn(w,De,2,"#host",je);for(let sr of q)re.mergedAttrs=Dn(re.mergedAttrs,sr.hostAttrs);re.mergedAttrs=Dn(re.mergedAttrs,je),_o(re,re.mergedAttrs,!0),p&&Df(h,p,re),L=lD(re,p,z,q,P,d),n!==void 0&&cD(re,this.ngContentSelectors,n),H=uD(L,z,q,ee,P,[dD]),qa(w,P,null)}catch(z){throw L!==null&&As(L),As(P),z}finally{Ma()}let xe=fd(w,De);return new Zs(this.componentType,H,Sn(xe,P),P,xe)}finally{R(i)}}},Zs=class extends Ws{location;_rootLView;_tNode;instance;hostView;changeDetectorRef;componentType;previousInputValues=null;constructor(e,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.instance=n,this.hostView=this.changeDetectorRef=new qt(o,void 0,!1),this.componentType=e}setInput(e,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[e])){if(this.previousInputValues??=new Map,this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),n))return;let i=this._rootLView;za(i[T],i,o,e,n),this.previousInputValues.set(e,n);let s=_t(this._tNode.index,i);Za(s,1)}}get injector(){return new Lt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function lD(t,e,n,r,o,i){let s=o[T],a=null;e!==null&&(a=ka(e,o[mn]));let l=i.rendererFactory.createRenderer(e,n),u=qo(o,_f(n),null,Cf(n),o[t.index],t,i,l,null,null,a);return s.firstCreatePass&&Us(s,t,r.length-1),Go(o,u),o[t.index]=u}function uD(t,e,n,r,o,i){let s=ne(),a=o[T],l=Se(s,o);If(a,o,s,n,null,r);for(let c=0;c<n.length;c++){let d=s.directiveStart+c,h=zt(o,a,d,s);Dt(h,o)}bf(a,o,s),l&&Dt(l,o);let u=zt(o,a,s.directiveStart+s.componentOffset,s);if(t[ke]=o[ke]=u,i!==null)for(let c of i)c(u,e);return Pa(a,s,o),u}function cD(t,e,n){let r=t.projection=[];for(let o=0;o<e.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}function dD(){let t=ne();$o(A()[T],t)}var Tn=(()=>{class t{static __NG_ELEMENT_ID__=fD}return t})();function fD(){let t=ne();return Bf(t,A())}var pD=Tn,jf=class extends pD{_lContainer;_hostTNode;_hostLView;constructor(e,n,r){super(),this._lContainer=e,this._hostTNode=n,this._hostLView=r}get element(){return Sn(this._hostTNode,this._hostLView)}get injector(){return new Lt(this._hostTNode,this._hostLView)}get parentInjector(){let e=Na(this._hostTNode,this._hostLView);if(Rd(e)){let n=fo(e,this._hostLView),r=co(e),o=n[T].data[r+8];return new Lt(o,n)}else return new Lt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let n=Ec(this._lContainer);return n!==null&&n[e]||null}get length(){return this._lContainer.length-Ee}createEmbeddedView(e,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=vc(this._lContainer,e.ssrId),a=e.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,yc(this._hostTNode,s)),a}createComponent(e,n,r,o,i){let s=e&&!$g(e),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let l=s?e:new Wt(Xe(e)),u=r||this.parentInjector;if(!i&&l.ngModule==null){let g=(s?u:this.parentInjector).get(mt,null);g&&(i=g)}let c=Xe(l.componentType??{}),d=vc(this._lContainer,c?.id??null),h=d?.firstChild??null,f=l.create(u,o,h,i);return this.insertImpl(f.hostView,a,yc(this._hostTNode,d)),f}insert(e,n){return this.insertImpl(e,n,!0)}insertImpl(e,n,r){let o=e._lView;if(Qg(o)){let a=this.indexOf(e);if(a!==-1)this.detach(a);else{let l=o[ie],u=new jf(l,l[Pe],l[ie]);u.detach(u.indexOf(e))}}let i=this._adjustIndex(n),s=this._lContainer;return Lv(s,o,i,r),e.attachToViewContainerRef(),zc(os(s),i,e),e}move(e,n){return this.insert(e,n)}indexOf(e){let n=Ec(this._lContainer);return n!==null?n.indexOf(e):-1}remove(e){let n=this._adjustIndex(e,-1),r=zs(this._lContainer,n);r&&(ro(os(this._lContainer),n),xf(r[T],r))}detach(e){let n=this._adjustIndex(e,-1),r=zs(this._lContainer,n);return r&&ro(os(this._lContainer),n)!=null?new qt(r):null}_adjustIndex(e,n=0){return e??this.length+n}};function Ec(t){return t[uo]}function os(t){return t[uo]||(t[uo]=[])}function Bf(t,e){let n,r=e[t.index];return ot(r)?n=r:(n=Sf(r,e,null,t),e[t.index]=n,Go(e,n)),gD(n,e,t,r),new jf(n,t,e)}function hD(t,e){let n=t[W],r=n.createComment(""),o=Se(e,t),i=n.parentNode(o);return yo(n,i,r,n.nextSibling(o),!1),r}var gD=vD,mD=()=>!1;function yD(t,e,n){return mD(t,e,n)}function vD(t,e,n,r){if(t[Bt])return;let o;n.type&8?o=We(r):o=hD(e,n),t[Bt]=o}var Ks=class t{queryList;matches=null;constructor(e){this.queryList=e}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},Ys=class t{queries;constructor(e=[]){this.queries=e}createEmbeddedView(e){let n=e.queries;if(n!==null){let r=e.contentQueries!==null?e.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new t(o)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let n=0;n<this.queries.length;n++)Ya(e,n).matches!==null&&this.queries[n].setDirty()}},bo=class{flags;read;predicate;constructor(e,n,r=null){this.flags=n,this.read=r,typeof e=="string"?this.predicate=SD(e):this.predicate=e}},Js=class t{queries;constructor(e=[]){this.queries=e}elementStart(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(e,n)}elementEnd(e){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(e)}embeddedTView(e){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(e,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new t(n):null}template(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(e,n)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},Xs=class t{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(e,n=-1){this.metadata=e,this._declarationNodeIndex=n}elementStart(e,n){this.isApplyingToNode(n)&&this.matchTNode(e,n)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,n){this.elementStart(e,n)}embeddedTView(e,n){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,n),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=e.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(e,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(e,n,DD(n,i)),this.matchTNodeWithReadOption(e,n,Jr(n,e,i,!1,!1))}else r===Gt?n.type&4&&this.matchTNodeWithReadOption(e,n,-1):this.matchTNodeWithReadOption(e,n,Jr(n,e,r,!1,!1))}matchTNodeWithReadOption(e,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Yt||o===Tn||o===Gt&&n.type&4)this.addMatch(n.index,-2);else{let i=Jr(n,e,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(e,n){this.matches===null?this.matches=[e,n]:this.matches.push(e,n)}};function DD(t,e){let n=t.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===e)return n[r+1]}return null}function ED(t,e){return t.type&11?Sn(t,e):t.type&4?Zo(t,e):null}function wD(t,e,n,r){return n===-1?ED(e,t):n===-2?_D(t,e,r):zt(t,t[T],n,e)}function _D(t,e,n){if(n===Yt)return Sn(e,t);if(n===Gt)return Zo(e,t);if(n===Tn)return Bf(e,t)}function $f(t,e,n,r){let o=e[tt].queries[r];if(o.matches===null){let i=t.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let u=s[l];if(u<0)a.push(null);else{let c=i[u];a.push(wD(e,c,s[l+1],n.metadata.read))}}o.matches=a}return o.matches}function ea(t,e,n,r){let o=t.queries.getByIndex(n),i=o.matches;if(i!==null){let s=$f(t,e,o,n);for(let a=0;a<i.length;a+=2){let l=i[a];if(l>0)r.push(s[a/2]);else{let u=i[a+1],c=e[-l];for(let d=Ee;d<c.length;d++){let h=c[d];h[jt]===h[ie]&&ea(h[T],h,u,r)}if(c[yn]!==null){let d=c[yn];for(let h=0;h<d.length;h++){let f=d[h];ea(f[T],f,u,r)}}}}}return r}function ID(t,e){return t[tt].queries[e].queryList}function Hf(t,e,n){let r=new xs((n&4)===4);return Yg(t,e,r,r.destroy),(e[tt]??=new Ys).queries.push(new Ks(r))-1}function bD(t,e,n){let r=Q();return r.firstCreatePass&&(Uf(r,new bo(t,e,n),-1),(e&2)===2&&(r.staticViewQueries=!0)),Hf(r,A(),e)}function CD(t,e,n,r){let o=Q();if(o.firstCreatePass){let i=ne();Uf(o,new bo(e,n,r),i.index),TD(o,t),(n&2)===2&&(o.staticContentQueries=!0)}return Hf(o,A(),n)}function SD(t){return t.split(",").map(e=>e.trim())}function Uf(t,e,n){t.queries===null&&(t.queries=new Js),t.queries.track(new Xs(e,n))}function TD(t,e){let n=t.contentQueries||(t.contentQueries=[]),r=n.length?n[n.length-1]:-1;e!==r&&n.push(t.queries.length-1,e)}function Ya(t,e){return t.queries.getByIndex(e)}function MD(t,e){let n=t[T],r=Ya(n,e);return r.crossesNgTemplate?ea(n,t,e,[]):$f(n,t,r,e)}var wt=class{},ta=class{};var na=class extends wt{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Io(this);constructor(e,n,r,o=!0){super(),this.ngModuleType=e,this._parent=n;let i=Wc(e);this._bootstrapComponents=pf(i.bootstrap),this._r3Injector=$d(e,n,[{provide:wt,useValue:this},{provide:_n,useValue:this.componentFactoryResolver},...r],le(e),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},ra=class extends ta{moduleType;constructor(e){super(),this.moduleType=e}create(e){return new na(this.moduleType,e,[])}};var Co=class extends wt{injector;componentFactoryResolver=new Io(this);instance=null;constructor(e){super();let n=new Un([...e.providers,{provide:wt,useValue:this},{provide:_n,useValue:this.componentFactoryResolver}],e.parent||Ro(),e.debugName,new Set(["environment"]));this.injector=n,e.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function ND(t,e,n=null){return new Co({providers:t,parent:e,debugName:n,runEnvironmentInitializers:!0}).injector}var xD=(()=>{class t{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Kc(!1,n.type),o=r.length>0?ND([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=V({token:t,providedIn:"environment",factory:()=>new t(Y(mt))})}return t})();function GN(t){return Wn(()=>{let e=qf(t),n=$e(fe({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===Zd.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:e.standalone?o=>o.get(xD).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:t.signals??!1,data:t.data||{},encapsulation:t.encapsulation||qn.Emulated,styles:t.styles||ge,_:null,schemas:t.schemas||null,tView:null,id:""});e.standalone&&Jt("NgStandalone"),Gf(n);let r=t.dependencies;return n.directiveDefs=_c(r,!1),n.pipeDefs=_c(r,!0),n.id=OD(n),n})}function AD(t){return Xe(t)||Qc(t)}function RD(t){return t!==null}function Ja(t){return Wn(()=>({type:t.type,bootstrap:t.bootstrap||ge,declarations:t.declarations||ge,imports:t.imports||ge,exports:t.exports||ge,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function wc(t,e){if(t==null)return hn;let n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r],i,s,a=Et.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),e?(n[i]=a!==Et.None?[r,a]:r,e[i]=s):n[i]=r}return n}function Ko(t){return Wn(()=>{let e=qf(t);return Gf(e),e})}function zf(t){return{type:t.type,name:t.name,factory:null,pure:t.pure!==!1,standalone:t.standalone??!0,onDestroy:t.type.prototype.ngOnDestroy||null}}function qf(t){let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputTransforms:null,inputConfig:t.inputs||hn,exportAs:t.exportAs||null,standalone:t.standalone??!0,signals:t.signals===!0,selectors:t.selectors||ge,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:wc(t.inputs,e),outputs:wc(t.outputs),debugInfo:null}}function Gf(t){t.features?.forEach(e=>e(t))}function _c(t,e){if(!t)return null;let n=e?Zc:AD;return()=>(typeof t=="function"?t():t).map(r=>n(r)).filter(RD)}function OD(t){let e=0,n=typeof t.consts=="function"?"":t.consts,r=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,n,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery];for(let i of r.join("|"))e=Math.imul(31,e)+i.charCodeAt(0)<<0;return e+=2147483648,"c"+e}function FD(t){return Object.getPrototypeOf(t.prototype).constructor}function kD(t){let e=FD(t.type),n=!0,r=[t];for(;e;){let o;if(yt(t))o=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new m(903,!1);o=e.\u0275dir}if(o){if(n){r.push(o);let s=t;s.inputs=Wr(t.inputs),s.inputTransforms=Wr(t.inputTransforms),s.declaredInputs=Wr(t.declaredInputs),s.outputs=Wr(t.outputs);let a=o.hostBindings;a&&BD(t,a);let l=o.viewQuery,u=o.contentQueries;if(l&&VD(t,l),u&&jD(t,u),PD(t,o),rg(t.outputs,o.outputs),yt(o)&&o.data.animation){let c=t.data;c.animation=(c.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(t),a===kD&&(n=!1)}}e=Object.getPrototypeOf(e)}LD(r)}function PD(t,e){for(let n in e.inputs){if(!e.inputs.hasOwnProperty(n)||t.inputs.hasOwnProperty(n))continue;let r=e.inputs[n];if(r!==void 0&&(t.inputs[n]=r,t.declaredInputs[n]=e.declaredInputs[n],e.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!e.inputTransforms.hasOwnProperty(o))continue;t.inputTransforms??={},t.inputTransforms[o]=e.inputTransforms[o]}}}function LD(t){let e=0,n=null;for(let r=t.length-1;r>=0;r--){let o=t[r];o.hostVars=e+=o.hostVars,o.hostAttrs=Dn(o.hostAttrs,n=Dn(n,o.hostAttrs))}}function Wr(t){return t===hn?{}:t===ge?[]:t}function VD(t,e){let n=t.viewQuery;n?t.viewQuery=(r,o)=>{e(r,o),n(r,o)}:t.viewQuery=e}function jD(t,e){let n=t.contentQueries;n?t.contentQueries=(r,o,i)=>{e(r,o,i),n(r,o,i)}:t.contentQueries=e}function BD(t,e){let n=t.hostBindings;n?t.hostBindings=(r,o)=>{e(r,o),n(r,o)}:t.hostBindings=e}function $D(t){let e=t.inputConfig,n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}t.inputTransforms=n}function Wf(t){return UD(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function HD(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{let n=t[Symbol.iterator](),r;for(;!(r=n.next()).done;)e(r.value)}}function UD(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function Xa(t,e,n){return t[e]=n}function zD(t,e){return t[e]}function Qe(t,e,n){let r=t[e];return Object.is(r,n)?!1:(t[e]=n,!0)}function qD(t,e,n,r){let o=Qe(t,e,n);return Qe(t,e+1,r)||o}function GD(t){return(t.flags&32)===32}function WD(t,e,n,r,o,i,s,a,l){let u=e.consts,c=Xn(e,t,4,s||null,a||null);Ua(e,n,c,vn(u,l)),$o(e,c);let d=c.tView=$a(2,c,r,o,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,u,null);return e.queries!==null&&(e.queries.template(e,c),d.queries=e.queries.embeddedTView(c)),c}function QD(t,e,n,r,o,i,s,a,l,u){let c=n+De,d=e.firstCreatePass?WD(c,e,t,r,o,i,s,a,l):e.data[c];Zt(d,!1);let h=KD(e,t,d,n);jo()&&Wo(e,t,h,d),Dt(h,t);let f=Sf(h,t,h,d);return t[c]=f,Go(t,f),yD(f,d,t),ko(d)&&ja(e,t,d),l!=null&&Ba(t,d,u),d}function ZD(t,e,n,r,o,i,s,a){let l=A(),u=Q(),c=vn(u.consts,i);return QD(l,u,t,e,n,r,o,c,s,a),ZD}var KD=YD;function YD(t,e,n,r){return Bo(!0),e[W].createComment("")}var WN=(()=>{class t{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"platform"})}return t})();var JD=new O(""),XD=new O(""),QN=(()=>{class t{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(n,r,o){this._ngZone=n,this.registry=r,el||(tE(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{ue.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||t)(Y(ue),Y(eE),Y(XD))};static \u0275prov=V({token:t,factory:t.\u0275fac})}return t})(),eE=(()=>{class t{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return el?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"platform"})}return t})();function tE(t){el=t}var el;function tl(t){return!!t&&typeof t.then=="function"}function Qf(t){return!!t&&typeof t.subscribe=="function"}var nE=new O("");var Zf=(()=>{class t{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=S(nE,{optional:!0})??[];injector=S(vt);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=td(this.injector,o);if(tl(i))n.push(i);else if(Qf(i)){let s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),rE=(()=>{class t{static \u0275prov=V({token:t,providedIn:"root",factory:()=>new oa})}return t})(),oa=class{queuedEffectCount=0;queues=new Map;schedule(e){this.enqueue(e)}remove(e){let n=e.zone,r=this.queues.get(n);r.has(e)&&(r.delete(e),this.queuedEffectCount--)}enqueue(e){let n=e.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(e)||(this.queuedEffectCount++,r.add(e))}flush(){for(;this.queuedEffectCount>0;)for(let[e,n]of this.queues)e===null?this.flushQueue(n):e.run(()=>this.flushQueue(n))}flushQueue(e){for(let n of e)e.delete(n),this.queuedEffectCount--,n.run()}},oE=new O("");function iE(){du(()=>{throw new m(600,!1)})}function sE(t){return t.isBoundToModule}var aE=10;function lE(t,e,n){try{let r=n();return tl(r)?r.catch(o=>{throw e.runOutsideAngular(()=>t.handleError(o)),o}):r}catch(r){throw e.runOutsideAngular(()=>t.handleError(r)),r}}var In=(()=>{class t{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=S(jm);afterRenderManager=S(tf);zonelessEnabled=S(Aa);rootEffectScheduler=S(rE);dirtyFlags=0;deferredDirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new He;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=S(Ho).hasPendingTasks.pipe(Ot(n=>!n));constructor(){S(Uo,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=S(mt);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Eo;if(!this._injector.get(Zf).done){let h=!o&&Mg(n),f=!1;throw new m(405,f)}let s;o?s=n:s=this._injector.get(_n).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=sE(s)?void 0:this._injector.get(wt),l=r||s.selector,u=s.create(vt.NULL,[],l,a),c=u.location.nativeElement,d=u.injector.get(JD,null);return d?.registerApplication(c),u.onDestroy(()=>{this.detachView(u.hostView),Xr(this.components,u),d?.unregisterApplication(c)}),this._loadComponent(u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick=()=>{if(this.tracingSnapshot!==null){let r=this.tracingSnapshot;this.tracingSnapshot=null,r.run(Fa.CHANGE_DETECTION,this._tick),r.dispose();return}if(this._runningTick)throw new m(101,!1);let n=R(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,R(n),this.afterTick.next()}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(wo,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let n=0;for(;this.dirtyFlags!==0&&n++<aE;)this.synchronizeOnce()}synchronizeOnce(){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)uE(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Po(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Xr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(oE,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Xr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new m(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Xr(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function uE(t,e,n,r){if(!n&&!Po(t))return;kf(t,e,n&&!r?0:1)}function cE(t,e,n,r){let o=A(),i=Kn();if(Qe(o,i,e)){let s=Q(),a=Vo();hv(a,o,t,e,n,r)}return cE}function dE(t,e,n,r){return Qe(t,Kn(),n)?e+xo(n)+r:Ze}function Qr(t,e){return t<<17|e<<2}function Qt(t){return t>>17&32767}function fE(t){return(t&2)==2}function pE(t,e){return t&131071|e<<17}function ia(t){return t|2}function bn(t){return(t&131068)>>2}function is(t,e){return t&-131069|e<<2}function hE(t){return(t&1)===1}function sa(t){return t|1}function gE(t,e,n,r,o,i){let s=i?e.classBindings:e.styleBindings,a=Qt(s),l=bn(s);t[r]=n;let u=!1,c;if(Array.isArray(n)){let d=n;c=d[1],(c===null||Qn(d,c)>0)&&(u=!0)}else c=n;if(o)if(l!==0){let h=Qt(t[a+1]);t[r+1]=Qr(h,a),h!==0&&(t[h+1]=is(t[h+1],r)),t[a+1]=pE(t[a+1],r)}else t[r+1]=Qr(a,0),a!==0&&(t[a+1]=is(t[a+1],r)),a=r;else t[r+1]=Qr(l,0),a===0?a=r:t[l+1]=is(t[l+1],r),l=r;u&&(t[r+1]=ia(t[r+1])),Ic(t,c,r,!0),Ic(t,c,r,!1),mE(e,c,t,r,i),s=Qr(a,l),i?e.classBindings=s:e.styleBindings=s}function mE(t,e,n,r,o){let i=o?t.residualClasses:t.residualStyles;i!=null&&typeof e=="string"&&Qn(i,e)>=0&&(n[r+1]=sa(n[r+1]))}function Ic(t,e,n,r){let o=t[n+1],i=e===null,s=r?Qt(o):bn(o),a=!1;for(;s!==0&&(a===!1||i);){let l=t[s],u=t[s+1];yE(l,e)&&(a=!0,t[s+1]=r?sa(u):ia(u)),s=r?Qt(u):bn(u)}a&&(t[n+1]=r?ia(o):sa(o))}function yE(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?Qn(t,e)>=0:!1}var Oe={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function vE(t){return t.substring(Oe.key,Oe.keyEnd)}function DE(t){return EE(t),Kf(t,Yf(t,0,Oe.textEnd))}function Kf(t,e){let n=Oe.textEnd;return n===e?-1:(e=Oe.keyEnd=wE(t,Oe.key=e,n),Yf(t,e,n))}function EE(t){Oe.key=0,Oe.keyEnd=0,Oe.value=0,Oe.valueEnd=0,Oe.textEnd=t.length}function Yf(t,e,n){for(;e<n&&t.charCodeAt(e)<=32;)e++;return e}function wE(t,e,n){for(;e<n&&t.charCodeAt(e)>32;)e++;return e}function _E(t,e,n){let r=A(),o=Kn();if(Qe(r,o,e)){let i=Q(),s=Vo();Ha(i,s,r,t,e,r[W],n,!1)}return _E}function aa(t,e,n,r,o){let i=e.inputs,s=o?"class":"style";za(t,n,i[s],s,r)}function IE(t,e){return CE(t,e,null,!0),IE}function ZN(t){SE(RE,bE,t,!0)}function bE(t,e){for(let n=DE(e);n>=0;n=Kf(e,n))Da(t,vE(e),!0)}function CE(t,e,n,r){let o=A(),i=Q(),s=_d(2);if(i.firstUpdatePass&&Xf(i,t,s,r),e!==Ze&&Qe(o,s,e)){let a=i.data[Kt()];ep(i,a,o,o[W],t,o[s+1]=FE(e,n),r,s)}}function SE(t,e,n,r){let o=Q(),i=_d(2);o.firstUpdatePass&&Xf(o,null,i,r);let s=A();if(n!==Ze&&Qe(s,i,n)){let a=o.data[Kt()];if(tp(a,r)&&!Jf(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;l!==null&&(n=cs(l,n||"")),aa(o,a,s,n,r)}else OE(o,a,s,s[W],s[i+1],s[i+1]=AE(t,e,n),r,i)}}function Jf(t,e){return e>=t.expandoStartIndex}function Xf(t,e,n,r){let o=t.data;if(o[n+1]===null){let i=o[Kt()],s=Jf(t,n);tp(i,r)&&e===null&&!s&&(e=!1),e=TE(o,i,e,r),gE(o,i,e,n,s,r)}}function TE(t,e,n,r){let o=cm(t),i=r?e.residualClasses:e.residualStyles;if(o===null)(r?e.classBindings:e.styleBindings)===0&&(n=ss(null,t,e,n,r),n=Gn(n,e.attrs,r),i=null);else{let s=e.directiveStylingLast;if(s===-1||t[s]!==o)if(n=ss(o,t,e,n,r),i===null){let l=ME(t,e,r);l!==void 0&&Array.isArray(l)&&(l=ss(null,t,e,l[1],r),l=Gn(l,e.attrs,r),NE(t,e,r,l))}else i=xE(t,e,r)}return i!==void 0&&(r?e.residualClasses=i:e.residualStyles=i),n}function ME(t,e,n){let r=n?e.classBindings:e.styleBindings;if(bn(r)!==0)return t[Qt(r)]}function NE(t,e,n,r){let o=n?e.classBindings:e.styleBindings;t[Qt(o)]=r}function xE(t,e,n){let r,o=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<o;i++){let s=t[i].hostAttrs;r=Gn(r,s,n)}return Gn(r,e.attrs,n)}function ss(t,e,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=e[a],r=Gn(r,i.hostAttrs,o),i!==t);)a++;return t!==null&&(n.directiveStylingLast=a),r}function Gn(t,e,n){let r=n?1:2,o=-1;if(e!==null)for(let i=0;i<e.length;i++){let s=e[i];typeof s=="number"?o=s:o===r&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),Da(t,s,n?!0:e[++i]))}return t===void 0?null:t}function AE(t,e,n){if(n==null||n==="")return ge;let r=[],o=Yn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)t(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&t(r,i,o[i]);else typeof o=="string"&&e(r,o);return r}function RE(t,e,n){let r=String(e);r!==""&&!r.includes(" ")&&Da(t,r,n)}function OE(t,e,n,r,o,i,s,a){o===Ze&&(o=ge);let l=0,u=0,c=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;c!==null||d!==null;){let h=l<o.length?o[l+1]:void 0,f=u<i.length?i[u+1]:void 0,p=null,g;c===d?(l+=2,u+=2,h!==f&&(p=d,g=f)):d===null||c!==null&&c<d?(l+=2,p=c):(u+=2,p=d,g=f),p!==null&&ep(t,e,n,r,p,g,s,a),c=l<o.length?o[l]:null,d=u<i.length?i[u]:null}}function ep(t,e,n,r,o,i,s,a){if(!(e.type&3))return;let l=t.data,u=l[a+1],c=hE(u)?bc(l,e,n,o,bn(u),s):void 0;if(!So(c)){So(i)||fE(u)&&(i=bc(l,null,n,o,a,s));let d=dd(Kt(),n);kv(r,s,d,o,i)}}function bc(t,e,n,r,o,i){let s=e===null,a;for(;o>0;){let l=t[o],u=Array.isArray(l),c=u?l[1]:l,d=c===null,h=n[o+1];h===Ze&&(h=d?ge:void 0);let f=d?Yi(h,r):c===r?h:void 0;if(u&&!So(f)&&(f=Yi(l,r)),So(f)&&(a=f,s))return a;let p=t[o+1];o=s?Qt(p):bn(p)}if(e!==null){let l=i?e.residualClasses:e.residualStyles;l!=null&&(a=Yi(l,r))}return a}function So(t){return t!==void 0}function FE(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=le(Yn(t)))),t}function tp(t,e){return(t.flags&(e?8:16))!==0}function kE(t,e,n,r,o,i){let s=e.consts,a=vn(s,o),l=Xn(e,t,2,r,a);return Ua(e,n,l,vn(s,i)),l.attrs!==null&&_o(l,l.attrs,!1),l.mergedAttrs!==null&&_o(l,l.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,l),l}function np(t,e,n,r){let o=A(),i=Q(),s=De+t,a=o[W],l=i.firstCreatePass?kE(s,i,o,e,n,r):i.data[s],u=LE(i,o,l,a,e,t);o[s]=u;let c=ko(l);return Zt(l,!0),Df(a,u,l),!GD(l)&&jo()&&Wo(i,o,u,l),Jg()===0&&Dt(u,o),Xg(),c&&(ja(i,o,l),Pa(i,l,o)),r!==null&&Ba(o,l),np}function rp(){let t=ne();ba()?Ed():(t=t.parent,Zt(t,!1));let e=t;nm(e)&&rm(),em();let n=Q();return n.firstCreatePass&&($o(n,t),wa(t)&&n.queries.elementEnd(t)),e.classesWithoutHost!=null&&ym(e)&&aa(n,e,A(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&vm(e)&&aa(n,e,A(),e.stylesWithoutHost,!1),rp}function PE(t,e,n,r){return np(t,e,n,r),rp(),PE}var LE=(t,e,n,r,o,i)=>(Bo(!0),yf(r,o,pm()));function VE(t,e,n,r,o){let i=e.consts,s=vn(i,r),a=Xn(e,t,8,"ng-container",s);s!==null&&_o(a,s,!0);let l=vn(i,o);return Ua(e,n,a,l),e.queries!==null&&e.queries.elementStart(e,a),a}function jE(t,e,n){let r=A(),o=Q(),i=t+De,s=o.firstCreatePass?VE(i,o,r,e,n):o.data[i];Zt(s,!0);let a=$E(o,r,s,t);return r[i]=a,jo()&&Wo(o,r,a,s),Dt(a,r),ko(s)&&(ja(o,r,s),Pa(o,s,r)),n!=null&&Ba(r,s),jE}function BE(){let t=ne(),e=Q();return ba()?Ed():(t=t.parent,Zt(t,!1)),e.firstCreatePass&&($o(e,t),wa(t)&&e.queries.elementEnd(t)),BE}var $E=(t,e,n,r)=>(Bo(!0),Wy(e[W],""));function KN(){return A()}function HE(t,e,n){let r=A(),o=Kn();if(Qe(r,o,e)){let i=Q(),s=Vo();Ha(i,s,r,t,e,r[W],n,!0)}return HE}var Pt=void 0;function UE(t){let e=t,n=Math.floor(Math.abs(t)),r=t.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var zE=["en",[["a","p"],["AM","PM"],Pt],[["AM","PM"],Pt,Pt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Pt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Pt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Pt,"{1} 'at' {0}",Pt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",UE],as={};function Te(t){let e=qE(t),n=Cc(e);if(n)return n;let r=e.split("-")[0];if(n=Cc(r),n)return n;if(r==="en")return zE;throw new m(701,!1)}function Cc(t){return t in as||(as[t]=ft.ng&&ft.ng.common&&ft.ng.common.locales&&ft.ng.common.locales[t]),as[t]}var Z=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}(Z||{});function qE(t){return t.toLowerCase().replace(/_/g,"-")}var To="en-US";var GE=To;function WE(t){typeof t=="string"&&(GE=t.toLowerCase().replace(/_/g,"-"))}var QE=(t,e,n)=>{};function ZE(t,e,n,r){let o=A(),i=Q(),s=ne();return op(i,o,o[W],s,t,e,r),ZE}function KE(t,e,n,r){let o=t.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=e[ao],l=o[i+2];return a.length>l?a[l]:null}typeof s=="string"&&(i+=2)}return null}function op(t,e,n,r,o,i,s){let a=ko(r),u=t.firstCreatePass&&yd(t),c=e[ke],d=md(e),h=!0;if(r.type&3||s){let g=Se(r,e),E=s?s(g):g,w=d.length,P=s?L=>s(We(L[r.index])):r.index,H=null;if(!s&&a&&(H=KE(t,e,o,r.index)),H!==null){let L=H.__ngLastListenerFn__||H;L.__ngNextListenerFn__=i,H.__ngLastListenerFn__=i,h=!1}else{i=Tc(r,e,c,i),QE(g,o,i);let L=n.listen(E,o,i);d.push(i,L),u&&u.push(o,P,w,w+1)}}else i=Tc(r,e,c,i);let f=r.outputs,p;if(h&&f!==null&&(p=f[o])){let g=p.length;if(g)for(let E=0;E<g;E+=2){let w=p[E],P=p[E+1],xe=e[w][P].subscribe(i),z=d.length;d.push(i,xe),u&&u.push(o,r.index,z,-(z+1))}}}function Sc(t,e,n,r){let o=R(null);try{return Ce(6,e,n),n(r)!==!1}catch(i){return Tf(t,i),!1}finally{Ce(7,e,n),R(o)}}function Tc(t,e,n,r){return function o(i){if(i===Function)return r;let s=t.componentOffset>-1?_t(t.index,e):e;Za(s,5);let a=Sc(e,n,r,i),l=o.__ngNextListenerFn__;for(;l;)a=Sc(e,n,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function YN(t=1){return fm(t)}function JN(t,e,n,r){CD(t,e,n,r)}function XN(t,e,n){bD(t,e,n)}function ex(t){let e=A(),n=Q(),r=Id();Sa(r+1);let o=Ya(n,r);if(t.dirty&&Wg(e)===((o.metadata.flags&2)===2)){if(o.matches===null)t.reset([]);else{let i=MD(e,r);t.reset(i,Hm),t.notifyOnChanges()}return!0}return!1}function tx(){return ID(A(),Id())}function YE(t,e,n,r){n>=t.data.length&&(t.data[n]=null,t.blueprint[n]=null),e[n]=r}function nx(t){let e=im();return pd(e,De+t)}function rx(t,e=""){let n=A(),r=Q(),o=t+De,i=r.firstCreatePass?Xn(r,o,1,e,null):r.data[o],s=JE(r,n,i,e,t);n[o]=s,jo()&&Wo(r,n,s,i),Zt(i,!1)}var JE=(t,e,n,r,o)=>(Bo(!0),qy(e[W],r));function XE(t){return ip("",t,""),XE}function ip(t,e,n){let r=A(),o=dE(r,t,e,n);return o!==Ze&&ew(r,Kt(),o),ip}function ew(t,e,n){let r=dd(e,t);Gy(t[W],r,n)}function tw(t,e,n){Wd(e)&&(e=e());let r=A(),o=Kn();if(Qe(r,o,e)){let i=Q(),s=Vo();Ha(i,s,r,t,e,r[W],n,!1)}return tw}function ox(t,e){let n=Wd(t);return n&&t.set(e),n}function nw(t,e){let n=A(),r=Q(),o=ne();return op(r,n,n[W],o,t,e),nw}function rw(t,e,n){let r=Q();if(r.firstCreatePass){let o=yt(t);la(n,r.data,r.blueprint,o,!0),la(e,r.data,r.blueprint,o,!1)}}function la(t,e,n,r,o){if(t=ae(t),Array.isArray(t))for(let i=0;i<t.length;i++)la(t[i],e,n,r,o);else{let i=Q(),s=A(),a=ne(),l=gn(t)?t:ae(t.provide),u=ed(t),c=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(gn(t)||!t.multi){let f=new Ut(u,o,ce),p=us(l,e,o?c:c+h,d);p===-1?(bs(ho(a,s),i,l),ls(i,t,e.length),e.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=us(l,e,c+h,d),p=us(l,e,c,c+h),g=f>=0&&n[f],E=p>=0&&n[p];if(o&&!E||!o&&!g){bs(ho(a,s),i,l);let w=sw(o?iw:ow,n.length,o,r,u);!o&&E&&(n[p].providerFactory=w),ls(i,t,e.length,0),e.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(w),s.push(w)}else{let w=sp(n[o?p:f],u,!o&&r);ls(i,t,f>-1?f:p,w)}!o&&r&&E&&n[p].componentProviders++}}}function ls(t,e,n,r){let o=gn(e),i=Fg(e);if(o||i){let l=(i?ae(e.useClass):e).prototype.ngOnDestroy;if(l){let u=t.destroyHooks||(t.destroyHooks=[]);if(!o&&e.multi){let c=u.indexOf(n);c===-1?u.push(n,[r,l]):u[c+1].push(r,l)}else u.push(n,l)}}}function sp(t,e,n){return n&&t.componentProviders++,t.multi.push(e)-1}function us(t,e,n,r){for(let o=n;o<r;o++)if(e[o]===t)return o;return-1}function ow(t,e,n,r){return ua(this.multi,[])}function iw(t,e,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=zt(n,n[T],this.providerFactory.index,r);i=a.slice(0,s),ua(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],ua(o,i);return i}function ua(t,e){for(let n=0;n<t.length;n++){let r=t[n];e.push(r())}return e}function sw(t,e,n,r,o){let i=new Ut(t,n,ce);return i.multi=[],i.index=e,i.componentProviders=0,sp(i,o,r&&!n),i}function ix(t,e=[]){return n=>{n.providersResolver=(r,o)=>rw(r,o?o(t):t,e)}}function sx(t,e,n){let r=Ca()+t,o=A();return o[r]===Ze?Xa(o,r,n?e.call(n):e()):zD(o,r)}function ax(t,e,n,r){return aw(A(),Ca(),t,e,n,r)}function ap(t,e){let n=t[e];return n===Ze?void 0:n}function aw(t,e,n,r,o,i){let s=e+n;return Qe(t,s,o)?Xa(t,s+1,i?r.call(i,o):r(o)):ap(t,s+1)}function lw(t,e,n,r,o,i,s){let a=e+n;return qD(t,a,o,i)?Xa(t,a+2,s?r.call(s,o,i):r(o,i)):ap(t,a+2)}function lx(t,e){let n=Q(),r,o=t+De;n.firstCreatePass?(r=uw(e,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Vt(r.type,!0)),s,a=he(ce);try{let l=po(!1),u=i();return po(l),YE(n,A(),o,u),u}finally{he(a)}}function uw(t,e){if(e)for(let n=e.length-1;n>=0;n--){let r=e[n];if(t===r.name)return r}}function ux(t,e,n,r){let o=t+De,i=A(),s=pd(i,o);return cw(i,o)?lw(i,Ca(),e,s.transform,n,r,s):s.transform(n,r)}function cw(t,e){return t[T].data[e].pure}function cx(t,e){return Zo(t,e)}var ca=class{ngModuleFactory;componentFactories;constructor(e,n){this.ngModuleFactory=e,this.componentFactories=n}},dx=(()=>{class t{compileModuleSync(n){return new ra(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Wc(n),i=pf(o.declarations).reduce((s,a)=>{let l=Xe(a);return l&&s.push(new Wt(l)),s},[]);return new ca(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var dw=(()=>{class t{zone=S(ue);changeDetectionScheduler=S(En);applicationRef=S(In);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),fw=new O("",{factory:()=>!1});function lp({ngZoneFactory:t,ignoreChangesOutsideZone:e,scheduleInRootZone:n}){return t??=()=>new ue($e(fe({},up()),{scheduleInRootZone:n})),[{provide:ue,useFactory:t},{provide:oo,multi:!0,useFactory:()=>{let r=S(dw,{optional:!0});return()=>r.initialize()}},{provide:oo,multi:!0,useFactory:()=>{let r=S(pw);return()=>{r.initialize()}}},e===!0?{provide:Ud,useValue:!0}:[],{provide:zd,useValue:n??Hd}]}function fx(t){let e=t?.ignoreChangesOutsideZone,n=t?.scheduleInRootZone,r=lp({ngZoneFactory:()=>{let o=up(t);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Jt("NgZone_CoalesceEvent"),new ue(o)},ignoreChangesOutsideZone:e,scheduleInRootZone:n});return Ng([{provide:fw,useValue:!0},{provide:Aa,useValue:!1},r])}function up(t){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:t?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:t?.runCoalescing??!1}}var pw=(()=>{class t{subscription=new K;initialized=!1;zone=S(ue);pendingTasks=S(Ho);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ue.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ue.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var hw=(()=>{class t{appRef=S(In);taskService=S(Ho);ngZone=S(ue);zonelessEnabled=S(Aa);tracing=S(Uo,{optional:!0});disableScheduling=S(Ud,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new K;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(mo):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(S(zd,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ns||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 8:{this.appRef.deferredDirtyFlags|=8;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 13:{this.appRef.dirtyFlags|=16,r=!0;break}case 14:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{r=!0;break}case 10:case 9:case 7:case 11:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?tc:qd;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(mo+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,tc(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function gw(){return typeof $localize<"u"&&$localize.locale||To}var Yo=new O("",{providedIn:"root",factory:()=>S(Yo,x.Optional|x.SkipSelf)||gw()});var da=new O(""),mw=new O("");function Vn(t){return!t.moduleRef}function yw(t){let e=Vn(t)?t.r3Injector:t.moduleRef.injector,n=e.get(ue);return n.run(()=>{Vn(t)?t.r3Injector.resolveInjectorInitializers():t.moduleRef.resolveInjectorInitializers();let r=e.get(wn,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Vn(t)){let i=()=>e.destroy(),s=t.platformInjector.get(da);s.add(i),e.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>t.moduleRef.destroy(),s=t.platformInjector.get(da);s.add(i),t.moduleRef.onDestroy(()=>{Xr(t.allPlatformModules,t.moduleRef),o.unsubscribe(),s.delete(i)})}return lE(r,n,()=>{let i=e.get(Zf);return i.runInitializers(),i.donePromise.then(()=>{let s=e.get(Yo,To);if(WE(s||To),!e.get(mw,!0))return Vn(t)?e.get(In):(t.allPlatformModules.push(t.moduleRef),t.moduleRef);if(Vn(t)){let l=e.get(In);return t.rootComponent!==void 0&&l.bootstrap(t.rootComponent),l}else return vw(t.moduleRef,t.allPlatformModules),t.moduleRef})})})}function vw(t,e){let n=t.injector.get(In);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(t.instance.ngDoBootstrap)t.instance.ngDoBootstrap(n);else throw new m(-403,!1);e.push(t)}var eo=null;function Dw(t=[],e){return vt.create({name:e,providers:[{provide:Xc,useValue:"platform"},{provide:da,useValue:new Set([()=>eo=null])},...t]})}function Ew(t=[]){if(eo)return eo;let e=Dw(t);return eo=e,iE(),ww(e),e}function ww(t){let e=t.get(Ym,null);td(t,()=>{e?.forEach(n=>n())})}var cp=(()=>{class t{static __NG_ELEMENT_ID__=_w}return t})();function _w(t){return Iw(ne(),A(),(t&16)===16)}function Iw(t,e,n){if(Fo(t)&&!n){let r=_t(t.index,e);return new qt(r,r)}else if(t.type&175){let r=e[Ge];return new qt(r,e)}return null}var fa=class{constructor(){}supports(e){return Wf(e)}create(e){return new pa(e)}},bw=(t,e)=>e,pa=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(e){this._trackByFn=e||bw}forEachItem(e){let n;for(n=this._itHead;n!==null;n=n._next)e(n)}forEachOperation(e){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Mc(r,o,i)?n:r,a=Mc(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,c=l-o;if(u!=c){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;c<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=c-u}}a!==l&&e(s,a,l)}}forEachPreviousItem(e){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachMovedItem(e){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}forEachIdentityChange(e){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)e(n)}diff(e){if(e==null&&(e=[]),!Wf(e))throw new m(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(e)){this.length=e.length;for(let a=0;a<this.length;a++)i=e[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,HD(e,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,n,r,o){let i;return e===null?i=this._itTail:(i=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._reinsertAfter(e,i,o)):(e=this._linkedRecords===null?null:this._linkedRecords.get(r,o),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._moveAfter(e,i,o)):e=this._addAfter(new ha(n,r),i,o)),e}_verifyReinsertion(e,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?e=this._reinsertAfter(i,e._prev,o):e.currentIndex!=o&&(e.currentIndex=o,this._addToMoves(e,o)),e}_truncate(e){for(;e!==null;){let n=e._next;this._addToRemovals(this._unlink(e)),e=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let o=e._prevRemoved,i=e._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(e,n,r),this._addToMoves(e,r),e}_moveAfter(e,n,r){return this._unlink(e),this._insertAfter(e,n,r),this._addToMoves(e,r),e}_addAfter(e,n,r){return this._insertAfter(e,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,n,r){let o=n===null?this._itHead:n._next;return e._next=o,e._prev=n,o===null?this._itTail=e:o._prev=e,n===null?this._itHead=e:n._next=e,this._linkedRecords===null&&(this._linkedRecords=new Mo),this._linkedRecords.put(e),e.currentIndex=r,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let n=e._prev,r=e._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,e}_addToMoves(e,n){return e.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Mo),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,n){return e.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},ha=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(e,n){this.item=e,this.trackById=n}},ga=class{_head=null;_tail=null;add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,e))return r;return null}remove(e){let n=e._prevDup,r=e._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Mo=class{map=new Map;put(e){let n=e.trackById,r=this.map.get(n);r||(r=new ga,this.map.set(n,r)),r.add(e)}get(e,n){let r=e,o=this.map.get(r);return o?o.get(e,n):null}remove(e){let n=e.trackById;return this.map.get(n).remove(e)&&this.map.delete(n),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Mc(t,e,n){let r=t.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+e+o}function Nc(){return new nl([new fa])}var nl=(()=>{class t{factories;static \u0275prov=V({token:t,providedIn:"root",factory:Nc});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new t(n)}static extend(n){return{provide:t,useFactory:r=>t.create(n,r||Nc()),deps:[[t,new Ig,new _g]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new m(901,!1)}}return t})();var px=(()=>{class t{constructor(n){}static \u0275fac=function(r){return new(r||t)(Y(In))};static \u0275mod=Ja({type:t});static \u0275inj=ma({})}return t})();function hx(t){try{let{rootComponent:e,appProviders:n,platformProviders:r}=t,o=Ew(r),i=[lp({}),{provide:En,useExisting:hw},...n||[]],s=new Co({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return yw({r3Injector:s.injector,platformInjector:o,rootComponent:e})}catch(e){return Promise.reject(e)}}function Cw(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function gx(t,e){Jt("NgSignals");let n=lu(t);return e?.equal&&(n[ye].equal=e.equal),n}function Sw(t){let e=R(null);try{return t()}finally{R(e)}}var xc=class{[ye];constructor(e){this[ye]=e}destroy(){this[ye].destroy()}};function mx(t,e){let n=Xe(t),r=e.elementInjector||Ro();return new Wt(n).create(r,e.projectableNodes,e.hostElement,e.environmentInjector)}function yx(t){let e=Xe(t);if(!e)return null;let n=new Wt(e);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return e.standalone},get isSignal(){return e.signals}}}var Ep=null;function rl(){return Ep}function zx(t){Ep??=t}var fp=class{};var dl=new O(""),fl=(()=>{class t{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:()=>S(Tw),providedIn:"platform"})}return t})(),qx=new O(""),Tw=(()=>{class t extends fl{_location;_history;_doc=S(dl);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return rl().getBaseHref(this._doc)}onPopState(n){let r=rl().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=rl().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:()=>new t,providedIn:"platform"})}return t})();function pl(t,e){if(t.length==0)return e;if(e.length==0)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,n==2?t+e.substring(1):n==1?t+e:t+"/"+e}function pp(t){let e=t.match(/#|\?|$/),n=e&&e.index||t.length,r=n-(t[n-1]==="/"?1:0);return t.slice(0,r)+t.slice(n)}function at(t){return t&&t[0]!=="?"?"?"+t:t}var ai=(()=>{class t{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:()=>S(Mw),providedIn:"root"})}return t})(),wp=new O(""),Mw=(()=>{class t extends ai{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??S(dl).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return pl(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+at(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+at(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+at(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||t)(Y(fl),Y(wp,8))};static \u0275prov=V({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Gx=(()=>{class t extends ai{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=pl(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+at(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+at(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||t)(Y(fl),Y(wp,8))};static \u0275prov=V({token:t,factory:t.\u0275fac})}return t})(),Nw=(()=>{class t{_subject=new He;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Rw(pp(hp(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+at(r))}normalize(n){return t.stripTrailingSlash(Aw(this._basePath,hp(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+at(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+at(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=at;static joinWithSlash=pl;static stripTrailingSlash=pp;static \u0275fac=function(r){return new(r||t)(Y(ai))};static \u0275prov=V({token:t,factory:()=>xw(),providedIn:"root"})}return t})();function xw(){return new Nw(Y(ai))}function Aw(t,e){if(!t||!e.startsWith(t))return e;let n=e.substring(t.length);return n===""||["/",";","?","#"].includes(n[0])?n:e}function hp(t){return t.replace(/\/index.html$/,"")}function Rw(t){if(new RegExp("^(https?:)?//").test(t)){let[,n]=t.split(/\/\/[^\/]+/);return n}return t}var de=function(t){return t[t.Format=0]="Format",t[t.Standalone=1]="Standalone",t}(de||{}),B=function(t){return t[t.Narrow=0]="Narrow",t[t.Abbreviated=1]="Abbreviated",t[t.Wide=2]="Wide",t[t.Short=3]="Short",t}(B||{}),we=function(t){return t[t.Short=0]="Short",t[t.Medium=1]="Medium",t[t.Long=2]="Long",t[t.Full=3]="Full",t}(we||{}),It={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Ow(t){return Te(t)[Z.LocaleId]}function Fw(t,e,n){let r=Te(t),o=[r[Z.DayPeriodsFormat],r[Z.DayPeriodsStandalone]],i=Me(o,e);return Me(i,n)}function kw(t,e,n){let r=Te(t),o=[r[Z.DaysFormat],r[Z.DaysStandalone]],i=Me(o,e);return Me(i,n)}function Pw(t,e,n){let r=Te(t),o=[r[Z.MonthsFormat],r[Z.MonthsStandalone]],i=Me(o,e);return Me(i,n)}function Lw(t,e){let r=Te(t)[Z.Eras];return Me(r,e)}function Jo(t,e){let n=Te(t);return Me(n[Z.DateFormat],e)}function Xo(t,e){let n=Te(t);return Me(n[Z.TimeFormat],e)}function ei(t,e){let r=Te(t)[Z.DateTimeFormat];return Me(r,e)}function li(t,e){let n=Te(t),r=n[Z.NumberSymbols][e];if(typeof r>"u"){if(e===It.CurrencyDecimal)return n[Z.NumberSymbols][It.Decimal];if(e===It.CurrencyGroup)return n[Z.NumberSymbols][It.Group]}return r}function _p(t){if(!t[Z.ExtraData])throw new Error(`Missing extra locale data for the locale "${t[Z.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function Vw(t){let e=Te(t);return _p(e),(e[Z.ExtraData][2]||[]).map(r=>typeof r=="string"?ol(r):[ol(r[0]),ol(r[1])])}function jw(t,e,n){let r=Te(t);_p(r);let o=[r[Z.ExtraData][0],r[Z.ExtraData][1]],i=Me(o,e)||[];return Me(i,n)||[]}function Me(t,e){for(let n=e;n>-1;n--)if(typeof t[n]<"u")return t[n];throw new Error("Locale data API: locale data undefined")}function ol(t){let[e,n]=t.split(":");return{hours:+e,minutes:+n}}var Bw=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ti={},$w=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function Hw(t,e,n,r){let o=Yw(t);e=st(n,e)||e;let s=[],a;for(;e;)if(a=$w.exec(e),a){s=s.concat(a.slice(1));let c=s.pop();if(!c)break;e=c}else{s.push(e);break}let l=o.getTimezoneOffset();r&&(l=bp(r,l),o=Kw(o,r,!0));let u="";return s.forEach(c=>{let d=Qw(c);u+=d?d(o,n,l):c==="''"?"'":c.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function si(t,e,n){let r=new Date(0);return r.setFullYear(t,e,n),r.setHours(0,0,0),r}function st(t,e){let n=Ow(t);if(ti[n]??={},ti[n][e])return ti[n][e];let r="";switch(e){case"shortDate":r=Jo(t,we.Short);break;case"mediumDate":r=Jo(t,we.Medium);break;case"longDate":r=Jo(t,we.Long);break;case"fullDate":r=Jo(t,we.Full);break;case"shortTime":r=Xo(t,we.Short);break;case"mediumTime":r=Xo(t,we.Medium);break;case"longTime":r=Xo(t,we.Long);break;case"fullTime":r=Xo(t,we.Full);break;case"short":let o=st(t,"shortTime"),i=st(t,"shortDate");r=ni(ei(t,we.Short),[o,i]);break;case"medium":let s=st(t,"mediumTime"),a=st(t,"mediumDate");r=ni(ei(t,we.Medium),[s,a]);break;case"long":let l=st(t,"longTime"),u=st(t,"longDate");r=ni(ei(t,we.Long),[l,u]);break;case"full":let c=st(t,"fullTime"),d=st(t,"fullDate");r=ni(ei(t,we.Full),[c,d]);break}return r&&(ti[n][e]=r),r}function ni(t,e){return e&&(t=t.replace(/\{([^}]+)}/g,function(n,r){return e!=null&&r in e?e[r]:n})),t}function Le(t,e,n="-",r,o){let i="";(t<0||o&&t<=0)&&(o?t=-t+1:(t=-t,i=n));let s=String(t);for(;s.length<e;)s="0"+s;return r&&(s=s.slice(s.length-e)),i+s}function Uw(t,e){return Le(t,3).substring(0,e)}function J(t,e,n=0,r=!1,o=!1){return function(i,s){let a=zw(t,i);if((n>0||a>-n)&&(a+=n),t===3)a===0&&n===-12&&(a=12);else if(t===6)return Uw(a,e);let l=li(s,It.MinusSign);return Le(a,e,l,r,o)}}function zw(t,e){switch(t){case 0:return e.getFullYear();case 1:return e.getMonth();case 2:return e.getDate();case 3:return e.getHours();case 4:return e.getMinutes();case 5:return e.getSeconds();case 6:return e.getMilliseconds();case 7:return e.getDay();default:throw new Error(`Unknown DateType value "${t}".`)}}function $(t,e,n=de.Format,r=!1){return function(o,i){return qw(o,i,t,e,n,r)}}function qw(t,e,n,r,o,i){switch(n){case 2:return Pw(e,o,r)[t.getMonth()];case 1:return kw(e,o,r)[t.getDay()];case 0:let s=t.getHours(),a=t.getMinutes();if(i){let u=Vw(e),c=jw(e,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,p]=h,g=s>=f.hours&&a>=f.minutes,E=s<p.hours||s===p.hours&&a<p.minutes;if(f.hours<p.hours){if(g&&E)return!0}else if(g||E)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return c[d]}return Fw(e,o,r)[s<12?0:1];case 3:return Lw(e,r)[t.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function ri(t){return function(e,n,r){let o=-1*r,i=li(n,It.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(t){case 0:return(o>=0?"+":"")+Le(s,2,i)+Le(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+Le(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+Le(s,2,i)+":"+Le(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+Le(s,2,i)+":"+Le(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${t}"`)}}}var Gw=0,ii=4;function Ww(t){let e=si(t,Gw,1).getDay();return si(t,0,1+(e<=ii?ii:ii+7)-e)}function Ip(t){let e=t.getDay(),n=e===0?-3:ii-e;return si(t.getFullYear(),t.getMonth(),t.getDate()+n)}function il(t,e=!1){return function(n,r){let o;if(e){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Ip(n),s=Ww(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Le(o,t,li(r,It.MinusSign))}}function oi(t,e=!1){return function(n,r){let i=Ip(n).getFullYear();return Le(i,t,li(r,It.MinusSign),e)}}var sl={};function Qw(t){if(sl[t])return sl[t];let e;switch(t){case"G":case"GG":case"GGG":e=$(3,B.Abbreviated);break;case"GGGG":e=$(3,B.Wide);break;case"GGGGG":e=$(3,B.Narrow);break;case"y":e=J(0,1,0,!1,!0);break;case"yy":e=J(0,2,0,!0,!0);break;case"yyy":e=J(0,3,0,!1,!0);break;case"yyyy":e=J(0,4,0,!1,!0);break;case"Y":e=oi(1);break;case"YY":e=oi(2,!0);break;case"YYY":e=oi(3);break;case"YYYY":e=oi(4);break;case"M":case"L":e=J(1,1,1);break;case"MM":case"LL":e=J(1,2,1);break;case"MMM":e=$(2,B.Abbreviated);break;case"MMMM":e=$(2,B.Wide);break;case"MMMMM":e=$(2,B.Narrow);break;case"LLL":e=$(2,B.Abbreviated,de.Standalone);break;case"LLLL":e=$(2,B.Wide,de.Standalone);break;case"LLLLL":e=$(2,B.Narrow,de.Standalone);break;case"w":e=il(1);break;case"ww":e=il(2);break;case"W":e=il(1,!0);break;case"d":e=J(2,1);break;case"dd":e=J(2,2);break;case"c":case"cc":e=J(7,1);break;case"ccc":e=$(1,B.Abbreviated,de.Standalone);break;case"cccc":e=$(1,B.Wide,de.Standalone);break;case"ccccc":e=$(1,B.Narrow,de.Standalone);break;case"cccccc":e=$(1,B.Short,de.Standalone);break;case"E":case"EE":case"EEE":e=$(1,B.Abbreviated);break;case"EEEE":e=$(1,B.Wide);break;case"EEEEE":e=$(1,B.Narrow);break;case"EEEEEE":e=$(1,B.Short);break;case"a":case"aa":case"aaa":e=$(0,B.Abbreviated);break;case"aaaa":e=$(0,B.Wide);break;case"aaaaa":e=$(0,B.Narrow);break;case"b":case"bb":case"bbb":e=$(0,B.Abbreviated,de.Standalone,!0);break;case"bbbb":e=$(0,B.Wide,de.Standalone,!0);break;case"bbbbb":e=$(0,B.Narrow,de.Standalone,!0);break;case"B":case"BB":case"BBB":e=$(0,B.Abbreviated,de.Format,!0);break;case"BBBB":e=$(0,B.Wide,de.Format,!0);break;case"BBBBB":e=$(0,B.Narrow,de.Format,!0);break;case"h":e=J(3,1,-12);break;case"hh":e=J(3,2,-12);break;case"H":e=J(3,1);break;case"HH":e=J(3,2);break;case"m":e=J(4,1);break;case"mm":e=J(4,2);break;case"s":e=J(5,1);break;case"ss":e=J(5,2);break;case"S":e=J(6,1);break;case"SS":e=J(6,2);break;case"SSS":e=J(6,3);break;case"Z":case"ZZ":case"ZZZ":e=ri(0);break;case"ZZZZZ":e=ri(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":e=ri(1);break;case"OOOO":case"ZZZZ":case"zzzz":e=ri(2);break;default:return null}return sl[t]=e,e}function bp(t,e){t=t.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+t)/6e4;return isNaN(n)?e:n}function Zw(t,e){return t=new Date(t.getTime()),t.setMinutes(t.getMinutes()+e),t}function Kw(t,e,n){let r=n?-1:1,o=t.getTimezoneOffset(),i=bp(e,o);return Zw(t,r*(i-o))}function Yw(t){if(gp(t))return t;if(typeof t=="number"&&!isNaN(t))return new Date(t);if(typeof t=="string"){if(t=t.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(t)){let[o,i=1,s=1]=t.split("-").map(a=>+a);return si(o,i-1,s)}let n=parseFloat(t);if(!isNaN(t-n))return new Date(n);let r;if(r=t.match(Bw))return Jw(r)}let e=new Date(t);if(!gp(e))throw new Error(`Unable to convert "${t}" into a date`);return e}function Jw(t){let e=new Date(0),n=0,r=0,o=t[8]?e.setUTCFullYear:e.setFullYear,i=t[8]?e.setUTCHours:e.setHours;t[9]&&(n=Number(t[9]+t[10]),r=Number(t[9]+t[11])),o.call(e,Number(t[1]),Number(t[2])-1,Number(t[3]));let s=Number(t[4]||0)-n,a=Number(t[5]||0)-r,l=Number(t[6]||0),u=Math.floor(parseFloat("0."+(t[7]||0))*1e3);return i.call(e,s,a,l,u),e}function gp(t){return t instanceof Date&&!isNaN(t.valueOf())}function Wx(t,e){e=encodeURIComponent(e);for(let n of t.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===e)return decodeURIComponent(i)}return null}var al=/\s+/,mp=[],Qx=(()=>{class t{_ngEl;_renderer;initialClasses=mp;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(al):mp}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(al):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(al).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||t)(ce(Yt),ce(Ka))};static \u0275dir=Ko({type:t,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return t})();var ll=class{$implicit;ngForOf;index;count;constructor(e,n,r,o){this.$implicit=e,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Zx=(()=>{class t{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new ll(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),yp(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);yp(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||t)(ce(Tn),ce(Gt),ce(nl))};static \u0275dir=Ko({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return t})();function yp(t,e){t.context.$implicit=e.item}var Kx=(()=>{class t{_viewContainer;_context=new ul;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){vp("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){vp("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||t)(ce(Tn),ce(Gt))};static \u0275dir=Ko({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return t})(),ul=class{$implicit=null;ngIf=null};function vp(t,e){if(!!!(!e||e.createEmbeddedView))throw new Error(`${t} must be a TemplateRef, but received '${le(e)}'.`)}function Xw(t,e){return new m(2100,!1)}var e_="mediumDate",t_=new O(""),n_=new O(""),Yx=(()=>{class t{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??e_,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Hw(n,s,i||this.locale,a)}catch(s){throw Xw(t,s.message)}}static \u0275fac=function(r){return new(r||t)(ce(Yo,16),ce(t_,24),ce(n_,24))};static \u0275pipe=zf({name:"date",type:t,pure:!0})}return t})();var Jx=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275mod=Ja({type:t});static \u0275inj=ma({})}return t})(),r_="browser",o_="server";function Xx(t){return t===r_}function eA(t){return t===o_}var tA=(()=>{class t{static \u0275prov=V({token:t,providedIn:"root",factory:()=>new cl(S(dl),window)})}return t})(),cl=class{document;window;offset=()=>[0,0];constructor(e,n){this.document=e,this.window=n}setOffset(e){Array.isArray(e)?this.offset=()=>e:this.offset=e}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(e){this.window.scrollTo(e[0],e[1])}scrollToAnchor(e){let n=i_(this.document,e);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(e){this.window.history.scrollRestoration=e}scrollToElement(e){let n=e.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function i_(t,e){let n=t.getElementById(e)||t.getElementsByName(e)[0];if(n)return n;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let r=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(e)||i.querySelector(`[name="${e}"]`);if(s)return s}o=r.nextNode()}}return null}var Dp=class{};var N=function(t){return t[t.State=0]="State",t[t.Transition=1]="Transition",t[t.Sequence=2]="Sequence",t[t.Group=3]="Group",t[t.Animate=4]="Animate",t[t.Keyframes=5]="Keyframes",t[t.Style=6]="Style",t[t.Trigger=7]="Trigger",t[t.Reference=8]="Reference",t[t.AnimateChild=9]="AnimateChild",t[t.AnimateRef=10]="AnimateRef",t[t.Query=11]="Query",t[t.Stagger=12]="Stagger",t}(N||{}),Ke="*";function Cp(t,e=null){return{type:N.Sequence,steps:t,options:e}}function hl(t){return{type:N.Style,styles:t,offset:null}}var bt=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(e=0,n=0){this.totalTime=e+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(e=>e()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(e){this._position=this.totalTime?e*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},er=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(e){this.players=e;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this.players.forEach(e=>e.init())}onStart(e){this._onStartFns.push(e)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(e=>e()),this._onStartFns=[])}onDone(e){this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(e=>e.play())}pause(){this.players.forEach(e=>e.pause())}restart(){this.players.forEach(e=>e.restart())}finish(){this._onFinish(),this.players.forEach(e=>e.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(e=>e.destroy()),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this.players.forEach(e=>e.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(e){let n=e*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let e=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return e!=null?e.getPosition():0}beforeDestroy(){this.players.forEach(e=>{e.beforeDestroy&&e.beforeDestroy()})}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},ui="!";function Sp(t){return new m(3e3,!1)}function s_(){return new m(3100,!1)}function a_(){return new m(3101,!1)}function l_(t){return new m(3001,!1)}function u_(t){return new m(3003,!1)}function c_(t){return new m(3004,!1)}function d_(t,e){return new m(3005,!1)}function f_(){return new m(3006,!1)}function p_(){return new m(3007,!1)}function h_(t,e){return new m(3008,!1)}function g_(t){return new m(3002,!1)}function m_(t,e,n,r,o){return new m(3010,!1)}function y_(){return new m(3011,!1)}function v_(){return new m(3012,!1)}function D_(){return new m(3200,!1)}function E_(){return new m(3202,!1)}function w_(){return new m(3013,!1)}function __(t){return new m(3014,!1)}function I_(t){return new m(3015,!1)}function b_(t){return new m(3016,!1)}function C_(t){return new m(3500,!1)}function S_(t){return new m(3501,!1)}function T_(t,e){return new m(3404,!1)}function M_(t){return new m(3502,!1)}function N_(t){return new m(3503,!1)}function x_(){return new m(3300,!1)}function A_(t){return new m(3504,!1)}function R_(t){return new m(3301,!1)}function O_(t,e){return new m(3302,!1)}function F_(t){return new m(3303,!1)}function k_(t,e){return new m(3400,!1)}function P_(t){return new m(3401,!1)}function L_(t){return new m(3402,!1)}function V_(t,e){return new m(3505,!1)}var j_=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function Ct(t){switch(t.length){case 0:return new bt;case 1:return t[0];default:return new er(t)}}function zp(t,e,n=new Map,r=new Map){let o=[],i=[],s=-1,a=null;if(e.forEach(l=>{let u=l.get("offset"),c=u==s,d=c&&a||new Map;l.forEach((h,f)=>{let p=f,g=h;if(f!=="offset")switch(p=t.normalizePropertyName(p,o),g){case ui:g=n.get(f);break;case Ke:g=r.get(f);break;default:g=t.normalizeStyleValue(f,p,g,o);break}d.set(p,g)}),c||i.push(d),a=d,s=u}),o.length)throw M_(o);return i}function jl(t,e,n,r){switch(e){case"start":t.onStart(()=>r(n&&gl(n,"start",t)));break;case"done":t.onDone(()=>r(n&&gl(n,"done",t)));break;case"destroy":t.onDestroy(()=>r(n&&gl(n,"destroy",t)));break}}function gl(t,e,n){let r=n.totalTime,o=!!n.disabled,i=Bl(t.element,t.triggerName,t.fromState,t.toState,e||t.phaseName,r??t.totalTime,o),s=t._data;return s!=null&&(i._data=s),i}function Bl(t,e,n,r,o="",i=0,s){return{element:t,triggerName:e,fromState:n,toState:r,phaseName:o,totalTime:i,disabled:!!s}}function Ie(t,e,n){let r=t.get(e);return r||t.set(e,r=n),r}function Tp(t){let e=t.indexOf(":"),n=t.substring(1,e),r=t.slice(e+1);return[n,r]}var B_=typeof document>"u"?null:document.documentElement;function $l(t){let e=t.parentNode||t.host||null;return e===B_?null:e}function $_(t){return t.substring(1,6)=="ebkit"}var Xt=null,Mp=!1;function H_(t){Xt||(Xt=U_()||{},Mp=Xt.style?"WebkitAppearance"in Xt.style:!1);let e=!0;return Xt.style&&!$_(t)&&(e=t in Xt.style,!e&&Mp&&(e="Webkit"+t.charAt(0).toUpperCase()+t.slice(1)in Xt.style)),e}function lA(t){return j_.has(t)}function U_(){return typeof document<"u"?document.body:null}function qp(t,e){for(;e;){if(e===t)return!0;e=$l(e)}return!1}function Gp(t,e,n){if(n)return Array.from(t.querySelectorAll(e));let r=t.querySelector(e);return r?[r]:[]}var Wp=(()=>{class t{validateStyleProperty(n){return H_(n)}containsElement(n,r){return qp(n,r)}getParentElement(n){return $l(n)}query(n,r,o){return Gp(n,r,o)}computeStyle(n,r,o){return o||""}animate(n,r,o,i,s,a=[],l){return new bt(o,i)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=V({token:t,factory:t.\u0275fac})}return t})(),Np=class{static NOOP=new Wp},wl=class{},_l=class{normalizePropertyName(e,n){return e}normalizeStyleValue(e,n,r,o){return r}},z_=1e3,Qp="{{",q_="}}",Hl="ng-enter",gi="ng-leave",ci="ng-trigger",mi=".ng-trigger",xp="ng-animating",Il=".ng-animating";function lt(t){if(typeof t=="number")return t;let e=t.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:bl(parseFloat(e[1]),e[2])}function bl(t,e){switch(e){case"s":return t*z_;default:return t}}function yi(t,e,n){return t.hasOwnProperty("duration")?t:G_(t,e,n)}function G_(t,e,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,o,i=0,s="";if(typeof t=="string"){let a=t.match(r);if(a===null)return e.push(Sp(t)),{duration:0,delay:0,easing:""};o=bl(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(i=bl(parseFloat(l),a[4]));let u=a[5];u&&(s=u)}else o=t;if(!n){let a=!1,l=e.length;o<0&&(e.push(s_()),a=!0),i<0&&(e.push(a_()),a=!0),a&&e.splice(l,0,Sp(t))}return{duration:o,delay:i,easing:s}}function W_(t){return t.length?t[0]instanceof Map?t:t.map(e=>new Map(Object.entries(e))):[]}function Ap(t){return Array.isArray(t)?new Map(...t):new Map(t)}function Ye(t,e,n){e.forEach((r,o)=>{let i=Ul(o);n&&!n.has(o)&&n.set(o,t.style[i]),t.style[i]=r})}function tn(t,e){e.forEach((n,r)=>{let o=Ul(r);t.style[o]=""})}function tr(t){return Array.isArray(t)?t.length==1?t[0]:Cp(t):t}function Q_(t,e,n){let r=e.params||{},o=Zp(t);o.length&&o.forEach(i=>{r.hasOwnProperty(i)||n.push(l_(i))})}var Cl=new RegExp(`${Qp}\\s*(.+?)\\s*${q_}`,"g");function Zp(t){let e=[];if(typeof t=="string"){let n;for(;n=Cl.exec(t);)e.push(n[1]);Cl.lastIndex=0}return e}function rr(t,e,n){let r=`${t}`,o=r.replace(Cl,(i,s)=>{let a=e[s];return a==null&&(n.push(u_(s)),a=""),a.toString()});return o==r?t:o}var Z_=/-+([a-z0-9])/g;function Ul(t){return t.replace(Z_,(...e)=>e[1].toUpperCase())}function uA(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function K_(t,e){return t===0||e===0}function Y_(t,e,n){if(n.size&&e.length){let r=e[0],o=[];if(n.forEach((i,s)=>{r.has(s)||o.push(s),r.set(s,i)}),o.length)for(let i=1;i<e.length;i++){let s=e[i];o.forEach(a=>s.set(a,zl(t,a)))}}return e}function _e(t,e,n){switch(e.type){case N.Trigger:return t.visitTrigger(e,n);case N.State:return t.visitState(e,n);case N.Transition:return t.visitTransition(e,n);case N.Sequence:return t.visitSequence(e,n);case N.Group:return t.visitGroup(e,n);case N.Animate:return t.visitAnimate(e,n);case N.Keyframes:return t.visitKeyframes(e,n);case N.Style:return t.visitStyle(e,n);case N.Reference:return t.visitReference(e,n);case N.AnimateChild:return t.visitAnimateChild(e,n);case N.AnimateRef:return t.visitAnimateRef(e,n);case N.Query:return t.visitQuery(e,n);case N.Stagger:return t.visitStagger(e,n);default:throw c_(e.type)}}function zl(t,e){return window.getComputedStyle(t)[e]}var J_=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Sl=class extends wl{normalizePropertyName(e,n){return Ul(e)}normalizeStyleValue(e,n,r,o){let i="",s=r.toString().trim();if(J_.has(n)&&r!==0&&r!=="0")if(typeof r=="number")i="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&o.push(d_(e,r))}return s+i}};var vi="*";function X_(t,e){let n=[];return typeof t=="string"?t.split(/\s*,\s*/).forEach(r=>eI(r,n,e)):n.push(t),n}function eI(t,e,n){if(t[0]==":"){let l=tI(t,n);if(typeof l=="function"){e.push(l);return}t=l}let r=t.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(I_(t)),e;let o=r[1],i=r[2],s=r[3];e.push(Rp(o,s));let a=o==vi&&s==vi;i[0]=="<"&&!a&&e.push(Rp(s,o))}function tI(t,e){switch(t){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return e.push(b_(t)),"* => *"}}var di=new Set(["true","1"]),fi=new Set(["false","0"]);function Rp(t,e){let n=di.has(t)||fi.has(t),r=di.has(e)||fi.has(e);return(o,i)=>{let s=t==vi||t==o,a=e==vi||e==i;return!s&&n&&typeof o=="boolean"&&(s=o?di.has(t):fi.has(t)),!a&&r&&typeof i=="boolean"&&(a=i?di.has(e):fi.has(e)),s&&a}}var Kp=":self",nI=new RegExp(`s*${Kp}s*,?`,"g");function ql(t,e,n,r){return new Tl(t).build(e,n,r)}var Op="",Tl=class{_driver;constructor(e){this._driver=e}build(e,n,r){let o=new Ml(n);return this._resetContextStyleTimingState(o),_e(this,tr(e),o)}_resetContextStyleTimingState(e){e.currentQuerySelector=Op,e.collectedStyles=new Map,e.collectedStyles.set(Op,new Map),e.currentTime=0}visitTrigger(e,n){let r=n.queryCount=0,o=n.depCount=0,i=[],s=[];return e.name.charAt(0)=="@"&&n.errors.push(f_()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==N.State){let l=a,u=l.name;u.toString().split(/\s*,\s*/).forEach(c=>{l.name=c,i.push(this.visitState(l,n))}),l.name=u}else if(a.type==N.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,o+=l.depCount,s.push(l)}else n.errors.push(p_())}),{type:N.Trigger,name:e.name,states:i,transitions:s,queryCount:r,depCount:o,options:null}}visitState(e,n){let r=this.visitStyle(e.styles,n),o=e.options&&e.options.params||null;if(r.containsDynamicStyles){let i=new Set,s=o||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{Zp(l).forEach(u=>{s.hasOwnProperty(u)||i.add(u)})})}),i.size&&n.errors.push(h_(e.name,[...i.values()]))}return{type:N.State,name:e.name,style:r,options:o?{params:o}:null}}visitTransition(e,n){n.queryCount=0,n.depCount=0;let r=_e(this,tr(e.animation),n),o=X_(e.expr,n.errors);return{type:N.Transition,matchers:o,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:en(e.options)}}visitSequence(e,n){return{type:N.Sequence,steps:e.steps.map(r=>_e(this,r,n)),options:en(e.options)}}visitGroup(e,n){let r=n.currentTime,o=0,i=e.steps.map(s=>{n.currentTime=r;let a=_e(this,s,n);return o=Math.max(o,n.currentTime),a});return n.currentTime=o,{type:N.Group,steps:i,options:en(e.options)}}visitAnimate(e,n){let r=sI(e.timings,n.errors);n.currentAnimateTimings=r;let o,i=e.styles?e.styles:hl({});if(i.type==N.Keyframes)o=this.visitKeyframes(i,n);else{let s=e.styles,a=!1;if(!s){a=!0;let u={};r.easing&&(u.easing=r.easing),s=hl(u)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,o=l}return n.currentAnimateTimings=null,{type:N.Animate,timings:r,style:o,options:null}}visitStyle(e,n){let r=this._makeStyleAst(e,n);return this._validateStyleAst(r,n),r}_makeStyleAst(e,n){let r=[],o=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of o)typeof a=="string"?a===Ke?r.push(a):n.errors.push(g_(a)):r.push(new Map(Object.entries(a)));let i=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!i)){for(let l of a.values())if(l.toString().indexOf(Qp)>=0){i=!0;break}}}),{type:N.Style,styles:r,easing:s,offset:e.offset,containsDynamicStyles:i,options:null}}_validateStyleAst(e,n){let r=n.currentAnimateTimings,o=n.currentTime,i=n.currentTime;r&&i>0&&(i-=r.duration+r.delay),e.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let u=n.collectedStyles.get(n.currentQuerySelector),c=u.get(l),d=!0;c&&(i!=o&&i>=c.startTime&&o<=c.endTime&&(n.errors.push(m_(l,c.startTime,c.endTime,i,o)),d=!1),i=c.startTime),d&&u.set(l,{startTime:i,endTime:o}),n.options&&Q_(a,n.options,n.errors)})})}visitKeyframes(e,n){let r={type:N.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(y_()),r;let o=1,i=0,s=[],a=!1,l=!1,u=0,c=e.steps.map(w=>{let P=this._makeStyleAst(w,n),H=P.offset!=null?P.offset:iI(P.styles),L=0;return H!=null&&(i++,L=P.offset=H),l=l||L<0||L>1,a=a||L<u,u=L,s.push(L),P});l&&n.errors.push(v_()),a&&n.errors.push(D_());let d=e.steps.length,h=0;i>0&&i<d?n.errors.push(E_()):i==0&&(h=o/(d-1));let f=d-1,p=n.currentTime,g=n.currentAnimateTimings,E=g.duration;return c.forEach((w,P)=>{let H=h>0?P==f?1:h*P:s[P],L=H*E;n.currentTime=p+g.delay+L,g.duration=L,this._validateStyleAst(w,n),w.offset=H,r.styles.push(w)}),r}visitReference(e,n){return{type:N.Reference,animation:_e(this,tr(e.animation),n),options:en(e.options)}}visitAnimateChild(e,n){return n.depCount++,{type:N.AnimateChild,options:en(e.options)}}visitAnimateRef(e,n){return{type:N.AnimateRef,animation:this.visitReference(e.animation,n),options:en(e.options)}}visitQuery(e,n){let r=n.currentQuerySelector,o=e.options||{};n.queryCount++,n.currentQuery=e;let[i,s]=rI(e.selector);n.currentQuerySelector=r.length?r+" "+i:i,Ie(n.collectedStyles,n.currentQuerySelector,new Map);let a=_e(this,tr(e.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:N.Query,selector:i,limit:o.limit||0,optional:!!o.optional,includeSelf:s,animation:a,originalSelector:e.selector,options:en(e.options)}}visitStagger(e,n){n.currentQuery||n.errors.push(w_());let r=e.timings==="full"?{duration:0,delay:0,easing:"full"}:yi(e.timings,n.errors,!0);return{type:N.Stagger,animation:_e(this,tr(e.animation),n),timings:r,options:null}}};function rI(t){let e=!!t.split(/\s*,\s*/).find(n=>n==Kp);return e&&(t=t.replace(nI,"")),t=t.replace(/@\*/g,mi).replace(/@\w+/g,n=>mi+"-"+n.slice(1)).replace(/:animating/g,Il),[t,e]}function oI(t){return t?fe({},t):null}var Ml=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function iI(t){if(typeof t=="string")return null;let e=null;if(Array.isArray(t))t.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;e=parseFloat(r.get("offset")),r.delete("offset")}});else if(t instanceof Map&&t.has("offset")){let n=t;e=parseFloat(n.get("offset")),n.delete("offset")}return e}function sI(t,e){if(t.hasOwnProperty("duration"))return t;if(typeof t=="number"){let i=yi(t,e).duration;return ml(i,0,"")}let n=t;if(n.split(/\s+/).some(i=>i.charAt(0)=="{"&&i.charAt(1)=="{")){let i=ml(0,0,"");return i.dynamic=!0,i.strValue=n,i}let o=yi(n,e);return ml(o.duration,o.delay,o.easing)}function en(t){return t?(t=fe({},t),t.params&&(t.params=oI(t.params))):t={},t}function ml(t,e,n){return{duration:t,delay:e,easing:n}}function Gl(t,e,n,r,o,i,s=null,a=!1){return{type:1,element:t,keyframes:e,preStyleProps:n,postStyleProps:r,duration:o,delay:i,totalTime:o+i,easing:s,subTimeline:a}}var Nn=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,n){let r=this._map.get(e);r||this._map.set(e,r=[]),r.push(...n)}has(e){return this._map.has(e)}clear(){this._map.clear()}},aI=1,lI=":enter",uI=new RegExp(lI,"g"),cI=":leave",dI=new RegExp(cI,"g");function Wl(t,e,n,r,o,i=new Map,s=new Map,a,l,u=[]){return new Nl().buildKeyframes(t,e,n,r,o,i,s,a,l,u)}var Nl=class{buildKeyframes(e,n,r,o,i,s,a,l,u,c=[]){u=u||new Nn;let d=new xl(e,n,u,o,i,c,[]);d.options=l;let h=l.delay?lt(l.delay):0;d.currentTimeline.delayNextStep(h),d.currentTimeline.setStyles([s],null,d.errors,l),_e(this,r,d);let f=d.timelines.filter(p=>p.containsAnimation());if(f.length&&a.size){let p;for(let g=f.length-1;g>=0;g--){let E=f[g];if(E.element===n){p=E;break}}p&&!p.allowOnlyTimelineStyles()&&p.setStyles([a],null,d.errors,l)}return f.length?f.map(p=>p.buildKeyframes()):[Gl(n,[],[],[],0,h,"",!1)]}visitTrigger(e,n){}visitState(e,n){}visitTransition(e,n){}visitAnimateChild(e,n){let r=n.subInstructions.get(n.element);if(r){let o=n.createSubContext(e.options),i=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,o,o.options);i!=s&&n.transformIntoNewTimeline(s)}n.previousNode=e}visitAnimateRef(e,n){let r=n.createSubContext(e.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],n,r),this.visitReference(e.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=e}_applyAnimationRefDelays(e,n,r){for(let o of e){let i=o?.delay;if(i){let s=typeof i=="number"?i:lt(rr(i,o?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(e,n,r){let i=n.currentTimeline.currentTime,s=r.duration!=null?lt(r.duration):null,a=r.delay!=null?lt(r.delay):null;return s!==0&&e.forEach(l=>{let u=n.appendInstructionToTimeline(l,s,a);i=Math.max(i,u.duration+u.delay)}),i}visitReference(e,n){n.updateOptions(e.options,!0),_e(this,e.animation,n),n.previousNode=e}visitSequence(e,n){let r=n.subContextCount,o=n,i=e.options;if(i&&(i.params||i.delay)&&(o=n.createSubContext(i),o.transformIntoNewTimeline(),i.delay!=null)){o.previousNode.type==N.Style&&(o.currentTimeline.snapshotCurrentStyles(),o.previousNode=Di);let s=lt(i.delay);o.delayNextStep(s)}e.steps.length&&(e.steps.forEach(s=>_e(this,s,o)),o.currentTimeline.applyStylesToKeyframe(),o.subContextCount>r&&o.transformIntoNewTimeline()),n.previousNode=e}visitGroup(e,n){let r=[],o=n.currentTimeline.currentTime,i=e.options&&e.options.delay?lt(e.options.delay):0;e.steps.forEach(s=>{let a=n.createSubContext(e.options);i&&a.delayNextStep(i),_e(this,s,a),o=Math.max(o,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(o),n.previousNode=e}_visitTiming(e,n){if(e.dynamic){let r=e.strValue,o=n.params?rr(r,n.params,n.errors):r;return yi(o,n.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,n){let r=n.currentAnimateTimings=this._visitTiming(e.timings,n),o=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),o.snapshotCurrentStyles());let i=e.style;i.type==N.Keyframes?this.visitKeyframes(i,n):(n.incrementTime(r.duration),this.visitStyle(i,n),o.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=e}visitStyle(e,n){let r=n.currentTimeline,o=n.currentAnimateTimings;!o&&r.hasCurrentStyleProperties()&&r.forwardFrame();let i=o&&o.easing||e.easing;e.isEmptyStep?r.applyEmptyStep(i):r.setStyles(e.styles,i,n.errors,n.options),n.previousNode=e}visitKeyframes(e,n){let r=n.currentAnimateTimings,o=n.currentTimeline.duration,i=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,e.styles.forEach(l=>{let u=l.offset||0;a.forwardTime(u*i),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(o+i),n.previousNode=e}visitQuery(e,n){let r=n.currentTimeline.currentTime,o=e.options||{},i=o.delay?lt(o.delay):0;i&&(n.previousNode.type===N.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Di);let s=r,a=n.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!o.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((u,c)=>{n.currentQueryIndex=c;let d=n.createSubContext(e.options,u);i&&d.delayNextStep(i),u===n.element&&(l=d.currentTimeline),_e(this,e.animation,d),d.currentTimeline.applyStylesToKeyframe();let h=d.currentTimeline.currentTime;s=Math.max(s,h)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=e}visitStagger(e,n){let r=n.parentContext,o=n.currentTimeline,i=e.timings,s=Math.abs(i.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(i.duration<0?"reverse":i.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let c=n.currentTimeline;l&&c.delayNextStep(l);let d=c.currentTime;_e(this,e.animation,n),n.previousNode=e,r.currentStaggerTime=o.currentTime-d+(o.startTime-r.currentTimeline.startTime)}},Di={},xl=class t{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=Di;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,n,r,o,i,s,a,l){this._driver=e,this.element=n,this.subInstructions=r,this._enterClassName=o,this._leaveClassName=i,this.errors=s,this.timelines=a,this.currentTimeline=l||new Ei(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,n){if(!e)return;let r=e,o=this.options;r.duration!=null&&(o.duration=lt(r.duration)),r.delay!=null&&(o.delay=lt(r.delay));let i=r.params;if(i){let s=o.params;s||(s=this.options.params={}),Object.keys(i).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=rr(i[a],s,this.errors))})}}_copyOptions(){let e={};if(this.options){let n=this.options.params;if(n){let r=e.params={};Object.keys(n).forEach(o=>{r[o]=n[o]})}}return e}createSubContext(e=null,n,r){let o=n||this.element,i=new t(this._driver,o,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(o,r||0));return i.previousNode=this.previousNode,i.currentAnimateTimings=this.currentAnimateTimings,i.options=this._copyOptions(),i.updateOptions(e),i.currentQueryIndex=this.currentQueryIndex,i.currentQueryTotal=this.currentQueryTotal,i.parentContext=this,this.subContextCount++,i}transformIntoNewTimeline(e){return this.previousNode=Di,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,n,r){let o={duration:n??e.duration,delay:this.currentTimeline.currentTime+(r??0)+e.delay,easing:""},i=new Al(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,o,e.stretchStartingKeyframe);return this.timelines.push(i),o}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,n,r,o,i,s){let a=[];if(o&&a.push(this.element),e.length>0){e=e.replace(uI,"."+this._enterClassName),e=e.replace(dI,"."+this._leaveClassName);let l=r!=1,u=this._driver.query(this.element,e,l);r!==0&&(u=r<0?u.slice(u.length+r,u.length):u.slice(0,r)),a.push(...u)}return!i&&a.length==0&&s.push(__(n)),a}},Ei=class t{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,n,r,o){this._driver=e,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=o,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+e),n&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,n){return this.applyStylesToKeyframe(),new t(this._driver,e,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=aI,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,n){this._localTimelineStyles.set(e,n),this._globalTimelineStyles.set(e,n),this._styleSummary.set(e,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||Ke),this._currentKeyframe.set(n,Ke);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,n,r,o){n&&this._previousKeyframe.set("easing",n);let i=o&&o.params||{},s=fI(e,this._globalTimelineStyles);for(let[a,l]of s){let u=rr(l,i,r);this._pendingStyles.set(a,u),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??Ke),this._updateStyle(a,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,n)=>{this._currentKeyframe.set(n,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,e)}))}snapshotCurrentStyles(){for(let[e,n]of this._localTimelineStyles)this._pendingStyles.set(e,n),this._updateStyle(e,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let n in this._currentKeyframe)e.push(n);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((n,r)=>{let o=this._styleSummary.get(r);(!o||n.time>o.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,o=[];this._keyframes.forEach((a,l)=>{let u=new Map([...this._backFill,...a]);u.forEach((c,d)=>{c===ui?e.add(d):c===Ke&&n.add(d)}),r||u.set("offset",l/this.duration),o.push(u)});let i=[...e.values()],s=[...n.values()];if(r){let a=o[0],l=new Map(a);a.set("offset",0),l.set("offset",1),o=[a,l]}return Gl(this.element,o,i,s,this.duration,this.startTime,this.easing,!1)}},Al=class extends Ei{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,n,r,o,i,s,a=!1){super(e,n,s.delay),this.keyframes=r,this.preStyleProps=o,this.postStyleProps=i,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:n,duration:r,easing:o}=this.timings;if(this._stretchStartingKeyframe&&n){let i=[],s=r+n,a=n/s,l=new Map(e[0]);l.set("offset",0),i.push(l);let u=new Map(e[0]);u.set("offset",Fp(a)),i.push(u);let c=e.length-1;for(let d=1;d<=c;d++){let h=new Map(e[d]),f=h.get("offset"),p=n+f*r;h.set("offset",Fp(p/s)),i.push(h)}r=s,n=0,o="",e=i}return Gl(this.element,e,this.preStyleProps,this.postStyleProps,r,n,o,!0)}};function Fp(t,e=3){let n=Math.pow(10,e-1);return Math.round(t*n)/n}function fI(t,e){let n=new Map,r;return t.forEach(o=>{if(o==="*"){r??=e.keys();for(let i of r)n.set(i,Ke)}else for(let[i,s]of o)n.set(i,s)}),n}function kp(t,e,n,r,o,i,s,a,l,u,c,d,h){return{type:0,element:t,triggerName:e,isRemovalTransition:o,fromState:n,fromStyles:i,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:u,postStyleProps:c,totalTime:d,errors:h}}var yl={},wi=class{_triggerName;ast;_stateStyles;constructor(e,n,r){this._triggerName=e,this.ast=n,this._stateStyles=r}match(e,n,r,o){return pI(this.ast.matchers,e,n,r,o)}buildStyles(e,n,r){let o=this._stateStyles.get("*");return e!==void 0&&(o=this._stateStyles.get(e?.toString())||o),o?o.buildStyles(n,r):new Map}build(e,n,r,o,i,s,a,l,u,c){let d=[],h=this.ast.options&&this.ast.options.params||yl,f=a&&a.params||yl,p=this.buildStyles(r,f,d),g=l&&l.params||yl,E=this.buildStyles(o,g,d),w=new Set,P=new Map,H=new Map,L=o==="void",xe={params:Yp(g,h),delay:this.ast.options?.delay},z=c?[]:Wl(e,n,this.ast.animation,i,s,p,E,xe,u,d),q=0;return z.forEach(ee=>{q=Math.max(ee.duration+ee.delay,q)}),d.length?kp(n,this._triggerName,r,o,L,p,E,[],[],P,H,q,d):(z.forEach(ee=>{let je=ee.element,re=Ie(P,je,new Set);ee.preStyleProps.forEach(St=>re.add(St));let sr=Ie(H,je,new Set);ee.postStyleProps.forEach(St=>sr.add(St)),je!==n&&w.add(je)}),kp(n,this._triggerName,r,o,L,p,E,z,[...w.values()],P,H,q))}};function pI(t,e,n,r,o){return t.some(i=>i(e,n,r,o))}function Yp(t,e){let n=fe({},e);return Object.entries(t).forEach(([r,o])=>{o!=null&&(n[r]=o)}),n}var Rl=class{styles;defaultParams;normalizer;constructor(e,n,r){this.styles=e,this.defaultParams=n,this.normalizer=r}buildStyles(e,n){let r=new Map,o=Yp(e,this.defaultParams);return this.styles.styles.forEach(i=>{typeof i!="string"&&i.forEach((s,a)=>{s&&(s=rr(s,o,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function hI(t,e,n){return new Ol(t,e,n)}var Ol=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,n,r){this.name=e,this.ast=n,this._normalizer=r,n.states.forEach(o=>{let i=o.options&&o.options.params||{};this.states.set(o.name,new Rl(o.style,i,r))}),Pp(this.states,"true","1"),Pp(this.states,"false","0"),n.transitions.forEach(o=>{this.transitionFactories.push(new wi(e,o,this.states))}),this.fallbackTransition=gI(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,n,r,o){return this.transitionFactories.find(s=>s.match(e,n,r,o))||null}matchStyles(e,n,r){return this.fallbackTransition.buildStyles(e,n,r)}};function gI(t,e,n){let r=[(s,a)=>!0],o={type:N.Sequence,steps:[],options:null},i={type:N.Transition,animation:o,matchers:r,options:null,queryCount:0,depCount:0};return new wi(t,i,e)}function Pp(t,e,n){t.has(e)?t.has(n)||t.set(n,t.get(e)):t.has(n)&&t.set(e,t.get(n))}var mI=new Nn,Fl=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,n,r){this.bodyNode=e,this._driver=n,this._normalizer=r}register(e,n){let r=[],o=[],i=ql(this._driver,n,r,o);if(r.length)throw N_(r);this._animations.set(e,i)}_buildPlayer(e,n,r){let o=e.element,i=zp(this._normalizer,e.keyframes,n,r);return this._driver.animate(o,i,e.duration,e.delay,e.easing,[],!0)}create(e,n,r={}){let o=[],i=this._animations.get(e),s,a=new Map;if(i?(s=Wl(this._driver,n,i,Hl,gi,new Map,new Map,r,mI,o),s.forEach(c=>{let d=Ie(a,c.element,new Map);c.postStyleProps.forEach(h=>d.set(h,null))})):(o.push(x_()),s=[]),o.length)throw A_(o);a.forEach((c,d)=>{c.forEach((h,f)=>{c.set(f,this._driver.computeStyle(d,f,Ke))})});let l=s.map(c=>{let d=a.get(c.element);return this._buildPlayer(c,new Map,d)}),u=Ct(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let n=this._getPlayer(e);n.destroy(),this._playersById.delete(e);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(e){let n=this._playersById.get(e);if(!n)throw R_(e);return n}listen(e,n,r,o){let i=Bl(n,"","","");return jl(this._getPlayer(e),r,i,o),()=>{}}command(e,n,r,o){if(r=="register"){this.register(e,o[0]);return}if(r=="create"){let s=o[0]||{};this.create(e,n,s);return}let i=this._getPlayer(e);switch(r){case"play":i.play();break;case"pause":i.pause();break;case"reset":i.reset();break;case"restart":i.restart();break;case"finish":i.finish();break;case"init":i.init();break;case"setPosition":i.setPosition(parseFloat(o[0]));break;case"destroy":this.destroy(e);break}}},Lp="ng-animate-queued",yI=".ng-animate-queued",vl="ng-animate-disabled",vI=".ng-animate-disabled",DI="ng-star-inserted",EI=".ng-star-inserted",wI=[],Jp={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},_I={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Ve="__ng_removed",or=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,n=""){this.namespaceId=n;let r=e&&e.hasOwnProperty("value"),o=r?e.value:e;if(this.value=bI(o),r){let i=e,{value:s}=i,a=eu(i,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let n=e.params;if(n){let r=this.options.params;Object.keys(n).forEach(o=>{r[o]==null&&(r[o]=n[o])})}}},nr="void",Dl=new or(nr),kl=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,n,r){this.id=e,this.hostElement=n,this._engine=r,this._hostClassName="ng-tns-"+e,Ne(n,this._hostClassName)}listen(e,n,r,o){if(!this._triggers.has(n))throw O_(r,n);if(r==null||r.length==0)throw F_(n);if(!CI(r))throw k_(r,n);let i=Ie(this._elementListeners,e,[]),s={name:n,phase:r,callback:o};i.push(s);let a=Ie(this._engine.statesByElement,e,new Map);return a.has(n)||(Ne(e,ci),Ne(e,ci+"-"+n),a.set(n,Dl)),()=>{this._engine.afterFlush(()=>{let l=i.indexOf(s);l>=0&&i.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(e,n){return this._triggers.has(e)?!1:(this._triggers.set(e,n),!0)}_getTrigger(e){let n=this._triggers.get(e);if(!n)throw P_(e);return n}trigger(e,n,r,o=!0){let i=this._getTrigger(n),s=new ir(this.id,n,e),a=this._engine.statesByElement.get(e);a||(Ne(e,ci),Ne(e,ci+"-"+n),this._engine.statesByElement.set(e,a=new Map));let l=a.get(n),u=new or(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),a.set(n,u),l||(l=Dl),!(u.value===nr)&&l.value===u.value){if(!MI(l.params,u.params)){let g=[],E=i.matchStyles(l.value,l.params,g),w=i.matchStyles(u.value,u.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{tn(e,E),Ye(e,w)})}return}let h=Ie(this._engine.playersByElement,e,[]);h.forEach(g=>{g.namespaceId==this.id&&g.triggerName==n&&g.queued&&g.destroy()});let f=i.matchTransition(l.value,u.value,e,u.params),p=!1;if(!f){if(!o)return;f=i.fallbackTransition,p=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:n,transition:f,fromState:l,toState:u,player:s,isFallbackTransition:p}),p||(Ne(e,Lp),s.onStart(()=>{Mn(e,Lp)})),s.onDone(()=>{let g=this.players.indexOf(s);g>=0&&this.players.splice(g,1);let E=this._engine.playersByElement.get(e);if(E){let w=E.indexOf(s);w>=0&&E.splice(w,1)}}),this.players.push(s),h.push(s),s}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(n=>n.delete(e)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(o=>o.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let n=this._engine.playersByElement.get(e);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,n){let r=this._engine.driver.query(e,mi,!0);r.forEach(o=>{if(o[Ve])return;let i=this._engine.fetchNamespacesByElement(o);i.size?i.forEach(s=>s.triggerLeaveAnimation(o,n,!1,!0)):this.clearElementCache(o)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(o=>this.clearElementCache(o)))}triggerLeaveAnimation(e,n,r,o){let i=this._engine.statesByElement.get(e),s=new Map;if(i){let a=[];if(i.forEach((l,u)=>{if(s.set(u,l.value),this._triggers.has(u)){let c=this.trigger(e,u,nr,o);c&&a.push(c)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,n,s),r&&Ct(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let n=this._elementListeners.get(e),r=this._engine.statesByElement.get(e);if(n&&r){let o=new Set;n.forEach(i=>{let s=i.name;if(o.has(s))return;o.add(s);let l=this._triggers.get(s).fallbackTransition,u=r.get(s)||Dl,c=new or(nr),d=new ir(this.id,s,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:s,transition:l,fromState:u,toState:c,player:d,isFallbackTransition:!0})})}}removeNode(e,n){let r=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,n),this.triggerLeaveAnimation(e,n,!0))return;let o=!1;if(r.totalAnimations){let i=r.players.length?r.playersByQueriedElement.get(e):[];if(i&&i.length)o=!0;else{let s=e;for(;s=s.parentNode;)if(r.statesByElement.get(s)){o=!0;break}}}if(this.prepareLeaveAnimationListeners(e),o)r.markElementAsRemoved(this.id,e,!1,n);else{let i=e[Ve];(!i||i===Jp)&&(r.afterFlush(()=>this.clearElementCache(e)),r.destroyInnerAnimations(e),r._onRemovalComplete(e,n))}}insertNode(e,n){Ne(e,this._hostClassName)}drainQueuedTransitions(e){let n=[];return this._queue.forEach(r=>{let o=r.player;if(o.destroyed)return;let i=r.element,s=this._elementListeners.get(i);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=Bl(i,r.triggerName,r.fromState.value,r.toState.value);l._data=e,jl(r.player,a.phase,l,a.callback)}}),o.markedForDestroy?this._engine.afterFlush(()=>{o.destroy()}):n.push(r)}),this._queue=[],n.sort((r,o)=>{let i=r.transition.ast.depCount,s=o.transition.ast.depCount;return i==0||s==0?i-s:this._engine.driver.containsElement(r.element,o.element)?1:-1})}destroy(e){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},Pl=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,n)=>{};_onRemovalComplete(e,n){this.onRemovalComplete(e,n)}constructor(e,n,r){this.bodyNode=e,this.driver=n,this._normalizer=r}get queuedPlayers(){let e=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&e.push(r)})}),e}createNamespace(e,n){let r=new kl(e,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[e]=r}_balanceNamespaceList(e,n){let r=this._namespaceList,o=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=o.get(a);if(l){let u=r.indexOf(l);r.splice(u+1,0,e),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(e)}else r.push(e);return o.set(n,e),e}register(e,n){let r=this._namespaceLookup[e];return r||(r=this.createNamespace(e,n)),r}registerTrigger(e,n,r){let o=this._namespaceLookup[e];o&&o.register(n,r)&&this.totalAnimations++}destroy(e,n){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(e);this.namespacesByHostElement.delete(r.hostElement);let o=this._namespaceList.indexOf(r);o>=0&&this._namespaceList.splice(o,1),r.destroy(n),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let n=new Set,r=this.statesByElement.get(e);if(r){for(let o of r.values())if(o.namespaceId){let i=this._fetchNamespace(o.namespaceId);i&&n.add(i)}}return n}trigger(e,n,r,o){if(pi(n)){let i=this._fetchNamespace(e);if(i)return i.trigger(n,r,o),!0}return!1}insertNode(e,n,r,o){if(!pi(n))return;let i=n[Ve];if(i&&i.setForRemoval){i.setForRemoval=!1,i.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(e){let s=this._fetchNamespace(e);s&&s.insertNode(n,r)}o&&this.collectEnterElement(n)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,n){n?this.disabledNodes.has(e)||(this.disabledNodes.add(e),Ne(e,vl)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),Mn(e,vl))}removeNode(e,n,r){if(pi(n)){let o=e?this._fetchNamespace(e):null;o?o.removeNode(n,r):this.markElementAsRemoved(e,n,!1,r);let i=this.namespacesByHostElement.get(n);i&&i.id!==e&&i.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(e,n,r,o,i){this.collectedLeaveElements.push(n),n[Ve]={namespaceId:e,setForRemoval:o,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:i}}listen(e,n,r,o,i){return pi(n)?this._fetchNamespace(e).listen(n,r,o,i):()=>{}}_buildInstruction(e,n,r,o,i){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,r,o,e.fromState.options,e.toState.options,n,i)}destroyInnerAnimations(e){let n=this.driver.query(e,mi,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(e,Il,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(e){let n=this.playersByElement.get(e);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(e){let n=this.playersByQueriedElement.get(e);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return Ct(this.players).onDone(()=>e());e()})}processLeaveNode(e){let n=e[Ve];if(n&&n.setForRemoval){if(e[Ve]=Jp,n.namespaceId){this.destroyInnerAnimations(e);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(e)}this._onRemovalComplete(e,n.setForRemoval)}e.classList?.contains(vl)&&this.markElementAsDisabled(e,!1),this.driver.query(e,vI,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(e=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,o)=>this._balanceNamespaceList(r,o)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let o=this.collectedEnterElements[r];Ne(o,DI)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,e)}finally{for(let o=0;o<r.length;o++)r[o]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let o=this.collectedLeaveElements[r];this.processLeaveNode(o)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?Ct(n).onDone(()=>{r.forEach(o=>o())}):r.forEach(o=>o())}}reportError(e){throw L_(e)}_flushAnimations(e,n){let r=new Nn,o=[],i=new Map,s=[],a=new Map,l=new Map,u=new Map,c=new Set;this.disabledNodes.forEach(y=>{c.add(y);let v=this.driver.query(y,yI,!0);for(let D=0;D<v.length;D++)c.add(v[D])});let d=this.bodyNode,h=Array.from(this.statesByElement.keys()),f=Bp(h,this.collectedEnterElements),p=new Map,g=0;f.forEach((y,v)=>{let D=Hl+g++;p.set(v,D),y.forEach(F=>Ne(F,D))});let E=[],w=new Set,P=new Set;for(let y=0;y<this.collectedLeaveElements.length;y++){let v=this.collectedLeaveElements[y],D=v[Ve];D&&D.setForRemoval&&(E.push(v),w.add(v),D.hasAnimation?this.driver.query(v,EI,!0).forEach(F=>w.add(F)):P.add(v))}let H=new Map,L=Bp(h,Array.from(w));L.forEach((y,v)=>{let D=gi+g++;H.set(v,D),y.forEach(F=>Ne(F,D))}),e.push(()=>{f.forEach((y,v)=>{let D=p.get(v);y.forEach(F=>Mn(F,D))}),L.forEach((y,v)=>{let D=H.get(v);y.forEach(F=>Mn(F,D))}),E.forEach(y=>{this.processLeaveNode(y)})});let xe=[],z=[];for(let y=this._namespaceList.length-1;y>=0;y--)this._namespaceList[y].drainQueuedTransitions(n).forEach(D=>{let F=D.player,te=D.element;if(xe.push(F),this.collectedEnterElements.length){let oe=te[Ve];if(oe&&oe.setForMove){if(oe.previousTriggersValues&&oe.previousTriggersValues.has(D.triggerName)){let Tt=oe.previousTriggersValues.get(D.triggerName),be=this.statesByElement.get(D.element);if(be&&be.has(D.triggerName)){let ar=be.get(D.triggerName);ar.value=Tt,be.set(D.triggerName,ar)}}F.destroy();return}}let Be=!d||!this.driver.containsElement(d,te),me=H.get(te),ut=p.get(te),G=this._buildInstruction(D,r,ut,me,Be);if(G.errors&&G.errors.length){z.push(G);return}if(Be){F.onStart(()=>tn(te,G.fromStyles)),F.onDestroy(()=>Ye(te,G.toStyles)),o.push(F);return}if(D.isFallbackTransition){F.onStart(()=>tn(te,G.fromStyles)),F.onDestroy(()=>Ye(te,G.toStyles)),o.push(F);return}let Kl=[];G.timelines.forEach(oe=>{oe.stretchStartingKeyframe=!0,this.disabledNodes.has(oe.element)||Kl.push(oe)}),G.timelines=Kl,r.append(te,G.timelines);let th={instruction:G,player:F,element:te};s.push(th),G.queriedElements.forEach(oe=>Ie(a,oe,[]).push(F)),G.preStyleProps.forEach((oe,Tt)=>{if(oe.size){let be=l.get(Tt);be||l.set(Tt,be=new Set),oe.forEach((ar,Si)=>be.add(Si))}}),G.postStyleProps.forEach((oe,Tt)=>{let be=u.get(Tt);be||u.set(Tt,be=new Set),oe.forEach((ar,Si)=>be.add(Si))})});if(z.length){let y=[];z.forEach(v=>{y.push(V_(v.triggerName,v.errors))}),xe.forEach(v=>v.destroy()),this.reportError(y)}let q=new Map,ee=new Map;s.forEach(y=>{let v=y.element;r.has(v)&&(ee.set(v,v),this._beforeAnimationBuild(y.player.namespaceId,y.instruction,q))}),o.forEach(y=>{let v=y.element;this._getPreviousPlayers(v,!1,y.namespaceId,y.triggerName,null).forEach(F=>{Ie(q,v,[]).push(F),F.destroy()})});let je=E.filter(y=>$p(y,l,u)),re=new Map;jp(re,this.driver,P,u,Ke).forEach(y=>{$p(y,l,u)&&je.push(y)});let St=new Map;f.forEach((y,v)=>{jp(St,this.driver,new Set(y),l,ui)}),je.forEach(y=>{let v=re.get(y),D=St.get(y);re.set(y,new Map([...v?.entries()??[],...D?.entries()??[]]))});let Ci=[],Ql=[],Zl={};s.forEach(y=>{let{element:v,player:D,instruction:F}=y;if(r.has(v)){if(c.has(v)){D.onDestroy(()=>Ye(v,F.toStyles)),D.disabled=!0,D.overrideTotalTime(F.totalTime),o.push(D);return}let te=Zl;if(ee.size>1){let me=v,ut=[];for(;me=me.parentNode;){let G=ee.get(me);if(G){te=G;break}ut.push(me)}ut.forEach(G=>ee.set(G,te))}let Be=this._buildAnimation(D.namespaceId,F,q,i,St,re);if(D.setRealPlayer(Be),te===Zl)Ci.push(D);else{let me=this.playersByElement.get(te);me&&me.length&&(D.parentPlayer=Ct(me)),o.push(D)}}else tn(v,F.fromStyles),D.onDestroy(()=>Ye(v,F.toStyles)),Ql.push(D),c.has(v)&&o.push(D)}),Ql.forEach(y=>{let v=i.get(y.element);if(v&&v.length){let D=Ct(v);y.setRealPlayer(D)}}),o.forEach(y=>{y.parentPlayer?y.syncPlayerEvents(y.parentPlayer):y.destroy()});for(let y=0;y<E.length;y++){let v=E[y],D=v[Ve];if(Mn(v,gi),D&&D.hasAnimation)continue;let F=[];if(a.size){let Be=a.get(v);Be&&Be.length&&F.push(...Be);let me=this.driver.query(v,Il,!0);for(let ut=0;ut<me.length;ut++){let G=a.get(me[ut]);G&&G.length&&F.push(...G)}}let te=F.filter(Be=>!Be.destroyed);te.length?SI(this,v,te):this.processLeaveNode(v)}return E.length=0,Ci.forEach(y=>{this.players.push(y),y.onDone(()=>{y.destroy();let v=this.players.indexOf(y);this.players.splice(v,1)}),y.play()}),Ci}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,n,r,o,i){let s=[];if(n){let a=this.playersByQueriedElement.get(e);a&&(s=a)}else{let a=this.playersByElement.get(e);if(a){let l=!i||i==nr;a.forEach(u=>{u.queued||!l&&u.triggerName!=o||s.push(u)})}}return(r||o)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||o&&o!=a.triggerName))),s}_beforeAnimationBuild(e,n,r){let o=n.triggerName,i=n.element,s=n.isRemovalTransition?void 0:e,a=n.isRemovalTransition?void 0:o;for(let l of n.timelines){let u=l.element,c=u!==i,d=Ie(r,u,[]);this._getPreviousPlayers(u,c,s,a,n.toState).forEach(f=>{let p=f.getRealPlayer();p.beforeDestroy&&p.beforeDestroy(),f.destroy(),d.push(f)})}tn(i,n.fromStyles)}_buildAnimation(e,n,r,o,i,s){let a=n.triggerName,l=n.element,u=[],c=new Set,d=new Set,h=n.timelines.map(p=>{let g=p.element;c.add(g);let E=g[Ve];if(E&&E.removedBeforeQueried)return new bt(p.duration,p.delay);let w=g!==l,P=TI((r.get(g)||wI).map(q=>q.getRealPlayer())).filter(q=>{let ee=q;return ee.element?ee.element===g:!1}),H=i.get(g),L=s.get(g),xe=zp(this._normalizer,p.keyframes,H,L),z=this._buildPlayer(p,xe,P);if(p.subTimeline&&o&&d.add(g),w){let q=new ir(e,a,g);q.setRealPlayer(z),u.push(q)}return z});u.forEach(p=>{Ie(this.playersByQueriedElement,p.element,[]).push(p),p.onDone(()=>II(this.playersByQueriedElement,p.element,p))}),c.forEach(p=>Ne(p,xp));let f=Ct(h);return f.onDestroy(()=>{c.forEach(p=>Mn(p,xp)),Ye(l,n.toStyles)}),d.forEach(p=>{Ie(o,p,[]).push(f)}),f}_buildPlayer(e,n,r){return n.length>0?this.driver.animate(e.element,n,e.duration,e.delay,e.easing,r):new bt(e.duration,e.delay)}},ir=class{namespaceId;triggerName;element;_player=new bt;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,n,r){this.namespaceId=e,this.triggerName=n,this.element=r}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((n,r)=>{n.forEach(o=>jl(e,r,void 0,o))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let n=this._player;n.triggerCallback&&e.onStart(()=>n.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,n){Ie(this._queuedCallbacks,e,[]).push(n)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let n=this._player;n.triggerCallback&&n.triggerCallback(e)}};function II(t,e,n){let r=t.get(e);if(r){if(r.length){let o=r.indexOf(n);r.splice(o,1)}r.length==0&&t.delete(e)}return r}function bI(t){return t??null}function pi(t){return t&&t.nodeType===1}function CI(t){return t=="start"||t=="done"}function Vp(t,e){let n=t.style.display;return t.style.display=e??"none",n}function jp(t,e,n,r,o){let i=[];n.forEach(l=>i.push(Vp(l)));let s=[];r.forEach((l,u)=>{let c=new Map;l.forEach(d=>{let h=e.computeStyle(u,d,o);c.set(d,h),(!h||h.length==0)&&(u[Ve]=_I,s.push(u))}),t.set(u,c)});let a=0;return n.forEach(l=>Vp(l,i[a++])),s}function Bp(t,e){let n=new Map;if(t.forEach(a=>n.set(a,[])),e.length==0)return n;let r=1,o=new Set(e),i=new Map;function s(a){if(!a)return r;let l=i.get(a);if(l)return l;let u=a.parentNode;return n.has(u)?l=u:o.has(u)?l=r:l=s(u),i.set(a,l),l}return e.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function Ne(t,e){t.classList?.add(e)}function Mn(t,e){t.classList?.remove(e)}function SI(t,e,n){Ct(n).onDone(()=>t.processLeaveNode(e))}function TI(t){let e=[];return Xp(t,e),e}function Xp(t,e){for(let n=0;n<t.length;n++){let r=t[n];r instanceof er?Xp(r.players,e):e.push(r)}}function MI(t,e){let n=Object.keys(t),r=Object.keys(e);if(n.length!=r.length)return!1;for(let o=0;o<n.length;o++){let i=n[o];if(!e.hasOwnProperty(i)||t[i]!==e[i])return!1}return!0}function $p(t,e,n){let r=n.get(t);if(!r)return!1;let o=e.get(t);return o?r.forEach(i=>o.add(i)):e.set(t,r),n.delete(t),!0}var _i=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,n)=>{};constructor(e,n,r){this._driver=n,this._normalizer=r,this._transitionEngine=new Pl(e.body,n,r),this._timelineEngine=new Fl(e.body,n,r),this._transitionEngine.onRemovalComplete=(o,i)=>this.onRemovalComplete(o,i)}registerTrigger(e,n,r,o,i){let s=e+"-"+o,a=this._triggerCache[s];if(!a){let l=[],u=[],c=ql(this._driver,i,l,u);if(l.length)throw T_(o,l);a=hI(o,c,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,o,a)}register(e,n){this._transitionEngine.register(e,n)}destroy(e,n){this._transitionEngine.destroy(e,n)}onInsert(e,n,r,o){this._transitionEngine.insertNode(e,n,r,o)}onRemove(e,n,r){this._transitionEngine.removeNode(e,n,r)}disableAnimations(e,n){this._transitionEngine.markElementAsDisabled(e,n)}process(e,n,r,o){if(r.charAt(0)=="@"){let[i,s]=Tp(r),a=o;this._timelineEngine.command(i,n,s,a)}else this._transitionEngine.trigger(e,n,r,o)}listen(e,n,r,o,i){if(r.charAt(0)=="@"){let[s,a]=Tp(r);return this._timelineEngine.listen(s,n,a,i)}return this._transitionEngine.listen(e,n,r,o,i)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function NI(t,e){let n=null,r=null;return Array.isArray(e)&&e.length?(n=El(e[0]),e.length>1&&(r=El(e[e.length-1]))):e instanceof Map&&(n=El(e)),n||r?new xI(t,n,r):null}var xI=(()=>{class t{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(n,r,o){this._element=n,this._startStyles=r,this._endStyles=o;let i=t.initialStylesByElement.get(n);i||t.initialStylesByElement.set(n,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&Ye(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Ye(this._element,this._initialStyles),this._endStyles&&(Ye(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(t.initialStylesByElement.delete(this._element),this._startStyles&&(tn(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(tn(this._element,this._endStyles),this._endStyles=null),Ye(this._element,this._initialStyles),this._state=3)}}return t})();function El(t){let e=null;return t.forEach((n,r)=>{AI(r)&&(e=e||new Map,e.set(r,n))}),e}function AI(t){return t==="display"||t==="position"}var Ii=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,n,r,o){this.element=e,this.keyframes=n,this.options=r,this._specialStyles=o,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let n=[];return e.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(e,n,r){return e.animate(this._convertKeyframesToObject(n),r)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,o)=>{o!=="offset"&&e.set(o,this._finished?r:zl(this.element,o))}),this.currentSnapshot=e}triggerCallback(e){let n=e==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Ll=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,n){return qp(e,n)}getParentElement(e){return $l(e)}query(e,n,r){return Gp(e,n,r)}computeStyle(e,n,r){return zl(e,n)}animate(e,n,r,o,i,s=[]){let a=o==0?"both":"forwards",l={duration:r,delay:o,fill:a};i&&(l.easing=i);let u=new Map,c=s.filter(f=>f instanceof Ii);K_(r,o)&&c.forEach(f=>{f.currentSnapshot.forEach((p,g)=>u.set(g,p))});let d=W_(n).map(f=>new Map(f));d=Y_(e,d,u);let h=NI(e,d);return new Ii(e,d,l,h)}};function cA(t,e){return t==="noop"?new _i(e,new Wp,new _l):new _i(e,new Ll,new Sl)}var Hp=class{_driver;_animationAst;constructor(e,n){this._driver=e;let r=[],i=ql(e,n,r,[]);if(r.length)throw C_(r);this._animationAst=i}buildTimelines(e,n,r,o,i){let s=Array.isArray(n)?Ap(n):n,a=Array.isArray(r)?Ap(r):r,l=[];i=i||new Nn;let u=Wl(this._driver,e,this._animationAst,Hl,gi,s,a,o,i,l);if(l.length)throw S_(l);return u}},hi="@",eh="@.disabled",bi=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,n,r,o){this.namespaceId=e,this.delegate=n,this.engine=r,this._onDestroy=o}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,n){return this.delegate.createElement(e,n)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,n){this.delegate.appendChild(e,n),this.engine.onInsert(this.namespaceId,n,e,!1)}insertBefore(e,n,r,o=!0){this.delegate.insertBefore(e,n,r),this.engine.onInsert(this.namespaceId,n,e,o)}removeChild(e,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(e,n){return this.delegate.selectRootElement(e,n)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,n,r,o){this.delegate.setAttribute(e,n,r,o)}removeAttribute(e,n,r){this.delegate.removeAttribute(e,n,r)}addClass(e,n){this.delegate.addClass(e,n)}removeClass(e,n){this.delegate.removeClass(e,n)}setStyle(e,n,r,o){this.delegate.setStyle(e,n,r,o)}removeStyle(e,n,r){this.delegate.removeStyle(e,n,r)}setProperty(e,n,r){n.charAt(0)==hi&&n==eh?this.disableAnimations(e,!!r):this.delegate.setProperty(e,n,r)}setValue(e,n){this.delegate.setValue(e,n)}listen(e,n,r,o){return this.delegate.listen(e,n,r,o)}disableAnimations(e,n){this.engine.disableAnimations(e,n)}},Vl=class extends bi{factory;constructor(e,n,r,o,i){super(n,r,o,i),this.factory=e,this.namespaceId=n}setProperty(e,n,r){n.charAt(0)==hi?n.charAt(1)=="."&&n==eh?(r=r===void 0?!0:!!r,this.disableAnimations(e,r)):this.engine.process(this.namespaceId,e,n.slice(1),r):this.delegate.setProperty(e,n,r)}listen(e,n,r,o){if(n.charAt(0)==hi){let i=RI(e),s=n.slice(1),a="";return s.charAt(0)!=hi&&([s,a]=OI(s)),this.engine.listen(this.namespaceId,i,s,a,l=>{let u=l._data||-1;this.factory.scheduleListenerCallback(u,r,l)})}return this.delegate.listen(e,n,r,o)}};function RI(t){switch(t){case"body":return document.body;case"document":return document;case"window":return window;default:return t}}function OI(t){let e=t.indexOf("."),n=t.substring(0,e),r=t.slice(e+1);return[n,r]}var Up=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,n,r){this.delegate=e,this.engine=n,this._zone=r,n.onRemovalComplete=(o,i)=>{i?.removeChild(null,o)}}createRenderer(e,n){let r="",o=this.delegate.createRenderer(e,n);if(!e||!n?.data?.animation){let u=this._rendererCache,c=u.get(o);if(!c){let d=()=>u.delete(o);c=new bi(r,o,this.engine,d),u.set(o,c)}return c}let i=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,e);let a=u=>{Array.isArray(u)?u.forEach(a):this.engine.registerTrigger(i,s,e,u.name,u)};return n.data.animation.forEach(a),new Vl(this,s,o,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,n,r){if(e>=0&&e<this._microtaskId){this._zone.run(()=>n(r));return}let o=this._animationCallbacksBuffer;o.length==0&&queueMicrotask(()=>{this._zone.run(()=>{o.forEach(i=>{let[s,a]=i;s(a)}),this._animationCallbacksBuffer=[]})}),o.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};export{fe as a,$e as b,FI as c,eu as d,tu as e,K as f,hh as g,k as h,qi as i,Gi as j,He as k,On as l,At as m,ze as n,bh as o,Ch as p,Sh as q,Je as r,Th as s,Ot as t,kh as u,Ft as v,Pn as w,$r as x,Lh as y,Vh as z,jh as A,kt as B,Bh as C,Bu as D,$h as E,Hh as F,Ln as G,Zi as H,Uh as I,Gh as J,Wh as K,Ki as L,Qh as M,Zh as N,Kh as O,Yh as P,Jh as Q,Xh as R,eg as S,m as T,ft as U,Pc as V,V as W,ma as X,_N as Y,O as Z,x as _,Y as $,S as aa,Ng as ba,Xc as ca,mt as da,td as ea,Hg as fa,IN as ga,bN as ha,CN as ia,SN as ja,xm as ka,vt as la,En as ma,Ho as na,pt as oa,ue as pa,wn as qa,TN as ra,Yt as sa,Jt as ta,MN as ua,NN as va,xN as wa,Ym as xa,Jm as ya,AN as za,RN as Aa,Uo as Ba,oy as Ca,iy as Da,qn as Ea,Yn as Fa,of as Ga,ON as Ha,FN as Ia,kN as Ja,PN as Ka,LN as La,sf as Ma,VN as Na,La as Oa,My as Pa,jN as Qa,BN as Ra,$N as Sa,ce as Ta,HN as Ua,vo as Va,Gt as Wa,wo as Xa,Ka as Ya,Tn as Za,wt as _a,ta as $a,ND as ab,GN as bb,Ja as cb,Ko as db,zf as eb,kD as fb,$D as gb,ZD as hb,WN as ib,JD as jb,XD as kb,QN as lb,eE as mb,tl as nb,nE as ob,oE as pb,In as qb,cE as rb,_E as sb,IE as tb,ZN as ub,np as vb,rp as wb,PE as xb,jE as yb,BE as zb,KN as Ab,HE as Bb,ZE as Cb,YN as Db,JN as Eb,XN as Fb,ex as Gb,tx as Hb,nx as Ib,rx as Jb,XE as Kb,ip as Lb,tw as Mb,ox as Nb,nw as Ob,ix as Pb,sx as Qb,ax as Rb,lx as Sb,ux as Tb,cx as Ub,dx as Vb,fx as Wb,cp as Xb,px as Yb,hx as Zb,Cw as _b,gx as $b,Sw as ac,mx as bc,yx as cc,rl as dc,zx as ec,fp as fc,dl as gc,qx as hc,ai as ic,Mw as jc,Gx as kc,Nw as lc,Wx as mc,Qx as nc,Zx as oc,Kx as pc,Yx as qc,Jx as rc,r_ as sc,Xx as tc,eA as uc,tA as vc,Dp as wc,$l as xc,H_ as yc,lA as zc,qp as Ac,Gp as Bc,Wp as Cc,Np as Dc,wl as Ec,_l as Fc,W_ as Gc,uA as Hc,K_ as Ic,Sl as Jc,_i as Kc,Ii as Lc,Ll as Mc,cA as Nc,Hp as Oc,bi as Pc,Vl as Qc,Up as Rc};
