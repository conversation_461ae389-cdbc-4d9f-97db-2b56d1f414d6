import './polyfills.server.mjs';
import{a as ie,b as Le,d as _c}from"./chunk-S6KH3LOX.mjs";function Gi(e,t){return Object.is(e,t)}var ne=null,Er=!1,Wi=1,Ie=Symbol("SIGNAL");function P(e){let t=ne;return ne=e,t}function Ic(){return ne}var Fn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function wr(e){if(Er)throw new Error("");if(ne===null)return;ne.consumerOnSignalRead(e);let t=ne.nextProducerIndex++;if(Cr(ne),t<ne.producerNode.length&&ne.producerNode[t]!==e&&kn(ne)){let n=ne.producerNode[t];Ir(n,ne.producerIndexOfThis[t])}ne.producerNode[t]!==e&&(ne.producerNode[t]=e,ne.producerIndexOfThis[t]=kn(ne)?bc(e,ne,t):0),ne.producerLastReadVersion[t]=e.version}function mg(){Wi++}function Ki(e){if(!(kn(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Wi)){if(!e.producerMustRecompute(e)&&!Zi(e)){qi(e);return}e.producerRecomputeValue(e),qi(e)}}function Cc(e){if(e.liveConsumerNode===void 0)return;let t=Er;Er=!0;try{for(let n of e.liveConsumerNode)n.dirty||yg(n)}finally{Er=t}}function Tc(){return ne?.consumerAllowSignalWrites!==!1}function yg(e){e.dirty=!0,Cc(e),e.consumerMarkedDirty?.(e)}function qi(e){e.dirty=!1,e.lastCleanEpoch=Wi}function _r(e){return e&&(e.nextProducerIndex=0),P(e)}function Qi(e,t){if(P(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(kn(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Ir(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Zi(e){Cr(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ki(n),r!==n.version))return!0}return!1}function Yi(e){if(Cr(e),kn(e))for(let t=0;t<e.producerNode.length;t++)Ir(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function bc(e,t,n){if(Sc(e),e.liveConsumerNode.length===0&&Mc(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=bc(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Ir(e,t){if(Sc(e),e.liveConsumerNode.length===1&&Mc(e))for(let r=0;r<e.producerNode.length;r++)Ir(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Cr(o),o.producerIndexOfThis[r]=t}}function kn(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Cr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Sc(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Mc(e){return e.producerNode!==void 0}function Nc(e){let t=Object.create(vg);t.computation=e;let n=()=>{if(Ki(t),wr(t),t.value===Dr)throw t.error;return t.value};return n[Ie]=t,n}var Ui=Symbol("UNSET"),zi=Symbol("COMPUTING"),Dr=Symbol("ERRORED"),vg=Le(ie({},Fn),{value:Ui,dirty:!0,error:null,equal:Gi,kind:"computed",producerMustRecompute(e){return e.value===Ui||e.value===zi},producerRecomputeValue(e){if(e.value===zi)throw new Error("Detected cycle in computations.");let t=e.value;e.value=zi;let n=_r(e),r,o=!1;try{r=e.computation(),P(null),o=t!==Ui&&t!==Dr&&r!==Dr&&e.equal(t,r)}catch(i){r=Dr,e.error=i}finally{Qi(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Eg(){throw new Error}var Ac=Eg;function xc(){Ac()}function Oc(e){Ac=e}var Dg=null;function Rc(e){let t=Object.create(Xi);t.value=e;let n=()=>(wr(t),t.value);return n[Ie]=t,n}function Tr(e,t){Tc()||xc(),e.equal(e.value,t)||(e.value=t,wg(e))}function Pc(e,t){Tc()||xc(),Tr(e,t(e.value))}var Xi=Le(ie({},Fn),{equal:Gi,value:void 0,kind:"signal"});function wg(e){e.version++,mg(),Cc(e),Dg?.()}function T(e){return typeof e=="function"}function cn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var br=cn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function xt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ee=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){t=i instanceof br?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{kc(i)}catch(s){t=t??[],s instanceof br?t=[...t,...s.errors]:t.push(s)}}if(t)throw new br(t)}}add(t){var n;if(t&&t!==this)if(this.closed)kc(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&xt(n,t)}remove(t){let{_finalizers:n}=this;n&&xt(n,t),t instanceof e&&t._removeParent(this)}};ee.EMPTY=(()=>{let e=new ee;return e.closed=!0,e})();var Ji=ee.EMPTY;function Sr(e){return e instanceof ee||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function kc(e){T(e)?e():e.unsubscribe()}var Ve={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var un={setTimeout(e,t,...n){let{delegate:r}=un;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=un;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Mr(e){un.setTimeout(()=>{let{onUnhandledError:t}=Ve;if(t)t(e);else throw e})}function Ln(){}var Fc=es("C",void 0,void 0);function Lc(e){return es("E",void 0,e)}function Vc(e){return es("N",e,void 0)}function es(e,t,n){return{kind:e,value:t,error:n}}var Ot=null;function dn(e){if(Ve.useDeprecatedSynchronousErrorHandling){let t=!Ot;if(t&&(Ot={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Ot;if(Ot=null,n)throw r}}else e()}function jc(e){Ve.useDeprecatedSynchronousErrorHandling&&Ot&&(Ot.errorThrown=!0,Ot.error=e)}var Rt=class extends ee{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Sr(t)&&t.add(this)):this.destination=Cg}static create(t,n,r){return new ht(t,n,r)}next(t){this.isStopped?ns(Vc(t),this):this._next(t)}error(t){this.isStopped?ns(Lc(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?ns(Fc,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},_g=Function.prototype.bind;function ts(e,t){return _g.call(e,t)}var rs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Nr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Nr(r)}else Nr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Nr(n)}}},ht=class extends Rt{constructor(t,n,r){super();let o;if(T(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ve.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&ts(t.next,i),error:t.error&&ts(t.error,i),complete:t.complete&&ts(t.complete,i)}):o=t}this.destination=new rs(o)}};function Nr(e){Ve.useDeprecatedSynchronousErrorHandling?jc(e):Mr(e)}function Ig(e){throw e}function ns(e,t){let{onStoppedNotification:n}=Ve;n&&un.setTimeout(()=>n(e,t))}var Cg={closed:!0,next:Ln,error:Ig,complete:Ln};var fn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function pe(e){return e}function Tg(...e){return os(e)}function os(e){return e.length===0?pe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Sg(n)?n:new ht(n,r,o);return dn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Bc(r),new r((o,i)=>{let s=new ht({next:a=>{try{n(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[fn](){return this}pipe(...n){return os(n)(this)}toPromise(n){return n=Bc(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Bc(e){var t;return(t=e??Ve.Promise)!==null&&t!==void 0?t:Promise}function bg(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function Sg(e){return e&&e instanceof Rt||bg(e)&&Sr(e)}function is(e){return T(e?.lift)}function M(e){return t=>{if(is(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function b(e,t,n,r,o){return new ss(e,t,n,r,o)}var ss=class extends Rt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function as(){return M((e,t)=>{let n=null;e._refCount++;let r=b(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var ls=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,is(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new ee;let n=this.getSubject();t.add(this.source.subscribe(b(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=ee.EMPTY)}return t}refCount(){return as()(this)}};var Hc=cn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Qe=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Ar(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Hc}next(n){dn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){dn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){dn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ji:(this.currentObservers=null,i.push(n),new ee(()=>{this.currentObservers=null,xt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new Ar(t,n),e})(),Ar=class extends Qe{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ji}};var Vn=class extends Qe{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var cs={now(){return(cs.delegate||Date).now()},delegate:void 0};var xr=class extends ee{constructor(t,n){super()}schedule(t,n=0){return this}};var jn={setInterval(e,t,...n){let{delegate:r}=jn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=jn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Or=class extends xr{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return jn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&jn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,xt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var pn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};pn.now=cs.now;var Rr=class extends pn{constructor(t,n=pn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Bn=new Rr(Or),$c=Bn;var Pt=new F(e=>e.complete());function Pr(e){return e&&T(e.schedule)}function us(e){return e[e.length-1]}function kr(e){return T(us(e))?e.pop():void 0}function Ze(e){return Pr(us(e))?e.pop():void 0}function Uc(e,t){return typeof us(e)=="number"?e.pop():t}function qc(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?i(u.value):o(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}function zc(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function kt(e){return this instanceof kt?(this.v=e,this):new kt(e)}function Gc(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(v,E){i.push([f,g,v,E])>1||l(f,g)})},h&&(o[f]=h(o[f])))}function l(f,h){try{c(r[f](h))}catch(g){p(i[0][3],g)}}function c(f){f.value instanceof kt?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){l("next",f)}function d(f){l("throw",f)}function p(f,h){f(h),i.shift(),i.length&&l(i[0][0],i[0][1])}}function Wc(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof zc=="function"?zc(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,l){s=e[i](s),o(a,l,s.done,s.value)})}}function o(i,s,a,l){Promise.resolve(l).then(function(c){i({value:c,done:a})},s)}}var Fr=e=>e&&typeof e.length=="number"&&typeof e!="function";function Lr(e){return T(e?.then)}function Vr(e){return T(e[fn])}function jr(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function Br(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Mg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Hr=Mg();function $r(e){return T(e?.[Hr])}function Ur(e){return Gc(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield kt(n.read());if(o)return yield kt(void 0);yield yield kt(r)}}finally{n.releaseLock()}})}function zr(e){return T(e?.getReader)}function z(e){if(e instanceof F)return e;if(e!=null){if(Vr(e))return Ng(e);if(Fr(e))return Ag(e);if(Lr(e))return xg(e);if(jr(e))return Kc(e);if($r(e))return Og(e);if(zr(e))return Rg(e)}throw Br(e)}function Ng(e){return new F(t=>{let n=e[fn]();if(T(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ag(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function xg(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Mr)})}function Og(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Kc(e){return new F(t=>{Pg(e,t).catch(n=>t.error(n))})}function Rg(e){return Kc(Ur(e))}function Pg(e,t){var n,r,o,i;return qc(this,void 0,void 0,function*(){try{for(n=Wc(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Ee(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function qr(e,t=0){return M((n,r)=>{n.subscribe(b(r,o=>Ee(r,e,()=>r.next(o),t),()=>Ee(r,e,()=>r.complete(),t),o=>Ee(r,e,()=>r.error(o),t)))})}function Gr(e,t=0){return M((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Qc(e,t){return z(e).pipe(Gr(t),qr(t))}function Zc(e,t){return z(e).pipe(Gr(t),qr(t))}function Yc(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Xc(e,t){return new F(n=>{let r;return Ee(n,t,()=>{r=e[Hr](),Ee(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function Wr(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{Ee(n,t,()=>{let r=e[Symbol.asyncIterator]();Ee(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Jc(e,t){return Wr(Ur(e),t)}function eu(e,t){if(e!=null){if(Vr(e))return Qc(e,t);if(Fr(e))return Yc(e,t);if(Lr(e))return Zc(e,t);if(jr(e))return Wr(e,t);if($r(e))return Xc(e,t);if(zr(e))return Jc(e,t)}throw Br(e)}function Ye(e,t){return t?eu(e,t):z(e)}function kg(...e){let t=Ze(e);return Ye(e,t)}function Fg(e,t){let n=T(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function Lg(e){return!!e&&(e instanceof F||T(e.lift)&&T(e.subscribe))}var ot=cn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Vg(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new ht({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new ot)}});e.subscribe(i)})}function tu(e){return e instanceof Date&&!isNaN(e)}function Ft(e,t){return M((n,r)=>{let o=0;n.subscribe(b(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:jg}=Array;function Bg(e,t){return jg(t)?e(...t):e(t)}function Kr(e){return Ft(t=>Bg(e,t))}var{isArray:Hg}=Array,{getPrototypeOf:$g,prototype:Ug,keys:zg}=Object;function Qr(e){if(e.length===1){let t=e[0];if(Hg(t))return{args:t,keys:null};if(qg(t)){let n=zg(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function qg(e){return e&&typeof e=="object"&&$g(e)===Ug}function Zr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Gg(...e){let t=Ze(e),n=kr(e),{args:r,keys:o}=Qr(e);if(r.length===0)return Ye([],t);let i=new F(Wg(r,t,o?s=>Zr(o,s):pe));return n?i.pipe(Kr(n)):i}function Wg(e,t,n=pe){return r=>{nu(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let l=0;l<o;l++)nu(t,()=>{let c=Ye(e[l],t),u=!1;c.subscribe(b(r,d=>{i[l]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function nu(e,t,n){e?Ee(n,e,t):t()}function ru(e,t,n,r,o,i,s,a){let l=[],c=0,u=0,d=!1,p=()=>{d&&!l.length&&!c&&t.complete()},f=g=>c<r?h(g):l.push(g),h=g=>{i&&t.next(g),c++;let v=!1;z(n(g,u++)).subscribe(b(t,E=>{o?.(E),i?f(E):t.next(E)},()=>{v=!0},void 0,()=>{if(v)try{for(c--;l.length&&c<r;){let E=l.shift();s?Ee(t,s,()=>h(E)):h(E)}p()}catch(E){t.error(E)}}))};return e.subscribe(b(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Lt(e,t,n=1/0){return T(t)?Lt((r,o)=>Ft((i,s)=>t(r,i,o,s))(z(e(r,o))),n):(typeof t=="number"&&(n=t),M((r,o)=>ru(r,o,e,n)))}function Hn(e=1/0){return Lt(pe,e)}function ou(){return Hn(1)}function Yr(...e){return ou()(Ye(e,Ze(e)))}function Kg(e){return new F(t=>{z(e()).subscribe(t)})}function Qg(...e){let t=kr(e),{args:n,keys:r}=Qr(e),o=new F(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let d=!1;z(n[u]).subscribe(b(i,p=>{d||(d=!0,c--),a[u]=p},()=>l--,void 0,()=>{(!l||!d)&&(c||i.next(r?Zr(r,a):a),i.complete())}))}});return t?o.pipe(Kr(t)):o}function iu(e=0,t,n=$c){let r=-1;return t!=null&&(Pr(t)?n=t:r=t),new F(o=>{let i=tu(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Zg(...e){let t=Ze(e),n=Uc(e,1/0),r=e;return r.length?r.length===1?z(r[0]):Hn(n)(Ye(r,t)):Pt}function Vt(e,t){return M((n,r)=>{let o=0;n.subscribe(b(r,i=>e.call(t,i,o++)&&r.next(i)))})}function su(e){return M((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},l=()=>{i=null,s&&n.complete()};t.subscribe(b(n,c=>{r=!0,o=c,i||z(e(c)).subscribe(i=b(n,a,l))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Yg(e,t=Bn){return su(()=>iu(e,t))}function au(e){return M((t,n)=>{let r=null,o=!1,i;r=t.subscribe(b(n,void 0,void 0,s=>{i=z(e(s,au(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function lu(e,t,n,r,o){return(i,s)=>{let a=n,l=t,c=0;i.subscribe(b(s,u=>{let d=c++;l=a?e(l,u,d):(a=!0,u),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}function Xg(e,t){return T(t)?Lt(e,t,1):Lt(e,1)}function Jg(e,t=Bn){return M((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function l(){let c=s+e,u=t.now();if(u<c){o=this.schedule(void 0,c-u),r.add(o);return}a()}n.subscribe(b(r,c=>{i=c,s=t.now(),o||(o=t.schedule(l,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function $n(e){return M((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function ds(e){return e<=0?()=>Pt:M((t,n)=>{let r=0;t.subscribe(b(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function em(e,t=pe){return e=e??tm,M((n,r)=>{let o,i=!0;n.subscribe(b(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function tm(e,t){return e===t}function Xr(e=nm){return M((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function nm(){return new ot}function rm(e){return M((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function om(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Vt((o,i)=>e(o,i,r)):pe,ds(1),n?$n(t):Xr(()=>new ot))}function fs(e){return e<=0?()=>Pt:M((t,n)=>{let r=[];t.subscribe(b(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function im(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Vt((o,i)=>e(o,i,r)):pe,fs(1),n?$n(t):Xr(()=>new ot))}function sm(e,t){return M(lu(e,t,arguments.length>=2,!0))}function am(e){return Vt((t,n)=>e<=n)}function lm(...e){let t=Ze(e);return M((n,r)=>{(t?Yr(e,n,t):Yr(e,n)).subscribe(r)})}function cm(e,t){return M((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(b(r,l=>{o?.unsubscribe();let c=0,u=i++;z(e(l,u)).subscribe(o=b(r,d=>r.next(t?t(l,d,u,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function um(e){return M((t,n)=>{z(e).subscribe(b(n,()=>n.complete(),Ln)),!n.closed&&t.subscribe(n)})}function dm(e,t,n){let r=T(e)||t||n?{next:e,error:t,complete:n}:e;return r?M((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(b(i,l=>{var c;(c=r.next)===null||c===void 0||c.call(r,l),i.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),i.complete()},l=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,l),i.error(l)},()=>{var l,c;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):pe}var ps={JSACTION:"jsaction"};var Z={AUXCLICK:"auxclick",CHANGE:"change",CLICK:"click",CLICKMOD:"clickmod",CLICKONLY:"clickonly",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEUP:"mouseup",MOUSEDOWN:"mousedown",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",MOUSEMOVE:"mousemove",POINTERUP:"pointerup",POINTERDOWN:"pointerdown",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",POINTERMOVE:"pointermove",POINTERCANCEL:"pointercancel",GOTPOINTERCAPTURE:"gotpointercapture",LOSTPOINTERCAPTURE:"lostpointercapture",ERROR:"error",LOAD:"load",UNLOAD:"unload",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",INPUT:"input",SCROLL:"scroll",TOGGLE:"toggle",CUSTOM:"_custom"},Tx=[Z.MOUSEENTER,Z.MOUSELEAVE,"pointerenter","pointerleave"],fm=[Z.CLICK,Z.DBLCLICK,Z.FOCUSIN,Z.FOCUSOUT,Z.KEYDOWN,Z.KEYUP,Z.KEYPRESS,Z.MOUSEOVER,Z.MOUSEOUT,Z.SUBMIT,Z.TOUCHSTART,Z.TOUCHEND,Z.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],cu=[Z.FOCUS,Z.BLUR,Z.ERROR,Z.LOAD,Z.TOGGLE],uu=e=>cu.indexOf(e)>=0,pm=fm.concat(cu),du=e=>pm.indexOf(e)>=0,hm=3,gm=13,mm=32,Ce={MAC_ENTER:hm,ENTER:gm,SPACE:mm};var bx=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);var Sx=typeof navigator<"u"&&!/Opera/.test(navigator.userAgent)&&/WebKit/.test(navigator.userAgent),Mx=typeof navigator<"u"&&(/MSIE/.test(navigator.userAgent)||/Trident/.test(navigator.userAgent)),Nx=typeof navigator<"u"&&!/Opera|WebKit/.test(navigator.userAgent)&&/Gecko/.test(navigator.product);var Ax={Enter:Ce.ENTER," ":Ce.SPACE},xx={A:Ce.ENTER,BUTTON:0,CHECKBOX:Ce.SPACE,COMBOBOX:Ce.ENTER,FILE:0,GRIDCELL:Ce.ENTER,LINK:Ce.ENTER,LISTBOX:Ce.ENTER,MENU:0,MENUBAR:0,MENUITEM:0,MENUITEMCHECKBOX:0,MENUITEMRADIO:0,OPTION:0,RADIO:Ce.SPACE,RADIOGROUP:Ce.SPACE,RESET:0,SUBMIT:0,SWITCH:Ce.SPACE,TAB:0,TREE:Ce.ENTER,TREEITEM:Ce.ENTER};var Ox=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent);var Rx=Z.CLICK;var pd="https://g.co/ng/security#xss",m=class extends Error{code;constructor(t,n){super(hd(t,n)),this.code=t}};function hd(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var gd=Symbol("InputSignalNode#UNSET"),vm=Le(ie({},Xi),{transformFn:void 0,applyValueToInputSignal(e,t){Tr(e,t)}});function md(e,t){let n=Object.create(vm);n.value=e,n.transformFn=t?.transform;function r(){if(wr(n),n.value===gd)throw new m(-950,!1);return n.value}return r[Ie]=n,r}function nr(e){return{toString:e}.toString()}var Jr="__parameters__";function Em(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function yd(e,t,n){return nr(()=>{let r=Em(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(l,c,u){let d=l.hasOwnProperty(Jr)?l[Jr]:Object.defineProperty(l,Jr,{value:[]})[Jr];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var it=globalThis;function B(e){for(let t in e)if(e[t]===B)return t;throw Error("Could not find renamed property on target object.")}function Dm(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function me(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(me).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function xs(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var wm=B({__forward_ref__:B});function vd(e){return e.__forward_ref__=vd,e.toString=function(){return me(this())},e}function he(e){return Ed(e)?e():e}function Ed(e){return typeof e=="function"&&e.hasOwnProperty(wm)&&e.__forward_ref__===vd}function V(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function La(e){return{providers:e.providers||[],imports:e.imports||[]}}function Wo(e){return fu(e,Dd)||fu(e,wd)}function yO(e){return Wo(e)!==null}function fu(e,t){return e.hasOwnProperty(t)?e[t]:null}function _m(e){let t=e&&(e[Dd]||e[wd]);return t||null}function pu(e){return e&&(e.hasOwnProperty(hu)||e.hasOwnProperty(Im))?e[hu]:null}var Dd=B({\u0275prov:B}),hu=B({\u0275inj:B}),wd=B({ngInjectableDef:B}),Im=B({ngInjectorDef:B}),C=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=V({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function _d(e){return e&&!!e.\u0275providers}var Cm=B({\u0275cmp:B}),Tm=B({\u0275dir:B}),bm=B({\u0275pipe:B}),Sm=B({\u0275mod:B}),po=B({\u0275fac:B}),qn=B({__NG_ELEMENT_ID__:B}),gu=B({__NG_ENV_ID__:B});function rr(e){return typeof e=="string"?e:e==null?"":String(e)}function Mm(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():rr(e)}function Nm(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new m(-200,e)}function Va(e,t){throw new m(-201,!1)}var x=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(x||{}),Os;function Id(){return Os}function De(e){let t=Os;return Os=e,t}function Cd(e,t,n){let r=Wo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&x.Optional)return null;if(t!==void 0)return t;Va(e,"Injector")}var Am={},Wn=Am,Rs="__NG_DI_FLAG__",ho="ngTempTokenPath",xm="ngTokenPath",Om=/\n/gm,Rm="\u0275",mu="__source",vn;function Pm(){return vn}function gt(e){let t=vn;return vn=e,t}function km(e,t=x.Default){if(vn===void 0)throw new m(-203,!1);return vn===null?Cd(e,void 0,t):vn.get(e,t&x.Optional?null:void 0,t)}function Y(e,t=x.Default){return(Id()||km)(he(e),t)}function S(e,t=x.Default){return Y(e,Ko(t))}function Ko(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ps(e){let t=[];for(let n=0;n<e.length;n++){let r=he(e[n]);if(Array.isArray(r)){if(r.length===0)throw new m(900,!1);let o,i=x.Default;for(let s=0;s<r.length;s++){let a=r[s],l=Fm(a);typeof l=="number"?l===-1?o=a.token:i|=l:o=a}t.push(Y(o,i))}else t.push(Y(r))}return t}function Td(e,t){return e[Rs]=t,e.prototype[Rs]=t,e}function Fm(e){return e[Rs]}function Lm(e,t,n,r){let o=e[ho];throw t[mu]&&o.unshift(t[mu]),e.message=Vm(`
`+e.message,o,n,r),e[xm]=o,e[ho]=null,e}function Vm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Rm?e.slice(2):e;let o=me(t);if(Array.isArray(t))o=t.map(me).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):me(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Om,`
  `)}`}var jm=Td(yd("Optional"),8);var Bm=Td(yd("SkipSelf"),4);function $t(e,t){let n=e.hasOwnProperty(po);return n?e[po]:null}function Hm(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function $m(e){return e.flat(Number.POSITIVE_INFINITY)}function ja(e,t){e.forEach(n=>Array.isArray(n)?ja(n,t):t(n))}function bd(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function go(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Um(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Ba(e,t,n){let r=or(e,t);return r>=0?e[r|1]=n:(r=~r,Um(e,r,t,n)),r}function hs(e,t){let n=or(e,t);if(n>=0)return e[n|1]}function or(e,t){return zm(e,t,1)}function zm(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Dn={},ge=[],mo=new C(""),Sd=new C("",-1),Md=new C(""),yo=class{get(t,n=Wn){if(n===Wn){let r=new Error(`NullInjectorError: No provider for ${me(t)}!`);throw r.name="NullInjectorError",r}return n}};function Nd(e,t){let n=e[Sm]||null;if(!n&&t===!0)throw new Error(`Type ${me(e)} does not have '\u0275mod' property.`);return n}function Je(e){return e[Cm]||null}function Ad(e){return e[Tm]||null}function xd(e){return e[bm]||null}function qm(e){let t=Je(e)||Ad(e)||xd(e);return t!==null&&t.standalone}function Gm(e){return{\u0275providers:e}}function Wm(...e){return{\u0275providers:Od(!0,e),\u0275fromNgModule:!0}}function Od(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ja(t,s=>{let a=s;ks(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Rd(o,i),n}function Rd(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Ha(o,i=>{t(i,r)})}}function ks(e,t,n,r){if(e=he(e),!e)return!1;let o=null,i=pu(e),s=!i&&Je(e);if(!i&&!s){let l=e.ngModule;if(i=pu(l),i)o=l;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)ks(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{ja(i.imports,u=>{ks(u,t,n,r)&&(c||=[],c.push(u))})}finally{}c!==void 0&&Rd(c,t)}if(!a){let c=$t(o)||(()=>new o);t({provide:o,useFactory:c,deps:ge},o),t({provide:Md,useValue:o,multi:!0},o),t({provide:mo,useValue:()=>Y(o),multi:!0},o)}let l=i.providers;if(l!=null&&!a){let c=e;Ha(l,u=>{t(u,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Ha(e,t){for(let n of e)_d(n)&&(n=n.\u0275providers),Array.isArray(n)?Ha(n,t):t(n)}var Km=B({provide:String,useValue:B});function Pd(e){return e!==null&&typeof e=="object"&&Km in e}function Qm(e){return!!(e&&e.useExisting)}function Zm(e){return!!(e&&e.useFactory)}function wn(e){return typeof e=="function"}function Ym(e){return!!e.useClass}var kd=new C(""),so={},Xm={},gs;function Qo(){return gs===void 0&&(gs=new yo),gs}var Dt=class{},Kn=class extends Dt{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Ls(t,s=>this.processProvider(s)),this.records.set(Sd,hn(void 0,this)),o.has("environment")&&this.records.set(Dt,hn(void 0,this));let i=this.records.get(kd);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Md,ge,x.Self))}destroy(){zn(this),this._destroyed=!0;let t=P(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),P(t)}}onDestroy(t){return zn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){zn(this);let n=gt(this),r=De(void 0),o;try{return t()}finally{gt(n),De(r)}}get(t,n=Wn,r=x.Default){if(zn(this),t.hasOwnProperty(gu))return t[gu](this);r=Ko(r);let o,i=gt(this),s=De(void 0);try{if(!(r&x.SkipSelf)){let l=this.records.get(t);if(l===void 0){let c=ry(t)&&Wo(t);c&&this.injectableDefInScope(c)?l=hn(Fs(t),so):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let a=r&x.Self?Qo():this.parent;return n=r&x.Optional&&n===Wn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[ho]=a[ho]||[]).unshift(me(t)),i)throw a;return Lm(a,t,"R3InjectorError",this.source)}else throw a}finally{De(s),gt(i)}}resolveInjectorInitializers(){let t=P(null),n=gt(this),r=De(void 0),o;try{let i=this.get(mo,ge,x.Self);for(let s of i)s()}finally{gt(n),De(r),P(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(me(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=he(t);let n=wn(t)?t:he(t&&t.provide),r=ey(t);if(!wn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=hn(void 0,so,!0),o.factory=()=>Ps(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=P(null);try{return n.value===so&&(n.value=Xm,n.value=n.factory()),typeof n.value=="object"&&n.value&&ny(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{P(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=he(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Fs(e){let t=Wo(e),n=t!==null?t.factory:$t(e);if(n!==null)return n;if(e instanceof C)throw new m(204,!1);if(e instanceof Function)return Jm(e);throw new m(204,!1)}function Jm(e){if(e.length>0)throw new m(204,!1);let n=_m(e);return n!==null?()=>n.factory(e):()=>new e}function ey(e){if(Pd(e))return hn(void 0,e.useValue);{let t=Fd(e);return hn(t,so)}}function Fd(e,t,n){let r;if(wn(e)){let o=he(e);return $t(o)||Fs(o)}else if(Pd(e))r=()=>he(e.useValue);else if(Zm(e))r=()=>e.useFactory(...Ps(e.deps||[]));else if(Qm(e))r=()=>Y(he(e.useExisting));else{let o=he(e&&(e.useClass||e.provide));if(ty(e))r=()=>new o(...Ps(e.deps));else return $t(o)||Fs(o)}return r}function zn(e){if(e.destroyed)throw new m(205,!1)}function hn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function ty(e){return!!e.deps}function ny(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function ry(e){return typeof e=="function"||typeof e=="object"&&e instanceof C}function Ls(e,t){for(let n of e)Array.isArray(n)?Ls(n,t):n&&_d(n)?Ls(n.\u0275providers,t):t(n)}function Ld(e,t){e instanceof Kn&&zn(e);let n,r=gt(e),o=De(void 0);try{return t()}finally{gt(r),De(o)}}function Vd(){return Id()!==void 0||Pm()!=null}function jd(e){if(!Vd())throw new m(-203,!1)}function oy(e){let t=it.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function iy(e){return typeof e=="function"}var se=0,w=1,I=2,ae=3,He=4,ze=5,vo=6,Qn=7,xe=8,Ut=9,st=10,G=11,Zn=12,yu=13,An=14,$e=15,zt=16,gn=17,at=18,Zo=19,Bd=20,yt=21,ms=22,Eo=23,Te=24,U=25,$a=1;var qt=7,Do=8,_n=9,we=10;function vt(e){return Array.isArray(e)&&typeof e[$a]=="object"}function Oe(e){return Array.isArray(e)&&e[$a]===!0}function Ua(e){return(e.flags&4)!==0}function ir(e){return e.componentOffset>-1}function Yo(e){return(e.flags&1)===1}function wt(e){return!!e.template}function Yn(e){return(e[I]&512)!==0}function Hd(e){return(e.type&16)===16}function sy(e){return(e[I]&32)===32}function sr(e){return(e[I]&256)===256}var Vs=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function $d(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var ay=(()=>{let e=()=>Ud;return e.ngInherit=!0,e})();function Ud(e){return e.type.prototype.ngOnChanges&&(e.setInput=cy),ly}function ly(){let e=qd(this),t=e?.current;if(t){let n=e.previous;if(n===Dn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function cy(e,t,n,r,o){let i=this.declaredInputs[r],s=qd(e)||uy(e,{previous:Dn,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[i];a[i]=new Vs(c&&c.currentValue,n,l===Dn),$d(e,t,o,n)}var zd="__ngSimpleChanges__";function qd(e){return e[zd]||null}function uy(e,t){return e[zd]=t}var vu=null;var Ae=function(e,t,n){vu?.(e,t,n)},Gd="svg",dy="math";function q(e){for(;Array.isArray(e);)e=e[se];return e}function Wd(e){for(;Array.isArray(e);){if(typeof e[$a]=="object")return e;e=e[se]}return null}function Kd(e,t){return q(t[e])}function Re(e,t){return q(t[e.index])}function Qd(e,t){return e.data[t]}function Zd(e,t){return e[t]}function Tt(e,t){let n=t[e];return vt(n)?n:n[se]}function fy(e){return(e[I]&4)===4}function za(e){return(e[I]&128)===128}function py(e){return Oe(e[ae])}function In(e,t){return t==null?null:e[t]}function Yd(e){e[gn]=0}function qa(e){e[I]&1024||(e[I]|=1024,za(e)&&Jo(e))}function hy(e,t){for(;e>0;)t=t[An],e--;return t}function Xo(e){return!!(e[I]&9216||e[Te]?.dirty)}function js(e){e[st].changeDetectionScheduler?.notify(9),e[I]&64&&(e[I]|=1024),Xo(e)&&Jo(e)}function Jo(e){e[st].changeDetectionScheduler?.notify(0);let t=Gt(e);for(;t!==null&&!(t[I]&8192||(t[I]|=8192,!za(t)));)t=Gt(t)}function Xd(e,t){if(sr(e))throw new m(911,!1);e[yt]===null&&(e[yt]=[]),e[yt].push(t)}function gy(e,t){if(e[yt]===null)return;let n=e[yt].indexOf(t);n!==-1&&e[yt].splice(n,1)}function Gt(e){let t=e[ae];return Oe(t)?t[ae]:t}function Jd(e){return e[Qn]??=[]}function ef(e){return e.cleanup??=[]}function my(e,t,n,r){let o=Jd(t);o.push(n),e.firstCreatePass&&ef(e).push(r,o.length-1)}var N={lFrame:uf(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Bs=!1;function yy(){return N.lFrame.elementDepthCount}function vy(){N.lFrame.elementDepthCount++}function Ey(){N.lFrame.elementDepthCount--}function tf(){return N.bindingsEnabled}function Dy(){return N.skipHydrationRootTNode!==null}function wy(e){return N.skipHydrationRootTNode===e}function _y(){N.skipHydrationRootTNode=null}function O(){return N.lFrame.lView}function X(){return N.lFrame.tView}function vO(e){return N.lFrame.contextLView=e,e[xe]}function EO(e){return N.lFrame.contextLView=null,e}function le(){let e=nf();for(;e!==null&&e.type===64;)e=e.parent;return e}function nf(){return N.lFrame.currentTNode}function Iy(){let e=N.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function tn(e,t){let n=N.lFrame;n.currentTNode=e,n.isParent=t}function Ga(){return N.lFrame.isParent}function rf(){N.lFrame.isParent=!1}function Cy(){return N.lFrame.contextLView}function of(){return Bs}function Eu(e){let t=Bs;return Bs=e,t}function Wa(){let e=N.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Ty(e){return N.lFrame.bindingIndex=e}function ar(){return N.lFrame.bindingIndex++}function sf(e){let t=N.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function by(){return N.lFrame.inI18n}function Sy(e,t){let n=N.lFrame;n.bindingIndex=n.bindingRootIndex=e,Hs(t)}function My(){return N.lFrame.currentDirectiveIndex}function Hs(e){N.lFrame.currentDirectiveIndex=e}function Ny(e){let t=N.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function af(){return N.lFrame.currentQueryIndex}function Ka(e){N.lFrame.currentQueryIndex=e}function Ay(e){let t=e[w];return t.type===2?t.declTNode:t.type===1?e[ze]:null}function lf(e,t,n){if(n&x.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&x.Host);)if(o=Ay(i),o===null||(i=i[An],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=N.lFrame=cf();return r.currentTNode=t,r.lView=e,!0}function Qa(e){let t=cf(),n=e[w];N.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function cf(){let e=N.lFrame,t=e===null?null:e.child;return t===null?uf(e):t}function uf(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function df(){let e=N.lFrame;return N.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var ff=df;function Za(){let e=df();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function xy(e){return(N.lFrame.contextLView=hy(e,N.lFrame.contextLView))[xe]}function nn(){return N.lFrame.selectedIndex}function Wt(e){N.lFrame.selectedIndex=e}function ei(){let e=N.lFrame;return Qd(e.tView,e.selectedIndex)}function DO(){N.lFrame.currentNamespace=Gd}function Oy(){return N.lFrame.currentNamespace}var pf=!0;function ti(){return pf}function ni(e){pf=e}function Ry(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ud(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function ri(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ao(e,t,n){hf(e,t,3,n)}function lo(e,t,n,r){(e[I]&3)===n&&hf(e,t,n,r)}function ys(e,t){let n=e[I];(n&3)===t&&(n&=16383,n+=1,e[I]=n)}function hf(e,t,n,r){let o=r!==void 0?e[gn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let l=o;l<s;l++)if(typeof t[l+1]=="number"){if(a=t[l],r!=null&&a>=r)break}else t[l]<0&&(e[gn]+=65536),(a<i||i==-1)&&(Py(e,n,t,l),e[gn]=(e[gn]&**********)+l+2),l++}function Du(e,t){Ae(4,e,t);let n=P(null);try{t.call(e)}finally{P(n),Ae(5,e,t)}}function Py(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[I]>>14<e[gn]>>16&&(e[I]&3)===t&&(e[I]+=16384,Du(a,i)):Du(a,i)}var En=-1,Kt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function ky(e){return e instanceof Kt}function gf(e){return e!=null&&typeof e=="object"&&(e.insertBeforeIndex===null||typeof e.insertBeforeIndex=="number"||Array.isArray(e.insertBeforeIndex))}function Fy(e){return!!(e.type&128)}function Ly(e){return(e.flags&8)!==0}function Vy(e){return(e.flags&16)!==0}function jy(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];By(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function mf(e){return e===3||e===4||e===6}function By(e){return e.charCodeAt(0)===64}function Cn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?wu(e,n,o,null,t[++r]):wu(e,n,o,null,null))}}return e}function wu(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var vs={},$s=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Ko(r);let o=this.injector.get(t,vs,r);return o!==vs||n===vs?o:this.parentInjector.get(t,n,r)}};function yf(e){return e!==En}function wo(e){return e&32767}function Hy(e){return e>>16}function _o(e,t){let n=Hy(e),r=t;for(;n>0;)r=r[An],n--;return r}var Us=!0;function Io(e){let t=Us;return Us=e,t}var $y=256,vf=$y-1,Ef=5,Uy=0,Xe={};function zy(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(qn)&&(r=n[qn]),r==null&&(r=n[qn]=Uy++);let o=r&vf,i=1<<o;t.data[e+(o>>Ef)]|=i}function Co(e,t){let n=Df(e,t);if(n!==-1)return n;let r=t[w];r.firstCreatePass&&(e.injectorIndex=t.length,Es(r.data,e),Es(t,null),Es(r.blueprint,null));let o=Ya(e,t),i=e.injectorIndex;if(yf(o)){let s=wo(o),a=_o(o,t),l=a[w].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|l[s+c]}return t[i+8]=o,i}function Es(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Df(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ya(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Tf(o),r===null)return En;if(n++,o=o[An],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return En}function zs(e,t,n){zy(e,t,n)}function qy(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(mf(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function wf(e,t,n){if(n&x.Optional||e!==void 0)return e;Va(t,"NodeInjector")}function _f(e,t,n,r){if(n&x.Optional&&r===void 0&&(r=null),!(n&(x.Self|x.Host))){let o=e[Ut],i=De(void 0);try{return o?o.get(t,r,n&x.Optional):Cd(t,r,n&x.Optional)}finally{De(i)}}return wf(r,t,n)}function If(e,t,n,r=x.Default,o){if(e!==null){if(t[I]&2048&&!(r&x.Self)){let s=Qy(e,t,n,r,Xe);if(s!==Xe)return s}let i=Cf(e,t,n,r,Xe);if(i!==Xe)return i}return _f(t,n,r,o)}function Cf(e,t,n,r,o){let i=Wy(n);if(typeof i=="function"){if(!lf(t,e,r))return r&x.Host?wf(o,n,r):_f(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&x.Optional))Va(n);else return s}finally{ff()}}else if(typeof i=="number"){let s=null,a=Df(e,t),l=En,c=r&x.Host?t[$e][ze]:null;for((a===-1||r&x.SkipSelf)&&(l=a===-1?Ya(e,t):t[a+8],l===En||!Iu(r,!1)?a=-1:(s=t[w],a=wo(l),t=_o(l,t)));a!==-1;){let u=t[w];if(_u(i,a,u.data)){let d=Gy(a,t,n,s,r,c);if(d!==Xe)return d}l=t[a+8],l!==En&&Iu(r,t[w].data[a+8]===c)&&_u(i,a,t)?(s=u,a=wo(l),t=_o(l,t)):a=-1}}return o}function Gy(e,t,n,r,o,i){let s=t[w],a=s.data[e+8],l=r==null?ir(a)&&Us:r!=s&&(a.type&3)!==0,c=o&x.Host&&i===a,u=co(a,s,n,l,c);return u!==null?Qt(t,s,u,a):Xe}function co(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,l=e.directiveStart,c=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:c;for(let f=d;f<p;f++){let h=s[f];if(f<l&&n===h||f>=l&&h.type===n)return f}if(o){let f=s[l];if(f&&wt(f)&&f.type===n)return l}return null}function Qt(e,t,n,r){let o=e[n],i=t.data;if(ky(o)){let s=o;s.resolving&&Nm(Mm(i[n]));let a=Io(s.canSeeViewProviders);s.resolving=!0;let l,c=s.injectImpl?De(s.injectImpl):null,u=lf(e,r,x.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Ry(n,i[n],t)}finally{c!==null&&De(c),Io(a),s.resolving=!1,ff()}}return o}function Wy(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(qn)?e[qn]:void 0;return typeof t=="number"?t>=0?t&vf:Ky:t}function _u(e,t,n){let r=1<<e;return!!(n[t+(e>>Ef)]&r)}function Iu(e,t){return!(e&x.Self)&&!(e&x.Host&&t)}var Ht=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return If(this._tNode,this._lView,t,Ko(r),n)}};function Ky(){return new Ht(le(),O())}function wO(e){return nr(()=>{let t=e.prototype.constructor,n=t[po]||qs(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[po]||qs(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function qs(e){return Ed(e)?()=>{let t=qs(he(e));return t&&t()}:$t(e)}function Qy(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[I]&2048&&!(s[I]&512);){let a=Cf(i,s,n,r|x.Self,Xe);if(a!==Xe)return a;let l=i.parent;if(!l){let c=s[Bd];if(c){let u=c.get(n,Xe,r);if(u!==Xe)return u}l=Tf(s),s=s[An]}i=l}return o}function Tf(e){let t=e[w],n=t.type;return n===2?t.declTNode:n===1?e[ze]:null}function Zy(e){return qy(le(),e)}function Cu(e,t=null,n=null,r){let o=bf(e,t,n,r);return o.resolveInjectorInitializers(),o}function bf(e,t=null,n=null,r,o=new Set){let i=[n||ge,Wm(e)];return r=r||(typeof e=="object"?void 0:me(e)),new Kn(i,t||Qo(),r||null,o)}var Ue=class e{static THROW_IF_NOT_FOUND=Wn;static NULL=new yo;static create(t,n){if(Array.isArray(t))return Cu({name:""},n,t,"");{let r=t.name??"";return Cu({name:r},t.parent,t.providers,r)}}static \u0275prov=V({token:e,providedIn:"any",factory:()=>Y(Sd)});static __NG_ELEMENT_ID__=-1};var Yy=new C("");Yy.__NG_ELEMENT_ID__=e=>{let t=le();if(t===null)throw new m(204,!1);if(t.type&2)return t.value;if(e&x.Optional)return null;throw new m(204,!1)};var Sf=!1,Xa=(()=>{class e{static __NG_ELEMENT_ID__=Xy;static __NG_ENV_ID__=n=>n}return e})(),Gs=class extends Xa{_lView;constructor(t){super(),this._lView=t}onDestroy(t){return Xd(this._lView,t),()=>gy(this._lView,t)}};function Xy(){return new Gs(O())}var Zt=class{},Ja=new C("",{providedIn:"root",factory:()=>!1});var Mf=new C(""),Nf=new C(""),oi=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Vn(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})();var Ws=class extends Qe{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Vd()&&(this.destroyRef=S(Xa,{optional:!0})??void 0,this.pendingTasks=S(oi,{optional:!0})??void 0)}emit(t){let n=P(null);try{super.next(t)}finally{P(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let l=t;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof ee&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},mt=Ws;function To(...e){}function Af(e){let t,n;function r(){e=To;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Tu(e){return queueMicrotask(()=>e()),()=>{e=To}}var el="isAngularZone",bo=el+"_ID",Jy=0,fe=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new mt(!1);onMicrotaskEmpty=new mt(!1);onStable=new mt(!1);onError=new mt(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Sf}=t;if(typeof Zone>"u")throw new m(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,nv(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(el)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new m(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new m(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,ev,To,To);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},ev={};function tl(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function tv(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Af(()=>{e.callbackScheduled=!1,Ks(e),e.isCheckStableRunning=!0,tl(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Ks(e)}function nv(e){let t=()=>{tv(e)},n=Jy++;e._inner=e._inner.fork({name:"angular",properties:{[el]:!0,[bo]:n,[bo+n]:!0},onInvokeTask:(r,o,i,s,a,l)=>{if(rv(l))return r.invokeTask(i,s,a,l);try{return bu(e),r.invokeTask(i,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Su(e)}},onInvoke:(r,o,i,s,a,l,c)=>{try{return bu(e),r.invoke(i,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!ov(l)&&t(),Su(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Ks(e),tl(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Ks(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function bu(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Su(e){e._nesting--,tl(e)}var So=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new mt;onMicrotaskEmpty=new mt;onStable=new mt;onError=new mt;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function rv(e){return xf(e,"__ignore_ng_zone__")}function ov(e){return xf(e,"__scheduler_tick__")}function xf(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function iv(e="zone.js",t){return e==="noop"?new So:e==="zone.js"?new fe(t):e}var Tn=class{_console=console;handleError(t){this._console.error("ERROR",t)}},sv=new C("",{providedIn:"root",factory:()=>{let e=S(fe),t=S(Tn);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Mu(e,t){return md(e,t)}function av(e){return md(gd,e)}var _O=(Mu.required=av,Mu);function lv(){return xn(le(),O())}function xn(e,t){return new rn(Re(e,t))}var rn=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=lv}return e})();function cv(e){return e instanceof rn?e.nativeElement:e}var Nu=new Set;function on(e){Nu.has(e)||(Nu.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function uv(e){return typeof e=="function"&&e[Ie]!==void 0}function IO(e,t){on("NgSignals");let n=Rc(e),r=n[Ie];return t?.equal&&(r.equal=t.equal),n.set=o=>Tr(r,o),n.update=o=>Pc(r,o),n.asReadonly=dv.bind(n),n}function dv(){let e=this[Ie];if(e.readonlyFn===void 0){let t=()=>this();t[Ie]=e,e.readonlyFn=t}return e.readonlyFn}function Of(e){return uv(e)&&typeof e.set=="function"}function fv(){return this._results[Symbol.iterator]()}var Qs=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new Qe}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=$m(t);(this._changesDetected=!Hm(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=fv},Mo="ngSkipHydration",pv="ngskiphydration";function Rf(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===pv)return!0}return!1}function Xn(e){return(e.flags&128)===128}function No(e){if(Xn(e))return!0;let t=e.parent;for(;t;){if(Xn(e)||Rf(t))return!0;t=t.parent}return!1}function hv(e){return Xn(e)||Rf(e)||No(e)}var Pf=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pf||{}),kf=new Map,gv=0;function mv(){return gv++}function yv(e){kf.set(e[Zo],e)}function Zs(e){kf.delete(e[Zo])}var Au="__ngContext__";function _t(e,t){vt(t)?(e[Au]=t[Zo],yv(t)):e[Au]=t}function Ff(e){return Vf(e[Zn])}function Lf(e){return Vf(e[He])}function Vf(e){for(;e!==null&&!Oe(e);)e=e[He];return e}var Ys;function CO(e){Ys=e}function jf(){if(Ys!==void 0)return Ys;if(typeof document<"u")return document;throw new m(210,!1)}var Bf=new C("",{providedIn:"root",factory:()=>vv}),vv="ng",Ev=new C(""),Hf=new C("",{providedIn:"platform",factory:()=>"unknown"});var TO=new C(""),bO=new C("",{providedIn:"root",factory:()=>jf().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function Dv(){let e=new $f;return S(Hf)==="browser"&&(e.store=wv(jf(),S(Bf))),e}var $f=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:Dv});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();function wv(e,t){let n=e.getElementById(t+"-state");if(n?.textContent)try{return JSON.parse(n.textContent)}catch(r){console.warn("Exception while restoring TransferState for app "+t,r)}return{}}var Uf="h",zf="b",_v="f",Iv="n",xu="e",Ou="t",Ru="c",Pu="x",Ds="r",ku="i",Fu="n",ws="d",Lu="l",Cv="di",Vu="s",Tv="p",bv="t";var SO=new C(""),qf=!1,Sv=new C("",{providedIn:"root",factory:()=>qf}),Mv=new C(""),Nv=new C(""),Av=!1,xv=new C("");var nl=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(nl||{}),ii=new C("");var mn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(mn||{}),Gf=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ov=[mn.EarlyRead,mn.Write,mn.MixedReadWrite,mn.Read],Rv=(()=>{class e{ngZone=S(fe);scheduler=S(Zt);errorHandler=S(Tn,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){S(ii,{optional:!0})}execute(){this.executing=!0;for(let n of Ov)for(let r of this.sequences)if(!(r.erroredOrDestroyed||!r.hooks[n]))try{r.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>r.hooks[n](r.pipelinedValue),r.snapshot))}catch(o){r.erroredOrDestroyed=!0,this.errorHandler?.handleError(o)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(8),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(7))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(nl.AFTER_NEXT_RENDER,n):n()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})(),Xs=class{impl;hooks;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i=null){this.impl=t,this.hooks=n,this.once=r,this.snapshot=i,this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function Pv(e,t){!t?.injector&&jd(Pv);let n=t?.injector??S(Ue);return Kf}function kv(e,t){!t?.injector&&jd(kv);let n=t?.injector??S(Ue);return Kf}function Fv(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Wf(e,t,n,r){let o=t.get(Gf);o.impl??=t.get(Rv);let i=t.get(ii,null,{optional:!0}),s=n?.phase??mn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Xa):null,l=new Xs(o.impl,Fv(e,s),r,a,i?.snapshot(null));return o.impl.register(l),l}var Kf={destroy(){}};var ju=1;function rl(e){return e+1}function Lv(e,t){let n=e[w],r=rl(t.index);return e[r]}function Qf(e,t){let n=rl(t.index);return e.data[n]}function Vv(e){return e!==null&&typeof e=="object"&&typeof e.primaryTmplIndex=="number"}function jv(e,t){let n=null,r=rl(t.index);return U<r&&r<e.bindingStartIndex&&(n=Qf(e,t)),!!n&&Vv(n)}var Bv=["click","keydown"],Hv=["mouseenter","mouseover","focusin"];var $v="ngb";function Zf(e,t,n=null){if(t.length===0||e.nodeType!==Node.ELEMENT_NODE)return;let r=e.getAttribute(ps.JSACTION),o=t.reduce((s,a)=>(r?.indexOf(a)??-1)===-1?s+a+":;":s,"");e.setAttribute(ps.JSACTION,`${r??""}${o}`);let i=n??"";i!==""&&o.length>0&&e.setAttribute($v,i)}var Uv="__nghData__",zv=Uv,qv="__nghDeferData__",Gv=qv,Yf="ngh",MO="nghm",Wv=()=>null;function ol(e,t,n=!1){return Wv(e,t,n)}function Kv(e){let t=e._lView;return t[w].type===2?null:(Yn(t)&&(t=t[U]),t)}function Qv(e){return e.get(xv,!1,{optional:!0})}function Xf(e,t){let n=t,r=e.corruptedTextNodes;n.textContent===""?r.set(n,"ngetn"):n.nextSibling?.nodeType===Node.TEXT_NODE&&r.set(n,"ngtns")}function Zv(e){let t=[];return e!==null&&(e.has(4)&&t.push(...Hv),e.has(3)&&t.push(...Bv)),t}function Jf(e,t){let n=e.contentQueries;if(n!==null){let r=P(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ka(i),a.contentQueries(2,t[s],s)}}}finally{P(r)}}}function Js(e,t,n){Ka(0);let r=P(null);try{t(e,n)}finally{P(r)}}function il(e,t,n){if(Ua(t)){let r=P(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{P(r)}}}var bn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(bn||{}),eo;function Yv(){if(eo===void 0&&(eo=null,it.trustedTypes))try{eo=it.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return eo}function si(e){return Yv()?.createHTML(e)||e}var to;function Xv(){if(to===void 0&&(to=null,it.trustedTypes))try{to=it.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return to}function Bu(e){return Xv()?.createScriptURL(e)||e}var lt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${pd})`}},ea=class extends lt{getTypeName(){return"HTML"}},ta=class extends lt{getTypeName(){return"Style"}},na=class extends lt{getTypeName(){return"Script"}},ra=class extends lt{getTypeName(){return"URL"}},oa=class extends lt{getTypeName(){return"ResourceURL"}};function lr(e){return e instanceof lt?e.changingThisBreaksApplicationSecurity:e}function ep(e,t){let n=Jv(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${pd})`)}return n===t}function Jv(e){return e instanceof lt&&e.getTypeName()||null}function NO(e){return new ea(e)}function AO(e){return new ta(e)}function xO(e){return new na(e)}function OO(e){return new ra(e)}function RO(e){return new oa(e)}function eE(e){let t=new sa(e);return tE()?new ia(t):t}var ia=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(si(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},sa=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=si(t),n}};function tE(){try{return!!new window.DOMParser().parseFromString(si(""),"text/html")}catch{return!1}}var nE=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function tp(e){return e=String(e),e.match(nE)?e:"unsafe:"+e}function ct(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function cr(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var np=ct("area,br,col,hr,img,wbr"),rp=ct("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),op=ct("rp,rt"),rE=cr(op,rp),oE=cr(rp,ct("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),iE=cr(op,ct("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Hu=cr(np,oE,iE,rE),ip=ct("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),sE=ct("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),aE=ct("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),lE=cr(ip,sE,aE),cE=ct("script,style,template"),aa=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=fE(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=dE(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=$u(t).toLowerCase();if(!Hu.hasOwnProperty(n))return this.sanitizedSomething=!0,!cE.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!lE.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;ip[a]&&(l=tp(l)),this.buf.push(" ",s,'="',Uu(l),'"')}return this.buf.push(">"),!0}endElement(t){let n=$u(t).toLowerCase();Hu.hasOwnProperty(n)&&!np.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Uu(t))}};function uE(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function dE(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw sp(t);return t}function fE(e){let t=e.firstChild;if(t&&uE(e,t))throw sp(t);return t}function $u(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function sp(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var pE=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,hE=/([^\#-~ |!])/g;function Uu(e){return e.replace(/&/g,"&amp;").replace(pE,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(hE,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var no;function PO(e,t){let n=null;try{no=no||eE(e);let r=t?String(t):"";n=no.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=no.getInertBodyElement(r)}while(r!==i);let a=new aa().sanitizeChildren(zu(n)||n);return si(a)}finally{if(n){let r=zu(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function zu(e){return"content"in e&&gE(e)?e.content:null}function gE(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var sl=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(sl||{});function mE(e){let t=ap();return t?t.sanitize(sl.URL,e)||"":ep(e,"URL")?lr(e):tp(rr(e))}function yE(e){let t=ap();if(t)return Bu(t.sanitize(sl.RESOURCE_URL,e)||"");if(ep(e,"ResourceURL"))return Bu(lr(e));throw new m(904,!1)}function vE(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?yE:mE}function kO(e,t,n){return vE(t,n)(e)}function ap(){let e=O();return e&&e[st].sanitizer}var EE=/^>|^->|<!--|-->|--!>|<!-$/g,DE=/(<|>)/g,wE="\u200B$1\u200B";function _E(e){return e.replace(EE,t=>t.replace(DE,wE))}function FO(e){return e.ownerDocument}function lp(e){return e instanceof Function?e():e}var It=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(It||{});function IE(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var cp="ng-template";function CE(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&IE(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(al(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function al(e){return e.type===4&&e.value!==cp}function TE(e,t,n){let r=e.type===4&&!n?cp:e.value;return t===r}function bE(e,t,n){let r=4,o=e.attrs,i=o!==null?NE(o):0,s=!1;for(let a=0;a<t.length;a++){let l=t[a];if(typeof l=="number"){if(!s&&!je(r)&&!je(l))return!1;if(s&&je(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!TE(e,l,n)||l===""&&t.length===1){if(je(r))return!1;s=!0}}else if(r&8){if(o===null||!CE(e,o,l,n)){if(je(r))return!1;s=!0}}else{let c=t[++a],u=SE(l,o,al(e),n);if(u===-1){if(je(r))return!1;s=!0;continue}if(c!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&c!==d){if(je(r))return!1;s=!0}}}}return je(r)||s}function je(e){return(e&1)===0}function SE(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return AE(t,e)}function ME(e,t,n=!1){for(let r=0;r<t.length;r++)if(bE(e,t[r],n))return!0;return!1}function NE(e){for(let t=0;t<e.length;t++){let n=e[t];if(mf(n))return t}return e.length}function AE(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function qu(e,t){return e?":not("+t.trim()+")":t}function xE(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!je(s)&&(t+=qu(i,o),o=""),r=s,i=i||!je(r);n++}return o!==""&&(t+=qu(i,o)),t}function OE(e){return e.map(xE).join(",")}function RE(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!je(o))break;o=i}r++}return{attrs:t,classes:n}}var tt={};function LO(e=1){up(X(),O(),nn()+e,!1)}function up(e,t,n,r){if(!r)if((t[I]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ao(t,i,n)}else{let i=e.preOrderHooks;i!==null&&lo(t,i,0,n)}Wt(n)}function ye(e,t=x.Default){let n=O();if(n===null)return Y(e,t);let r=le();return If(r,n,he(e),t)}function VO(){let e="invalid";throw new Error(e)}function dp(e,t,n,r,o,i){let s=P(null);try{let a=null;o&It.SignalBased&&(a=t[r][Ie]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&It.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):$d(t,a,r,i)}finally{P(s)}}function PE(e,t){return e.createText(t)}function kE(e,t,n){e.setValue(t,n)}function FE(e,t){return e.createComment(_E(t))}function fp(e,t,n){return e.createElement(t,n)}function Ao(e,t,n,r,o){e.insertBefore(t,n,r,o)}function pp(e,t,n){e.appendChild(t,n)}function Gu(e,t,n,r,o){r!==null?Ao(e,t,n,r,o):pp(e,t,n)}function LE(e,t,n){e.removeChild(null,t,n)}function VE(e,t,n){e.setAttribute(t,"style",n)}function jE(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function hp(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&jy(e,t,r),o!==null&&jE(e,t,o),i!==null&&VE(e,t,i)}function ai(e,t,n,r,o,i,s,a,l,c,u){let d=t.blueprint.slice();return d[se]=o,d[I]=r|4|128|8|64|1024,(c!==null||e&&e[I]&2048)&&(d[I]|=2048),Yd(d),d[ae]=d[An]=e,d[xe]=n,d[st]=s||e&&e[st],d[G]=a||e&&e[G],d[Ut]=l||e&&e[Ut]||null,d[ze]=i,d[Zo]=mv(),d[vo]=u,d[Bd]=c,d[$e]=t.type==2?e[$e]:d,d}function gp(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function mp(e,t,n,r,o){let i=nn(),s=r&2;try{Wt(-1),s&&t.length>U&&up(e,t,U,!1),Ae(s?2:0,o),n(r,o)}finally{Wt(i),Ae(s?3:1,o)}}function ll(e,t,n){tf()&&(QE(e,t,n,Re(n,t)),(n.flags&64)===64&&Ep(e,t,n))}function cl(e,t,n=Re){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function yp(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=ul(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function ul(e,t,n,r,o,i,s,a,l,c,u){let d=U+r,p=d+o,f=BE(d,p),h=typeof c=="function"?c():c;return f[w]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:h,incompleteFirstPass:!1,ssrId:u}}function BE(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:tt);return n}function HE(e,t,n,r){let i=r.get(Sv,qf)||n===bn.ShadowDom,s=e.selectRootElement(t,i);return $E(s),s}function $E(e){UE(e)}var UE=()=>null;function Wu(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,l=It.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?Ku(r,n,c,a,l):Ku(r,n,c,a)}return r}function Ku(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function zE(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],l=null,c=null;for(let u=r;u<o;u++){let d=i[u],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;l=Wu(0,d.inputs,u,l,f),c=Wu(1,d.outputs,u,c,h);let g=l!==null&&s!==null&&!al(t)?sD(l,u,s):null;a.push(g)}l!==null&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}function qE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function dl(e,t,n,r,o,i,s,a){let l=Re(t,n),c=t.inputs,u;!a&&c!=null&&(u=c[r])?(pl(e,n,u,r,o),ir(t)&&GE(n,t.index)):t.type&3?(r=qE(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(l,r,o)):t.type&12}function GE(e,t){let n=Tt(t,e);n[I]&16||(n[I]|=64)}function fl(e,t,n,r){if(tf()){let o=r===null?null:{"":-1},i=YE(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&vp(e,t,n,s,o,a),o&&XE(n,r,o)}n.mergedAttrs=Cn(n.mergedAttrs,n.attrs)}function vp(e,t,n,r,o,i){for(let c=0;c<r.length;c++)zs(Co(n,t),e,r[c].type);eD(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=gp(e,t,r.length,null);for(let c=0;c<r.length;c++){let u=r[c];n.mergedAttrs=Cn(n.mergedAttrs,u.hostAttrs),tD(e,n,t,l,u),JE(l,u,o),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}zE(e,n,i)}function WE(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;KE(s)!=a&&s.push(a),s.push(n,r,i)}}function KE(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function QE(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;ir(n)&&nD(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Co(n,t),_t(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let l=e.data[a],c=Qt(t,e,a,n);if(_t(c,t),s!==null&&iD(t,a-o,c,l,n,s),wt(l)){let u=Tt(n.index,t);u[xe]=Qt(t,e,a,n)}}}function Ep(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=My();try{Wt(i);for(let a=r;a<o;a++){let l=e.data[a],c=t[a];Hs(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&ZE(l,c)}}finally{Wt(-1),Hs(s)}}function ZE(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function YE(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(ME(t,s.selectors,!1))if(r||(r=[]),wt(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let l=a.length;la(e,t,l)}else r.unshift(s),la(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function la(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function XE(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new m(-301,!1);r.push(t[o],i)}}}function JE(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;wt(t)&&(n[""]=e)}}function eD(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function tD(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=$t(o.type,!0)),s=new Kt(i,wt(o),ye);e.blueprint[r]=s,n[r]=s,WE(e,t,r,gp(e,n,o.hostVars,tt),o)}function Dp(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function nD(e,t,n){let r=Re(t,e),o=yp(n),i=e[st].rendererFactory,s=li(e,ai(e,o,null,Dp(n),r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=s}function rD(e,t,n,r,o,i){let s=Re(e,t);oD(t[G],s,i,e.value,n,r,o)}function oD(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?rr(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function iD(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],c=s[a++],u=s[a++],d=s[a++];dp(r,n,l,c,u,d)}}function sD(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function wp(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function li(e,t){return e[Zn]?e[yu][He]=t:e[Zn]=t,e[yu]=t,t}function _p(e,t){let n=e[Ut],r=n?n.get(Tn,null):null;r&&r.handleError(t)}function pl(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],l=n[i++],c=t[s],u=e.data[s];dp(u,c,r,a,l,o)}}function aD(e,t){let n=Tt(t,e),r=n[w];lD(r,n);let o=n[se];o!==null&&n[vo]===null&&(n[vo]=ol(o,n[Ut])),hl(r,n,n[xe])}function lD(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function hl(e,t,n){Qa(t);try{let r=e.viewQuery;r!==null&&Js(1,r,n);let o=e.template;o!==null&&mp(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[at]?.finishViewCreation(e),e.staticContentQueries&&Jf(e,t),e.staticViewQueries&&Js(2,e.viewQuery,n);let i=e.components;i!==null&&cD(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[I]&=-5,Za()}}function cD(e,t){for(let n=0;n<t.length;n++)aD(e,t[n])}var xo=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(xo||{}),uD;function gl(e,t){return uD(e,t)}function yn(e,t,n,r,o){if(r!=null){let i,s=!1;Oe(r)?i=r:vt(r)&&(s=!0,r=r[se]);let a=q(r);e===0&&n!==null?o==null?pp(t,n,a):Ao(t,n,a,o||null,!0):e===1&&n!==null?Ao(t,n,a,o||null,!0):e===2?LE(t,a,s):e===3&&t.destroyNode(a),i!=null&&_D(t,e,i,n,o)}}function dD(e,t){Ip(e,t),t[se]=null,t[ze]=null}function fD(e,t,n,r,o,i){r[se]=o,r[ze]=t,ui(e,r,n,1,o,i)}function Ip(e,t){t[st].changeDetectionScheduler?.notify(10),ui(e,t,t[G],2,null,null)}function pD(e){let t=e[Zn];if(!t)return _s(e[w],e);for(;t;){let n=null;if(vt(t))n=t[Zn];else{let r=t[we];r&&(n=r)}if(!n){for(;t&&!t[He]&&t!==e;)vt(t)&&_s(t[w],t),t=t[ae];t===null&&(t=e),vt(t)&&_s(t[w],t),n=t&&t[He]}t=n}}function hD(e,t,n,r){let o=we+r,i=n.length;r>0&&(n[o-1][He]=t),r<i-we?(t[He]=n[o],bd(n,we+r,t)):(n.push(t),t[He]=null),t[ae]=n;let s=t[zt];s!==null&&n!==s&&Cp(s,t);let a=t[at];a!==null&&a.insertView(e),js(t),t[I]|=128}function Cp(e,t){let n=e[_n],r=t[ae];if(vt(r))e[I]|=2;else{let o=r[ae][$e];t[$e]!==o&&(e[I]|=2)}n===null?e[_n]=[t]:n.push(t)}function ml(e,t){let n=e[_n],r=n.indexOf(t);n.splice(r,1)}function ca(e,t){if(e.length<=we)return;let n=we+t,r=e[n];if(r){let o=r[zt];o!==null&&o!==e&&ml(o,r),t>0&&(e[n-1][He]=r[He]);let i=go(e,we+t);dD(r[w],r);let s=i[at];s!==null&&s.detachView(i[w]),r[ae]=null,r[He]=null,r[I]&=-129}return r}function Tp(e,t){if(sr(t))return;let n=t[G];n.destroyNode&&ui(e,t,n,3,null,null),pD(t)}function _s(e,t){if(sr(t))return;let n=P(null);try{t[I]&=-129,t[I]|=256,t[Te]&&Yi(t[Te]),mD(e,t),gD(e,t),t[w].type===1&&t[G].destroy();let r=t[zt];if(r!==null&&Oe(t[ae])){r!==t[ae]&&ml(r,t);let o=t[at];o!==null&&o.detachView(e)}Zs(t)}finally{P(n)}}function gD(e,t){let n=e.cleanup,r=t[Qn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Qn]=null);let o=t[yt];if(o!==null){t[yt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Eo];if(i!==null){t[Eo]=null;for(let s of i)s.destroy()}}function mD(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Kt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],l=i[s+1];Ae(4,a,l);try{l.call(a)}finally{Ae(5,a,l)}}else{Ae(4,o,i);try{i.call(o)}finally{Ae(5,o,i)}}}}}function bp(e,t,n){return yD(e,t.parent,n)}function yD(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[se];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===bn.None||i===bn.Emulated)return null}return Re(r,n)}}function vD(e,t,n){return DD(e,t,n)}function ED(e,t,n){return e.type&40?Re(e,n):null}var DD=ED,Qu;function ci(e,t,n,r){let o=bp(e,r,t),i=t[G],s=r.parent||t[ze],a=vD(s,r,t);if(o!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)Gu(i,o,n[l],a,!1);else Gu(i,o,n,a,!1);Qu!==void 0&&Qu(i,r,t,n,o)}function Bt(e,t){if(t!==null){let n=t.type;if(n&3)return Re(t,e);if(n&4)return ua(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Bt(e,r);{let o=e[t.index];return Oe(o)?ua(-1,o):q(o)}}else{if(n&128)return Bt(e,t.next);if(n&32)return gl(t,e)()||q(e[t.index]);{let r=Sp(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Gt(e[$e]);return Bt(o,r)}else return Bt(e,t.next)}}}return null}function Sp(e,t){if(t!==null){let r=e[$e][ze],o=t.projection;return r.projection[o]}return null}function ua(e,t){let n=we+e+1;if(n<t.length){let r=t[n],o=r[w].firstChild;if(o!==null)return Bt(r,o)}return t[qt]}function yl(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&t===0&&(a&&_t(q(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)yl(e,t,n.child,r,o,i,!1),yn(t,e,o,a,i);else if(l&32){let c=gl(n,r),u;for(;u=c();)yn(t,e,o,u,i);yn(t,e,o,a,i)}else l&16?wD(e,t,r,n,o,i):yn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ui(e,t,n,r,o,i){yl(n,r,e.firstChild,t,o,i,!1)}function wD(e,t,n,r,o,i){let s=n[$e],l=s[ze].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];yn(t,e,o,u,i)}else{let c=l,u=s[ae];Xn(r)&&(c.flags|=128),yl(e,t,c,u,o,i,!0)}}function _D(e,t,n,r,o){let i=n[qt],s=q(n);i!==s&&yn(t,e,r,i,o);for(let a=we;a<n.length;a++){let l=n[a];ui(l[w],l,e,t,r,i)}}function ID(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:xo.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=xo.Important),e.setStyle(n,r,o,i))}}function CD(e,t,n,r){let o=P(null);try{let i=t.tView,a=e[I]&4096?4096:16,l=ai(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];l[zt]=c;let u=e[at];return u!==null&&(l[at]=u.createEmbeddedView(i)),hl(i,l,n),l}finally{P(o)}}function Zu(e,t){return!t||t.firstChild===null||Xn(e)}function TD(e,t,n,r=!0){let o=t[w];if(hD(o,t,e,n),r){let s=ua(n,e),a=t[G],l=a.parentNode(e[qt]);l!==null&&fD(o,e[ze],a,t,l,s)}let i=t[vo];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Jn(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(q(i)),Oe(i)&&vl(i,r);let s=n.type;if(s&8)Jn(e,t,n.child,r);else if(s&32){let a=gl(n,t),l;for(;l=a();)r.push(l)}else if(s&16){let a=Sp(t,n);if(Array.isArray(a))r.push(...a);else{let l=Gt(t[$e]);Jn(l[w],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function vl(e,t){for(let n=we;n<e.length;n++){let r=e[n],o=r[w].firstChild;o!==null&&Jn(r[w],r,o,t)}e[qt]!==e[se]&&t.push(e[qt])}var Mp=[];function bD(e){return e[Te]??SD(e)}function SD(e){let t=Mp.pop()??Object.create(ND);return t.lView=e,t}function MD(e){e.lView[Te]!==e&&(e.lView=null,Mp.push(e))}var ND=Le(ie({},Fn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Jo(e.lView)},consumerOnSignalRead(){this.lView[Te]=this}});function AD(e){let t=e[Te]??Object.create(xD);return t.lView=e,t}var xD=Le(ie({},Fn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Gt(e.lView);for(;t&&!Np(t[w]);)t=Gt(t);t&&qa(t)},consumerOnSignalRead(){this.lView[Te]=this}});function Np(e){return e.type!==2}function Ap(e){if(e[Eo]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Eo])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[I]&8192)}}var OD=100;function xp(e,t=!0,n=0){let o=e[st].rendererFactory,i=!1;i||o.begin?.();try{RD(e,n)}catch(s){throw t&&_p(e,s),s}finally{i||o.end?.()}}function RD(e,t){let n=of();try{Eu(!0),da(e,t);let r=0;for(;Xo(e);){if(r===OD)throw new m(103,!1);r++,da(e,1)}}finally{Eu(n)}}function PD(e,t,n,r){if(sr(t))return;let o=t[I],i=!1,s=!1;Qa(t);let a=!0,l=null,c=null;i||(Np(e)?(c=bD(t),l=_r(c)):Ic()===null?(a=!1,c=AD(t),l=_r(c)):t[Te]&&(Yi(t[Te]),t[Te]=null));try{Yd(t),Ty(e.bindingStartIndex),n!==null&&mp(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&ao(t,f,null)}else{let f=e.preOrderHooks;f!==null&&lo(t,f,0,null),ys(t,0)}if(s||kD(t),Ap(t),Op(t,0),e.contentQueries!==null&&Jf(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&ao(t,f)}else{let f=e.contentHooks;f!==null&&lo(t,f,1),ys(t,1)}LD(e,t);let d=e.components;d!==null&&Pp(t,d,0);let p=e.viewQuery;if(p!==null&&Js(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&ao(t,f)}else{let f=e.viewHooks;f!==null&&lo(t,f,2),ys(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ms]){for(let f of t[ms])f();t[ms]=null}i||(t[I]&=-73)}catch(u){throw i||Jo(t),u}finally{c!==null&&(Qi(c,l),a&&MD(c)),Za()}}function Op(e,t){for(let n=Ff(e);n!==null;n=Lf(n))for(let r=we;r<n.length;r++){let o=n[r];Rp(o,t)}}function kD(e){for(let t=Ff(e);t!==null;t=Lf(t)){if(!(t[I]&2))continue;let n=t[_n];for(let r=0;r<n.length;r++){let o=n[r];qa(o)}}}function FD(e,t,n){let r=Tt(t,e);Rp(r,n)}function Rp(e,t){za(e)&&da(e,t)}function da(e,t){let r=e[w],o=e[I],i=e[Te],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Zi(i)),s||=!1,i&&(i.dirty=!1),e[I]&=-9217,s)PD(r,e,r.template,e[xe]);else if(o&8192){Ap(e),Op(e,1);let a=r.components;a!==null&&Pp(e,a,1)}}function Pp(e,t,n){for(let r=0;r<t.length;r++)FD(e,t[r],n)}function LD(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Wt(~o);else{let i=o,s=n[++r],a=n[++r];Sy(s,i);let l=t[i];Ae(24,l),a(2,l),Ae(25,l)}}}finally{Wt(-1)}}function El(e,t){let n=of()?64:1088;for(e[st].changeDetectionScheduler?.notify(t);e;){e[I]|=n;let r=Gt(e);if(Yn(e)&&!r)return e;e=r}return null}var Yt=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[w];return Jn(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[xe]}get dirty(){return!!(this._lView[I]&9280)||!!this._lView[Te]?.dirty}set context(t){this._lView[xe]=t}get destroyed(){return sr(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ae];if(Oe(t)){let n=t[Do],r=n?n.indexOf(this):-1;r>-1&&(ca(t,r),go(n,r))}this._attachedToViewContainer=!1}Tp(this._lView[w],this._lView)}onDestroy(t){Xd(this._lView,t)}markForCheck(){El(this._cdRefInjectingView||this._lView,4)}markForRefresh(){qa(this._cdRefInjectingView||this._lView)}detach(){this._lView[I]&=-129}reattach(){js(this._lView),this._lView[I]|=128}detectChanges(){this._lView[I]|=1024,xp(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new m(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Yn(this._lView),n=this._lView[zt];n!==null&&!t&&ml(n,this._lView),Ip(this._lView[w],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new m(902,!1);this._appRef=t;let n=Yn(this._lView),r=this._lView[zt];r!==null&&!n&&Cp(r,this._lView),js(this._lView)}},Xt=(()=>{class e{static __NG_ELEMENT_ID__=BD}return e})(),VD=Xt,jD=class extends VD{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=CD(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Yt(o)}};function BD(){return di(le(),O())}function di(e,t){return e.type&4?new jD(t,e,xn(e,t)):null}var fa="<-- AT THIS LOCATION";function HD(e){switch(e){case 4:return"view container";case 2:return"element";case 8:return"ng-container";case 32:return"icu";case 64:return"i18n";case 16:return"projection";case 1:return"text";case 128:return"@let";default:return"<unknown>"}}function $D(e,t){let n=`During serialization, Angular was unable to find an element in the DOM:

`,r=`${WD(e,t,!1)}

`,o=QD();throw new m(-502,n+r+o)}function UD(e){let t="During serialization, Angular detected DOM nodes that were created outside of Angular context and provided as projectable nodes (likely via `ViewContainerRef.createComponent` or `createComponent` APIs). Hydration is not supported for such cases, consider refactoring the code to avoid this pattern or using `ngSkipHydration` on the host element of the component.\n\n",n=`${KD(e)}

`,r=t+n+ZD();return new m(-503,r)}function zD(e){let t=[];if(e.attrs)for(let n=0;n<e.attrs.length;){let r=e.attrs[n++];if(typeof r=="number")break;let o=e.attrs[n++];t.push(`${r}="${Oo(o)}"`)}return t.join(" ")}var qD=new Set(["ngh","ng-version","ng-server-context"]);function GD(e){let t=[];for(let n=0;n<e.attributes.length;n++){let r=e.attributes[n];qD.has(r.name)||t.push(`${r.name}="${Oo(r.value)}"`)}return t.join(" ")}function Is(e,t="\u2026"){switch(e.type){case 1:return`#text${e.value?`(${e.value})`:""}`;case 2:let r=zD(e),o=e.value.toLowerCase();return`<${o}${r?" "+r:""}>${t}</${o}>`;case 8:return"<!-- ng-container -->";case 4:return"<!-- container -->";default:return`#node(${HD(e.type)})`}}function uo(e,t="\u2026"){let n=e;switch(n.nodeType){case Node.ELEMENT_NODE:let r=n.tagName.toLowerCase(),o=GD(n);return`<${r}${o?" "+o:""}>${t}</${r}>`;case Node.TEXT_NODE:let i=n.textContent?Oo(n.textContent):"";return`#text${i?`(${i})`:""}`;case Node.COMMENT_NODE:return`<!-- ${Oo(n.textContent??"")} -->`;default:return`#node(${n.nodeType})`}}function WD(e,t,n){let r="  ",o="";t.prev?(o+=r+`\u2026
`,o+=r+Is(t.prev)+`
`):t.type&&t.type&12&&(o+=r+`\u2026
`),n?(o+=r+Is(t)+`
`,o+=r+`<!-- container -->  ${fa}
`):o+=r+Is(t)+`  ${fa}
`,o+=r+`\u2026
`;let i=t.type?bp(e[w],t,e):null;return i&&(o=uo(i,`
`+o)),o}function KD(e){let t="  ",n="",r=e;return r.previousSibling&&(n+=t+`\u2026
`,n+=t+uo(r.previousSibling)+`
`),n+=t+uo(r)+`  ${fa}
`,e.nextSibling&&(n+=t+`\u2026
`),e.parentNode&&(n=uo(r.parentNode,`
`+n)),n}function QD(e){return`To fix this problem:
  * check ${e?`the "${e}"`:"corresponding"} component for hydration-related issues
  * check to see if your template has valid HTML structure
  * or skip hydration by adding the \`ngSkipHydration\` attribute to its host node in a template

`}function ZD(){return`Note: attributes are only displayed to better represent the DOM but have no effect on hydration mismatches.

`}function YD(e){return e.replace(/\s+/gm,"")}function Oo(e,t=50){return e?(e=YD(e),e.length>t?`${e.substring(0,t-1)}\u2026`:e):""}function ur(e,t,n,r,o){let i=e.data[t];if(i===null)i=XD(e,t,n,r,o),by()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Iy();i.injectorIndex=s===null?-1:s.injectorIndex}return tn(i,!0),i}function XD(e,t,n,r,o){let i=nf(),s=Ga(),a=s?i:i&&i.parent,l=e.data[t]=ew(e,a,n,t,r,o);return JD(e,l,i,s),l}function JD(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function ew(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Dy()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function tw(e,t){let n=t[e.currentCaseLViewIndex];return n===null?n:n<0?~n:n}function kp(e,t,n){e.index=0;let r=tw(t,n);r!==null?e.removes=t.remove[r]:e.removes=ge}function pa(e){if(e.index<e.removes.length){let t=e.removes[e.index++];if(t>0)return e.lView[t];{e.stack.push(e.index,e.removes);let n=~t,r=e.lView[w].data[n];return kp(e,r,e.lView),pa(e)}}else return e.stack.length===0?null:(e.removes=e.stack.pop(),e.index=e.stack.pop(),pa(e))}function nw(e,t){let n={stack:[],index:-1,lView:t};return kp(n,e,t),pa.bind(null,n)}var BO=new RegExp(`^(\\d+)*(${zf}|${Uf})*(.*)`);function rw(e,t){let n=[e];for(let r of t){let o=n.length-1;if(o>0&&n[o-1]===r){let i=n[o]||1;n[o]=i+1}else n.push(r,"")}return n.join("")}function er(e,t){return!(e.type&144)&&!!t[e.index]&&Fp(q(t[e.index]))}function Fp(e){return!!e&&!e.isConnected}function ha(e,t){if(e===t)return[];if(e.parentElement==null||t.parentElement==null)return null;if(e.parentElement===t.parentElement)return ow(e,t);{let n=t.parentElement,r=ha(e,n),o=ha(n.firstChild,t);return!r||!o?null:[...r,_v,...o]}}function ow(e,t){let n=[],r=null;for(r=e;r!=null&&r!==t;r=r.nextSibling)n.push(Iv);return r==null?null:n}function Yu(e,t,n){let r=ha(e,t);return r===null?null:rw(n,r)}function iw(e,t,n){let r=e.parent,o,i,s;for(;r!==null&&(er(r,t)||n?.has(r.index));)r=r.parent;r===null||!(r.type&3)?(o=s=Uf,i=t[$e][se]):(o=r.index,i=q(t[o]),s=rr(o-U));let a=q(t[e.index]);if(e.type&44){let c=Bt(t,e);c&&(a=c)}let l=Yu(i,a,s);if(l===null&&i!==a){let c=i.ownerDocument.body;if(l=Yu(c,a,zf),l===null)throw $D(t,e)}return l}var sw=!1;function aw(){return sw}function lw(e){return e=e??S(Ue),e.get(Mv,!1)}function cw(e,t){let n=t.i18nChildren.get(e);return n===void 0&&(n=uw(e),t.i18nChildren.set(e,n)),n}function uw(e){let t=new Set;function n(r){switch(t.add(r.index),r.kind){case 1:case 2:{for(let o of r.children)n(o);break}case 3:{for(let o of r.cases)for(let i of o)n(i);break}}}for(let r=U;r<e.bindingStartIndex;r++){let o=e.data[r];if(!(!o||!o.ast))for(let i of o.ast)n(i)}return t.size===0?null:t}function dw(e,t,n){if(!n.isI18nHydrationEnabled)return null;let r=e[w],o=r.data[t];if(!o||!o.ast)return null;let i=r.data[o.parentTNodeIndex];if(i&&hv(i))return null;let s={caseQueue:[],disconnectedNodes:new Set,disjointNodes:new Set};return ga(e,s,n,o.ast),s.caseQueue.length===0&&s.disconnectedNodes.size===0&&s.disjointNodes.size===0?null:s}function ga(e,t,n,r){let o=null;for(let i of r){let s=pw(e,t,n,i);s&&(fw(o,s)&&t.disjointNodes.add(i.index-U),o=s)}return o}function fw(e,t){return e&&e.nextSibling!==t}function pw(e,t,n,r){let o=q(e[r.index]);if(!o||Fp(o))return t.disconnectedNodes.add(r.index-U),null;let i=o;switch(r.kind){case 0:{Xf(n,i);break}case 1:case 2:{ga(e,t,n,r.children);break}case 3:{let s=e[r.currentCaseLViewIndex];if(s!=null){let a=s<0?~s:s;t.caseQueue.push(a),ga(e,t,n,r.cases[a])}break}}return hw(e,r)}function hw(e,t){let r=e[w].data[t.index];return gf(r)?Bt(e,r):t.kind===3?nw(r,e)()??q(e[t.index]):q(e[t.index])??null}var gw=()=>null;function Xu(e,t){return gw(e,t)}var ma=class{},Ro=class{},ya=class{resolveComponentFactory(t){throw Error(`No component factory found for ${me(t)}.`)}},Sn=class{static NULL=new ya},Po=class{},Dl=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>mw()}return e})();function mw(){let e=O(),t=le(),n=Tt(t.index,e);return(vt(n)?n:e)[G]}var yw=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>null})}return e})();function ko(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=xs(o,a);else if(i==2){let l=a,c=t[++s];r=xs(r,l+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var Fo=class extends Sn{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Je(t);return new Jt(n,this.ngModule)}};function Ju(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:It.None;t?n.push({propName:s,templateName:r,isSignal:(a&It.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function vw(e){let t=e.toLowerCase();return t==="svg"?Gd:t==="math"?dy:null}function Ew(e){let{attrs:t,classes:n}=RE(e),r=t;return n.length&&r.push(1,...n),r}var Jt=class extends Ro{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;get inputs(){let t=this.componentDef,n=t.inputTransforms,r=Ju(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Ju(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=OE(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=P(null);try{o=o||this.ngModule;let s=o instanceof Dt?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new $s(t,s):t,l=a.get(Po,null);if(l===null)throw new m(407,!1);let c=a.get(yw,null),u=a.get(Zt,null),d={rendererFactory:l,sanitizer:c,changeDetectionScheduler:u},p=l.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",h=r?HE(p,r,this.componentDef.encapsulation,a):fp(p,f,vw(f)),g=512;this.componentDef.signals?g|=4096:this.componentDef.onPush||(g|=16);let v=null;h!==null&&(v=ol(h,a,!0));let E=ul(0,null,null,1,0,null,null,null,null,null,null),R=ai(null,E,null,g,null,null,d,p,a,null,v);R[U]=h,Qa(R);let j,L=null;try{let W=this.componentDef,K,re=null;W.findHostDirectiveDefs?(K=[],re=new Map,W.findHostDirectiveDefs(W,K,re),K.push(W)):K=[W];let We=r?["ng-version","19.1.3"]:Ew(this.componentDef.selectors[0]),ue=ur(E,U,2,"#host",We);for(let yr of K)ue.mergedAttrs=Cn(ue.mergedAttrs,yr.hostAttrs);ue.mergedAttrs=Cn(ue.mergedAttrs,We),ko(ue,ue.mergedAttrs,!0),h&&hp(p,h,ue),L=Dw(ue,h,W,K,R,d),n!==void 0&&_w(ue,this.ngContentSelectors,n),j=ww(L,W,K,re,R,[Iw]),hl(E,R,null)}catch(W){throw L!==null&&Zs(L),Zs(R),W}finally{Za()}let ce=Qd(E,U);return new va(this.componentType,j,xn(ce,R),R,ce)}finally{P(i)}}},va=class extends ma{location;_rootLView;_tNode;instance;hostView;changeDetectorRef;componentType;previousInputValues=null;constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.instance=n,this.hostView=this.changeDetectorRef=new Yt(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;pl(i[w],i,o,t,n),this.previousInputValues.set(t,n);let s=Tt(this._tNode.index,i);El(s,1)}}get injector(){return new Ht(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Dw(e,t,n,r,o,i){let s=o[w],a=null;t!==null&&(a=ol(t,o[Ut]));let l=i.rendererFactory.createRenderer(t,n),c=ai(o,yp(n),null,Dp(n),o[e.index],e,i,l,null,null,a);return s.firstCreatePass&&la(s,e,r.length-1),li(o,c),o[e.index]=c}function ww(e,t,n,r,o,i){let s=le(),a=o[w],l=Re(s,o);vp(a,o,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,p=Qt(o,a,d,s);_t(p,o)}Ep(a,o,s),l&&_t(l,o);let c=Qt(o,a,s.directiveStart+s.componentOffset,s);if(e[xe]=o[xe]=c,i!==null)for(let u of i)u(c,t);return il(a,s,o),c}function _w(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}function Iw(){let e=le();ri(O()[w],e)}var On=(()=>{class e{static __NG_ELEMENT_ID__=Cw}return e})();function Cw(){let e=le();return Vp(e,O())}var Tw=On,Lp=class extends Tw{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return xn(this._hostTNode,this._hostLView)}get injector(){return new Ht(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ya(this._hostTNode,this._hostLView);if(yf(t)){let n=_o(t,this._hostLView),r=wo(t),o=n[w].data[r+8];return new Ht(o,n)}else return new Ht(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=ed(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-we}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Xu(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Zu(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!iy(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let l=s?t:new Jt(Je(t)),c=r||this.parentInjector;if(!i&&l.ngModule==null){let g=(s?c:this.parentInjector).get(Dt,null);g&&(i=g)}let u=Je(l.componentType??{}),d=Xu(this._lContainer,u?.id??null),p=d?.firstChild??null,f=l.create(c,o,p,i);return this.insertImpl(f.hostView,a,Zu(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(py(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let l=o[ae],c=new Lp(l,l[ze],l[ae]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return TD(s,o,i,r),t.attachToViewContainerRef(),bd(Cs(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=ed(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=ca(this._lContainer,n);r&&(go(Cs(this._lContainer),n),Tp(r[w],r))}detach(t){let n=this._adjustIndex(t,-1),r=ca(this._lContainer,n);return r&&go(Cs(this._lContainer),n)!=null?new Yt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function ed(e){return e[Do]}function Cs(e){return e[Do]||(e[Do]=[])}function Vp(e,t){let n,r=t[e.index];return Oe(r)?n=r:(n=wp(r,t,null,e),t[e.index]=n,li(t,n)),Sw(n,t,e,r),new Lp(n,e,t)}function bw(e,t){let n=e[G],r=n.createComment(""),o=Re(t,e),i=n.parentNode(o);return Ao(n,i,r,n.nextSibling(o),!1),r}var Sw=Aw,Mw=()=>!1;function Nw(e,t,n){return Mw(e,t,n)}function Aw(e,t,n,r){if(e[qt])return;let o;n.type&8?o=q(r):o=bw(t,n),e[qt]=o}var Ea=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Da=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)wl(t,n).matches!==null&&this.queries[n].setDirty()}},Lo=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Vw(t):this.predicate=t}},wa=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},_a=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,xw(n,i)),this.matchTNodeWithReadOption(t,n,co(n,t,i,!1,!1))}else r===Xt?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,co(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===rn||o===On||o===Xt&&n.type&4)this.addMatch(n.index,-2);else{let i=co(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function xw(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Ow(e,t){return e.type&11?xn(e,t):e.type&4?di(e,t):null}function Rw(e,t,n,r){return n===-1?Ow(t,e):n===-2?Pw(e,t,r):Qt(e,e[w],n,t)}function Pw(e,t,n){if(n===rn)return xn(t,e);if(n===Xt)return di(t,e);if(n===On)return Vp(t,e)}function jp(e,t,n,r){let o=t[at].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let c=s[l];if(c<0)a.push(null);else{let u=i[c];a.push(Rw(t,u,s[l+1],n.metadata.read))}}o.matches=a}return o.matches}function Ia(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=jp(e,t,o,n);for(let a=0;a<i.length;a+=2){let l=i[a];if(l>0)r.push(s[a/2]);else{let c=i[a+1],u=t[-l];for(let d=we;d<u.length;d++){let p=u[d];p[zt]===p[ae]&&Ia(p[w],p,c,r)}if(u[_n]!==null){let d=u[_n];for(let p=0;p<d.length;p++){let f=d[p];Ia(f[w],f,c,r)}}}}}return r}function kw(e,t){return e[at].queries[t].queryList}function Bp(e,t,n){let r=new Qs((n&4)===4);return my(e,t,r,r.destroy),(t[at]??=new Da).queries.push(new Ea(r))-1}function Fw(e,t,n){let r=X();return r.firstCreatePass&&(Hp(r,new Lo(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Bp(r,O(),t)}function Lw(e,t,n,r){let o=X();if(o.firstCreatePass){let i=le();Hp(o,new Lo(t,n,r),i.index),jw(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Bp(o,O(),n)}function Vw(e){return e.split(",").map(t=>t.trim())}function Hp(e,t,n){e.queries===null&&(e.queries=new wa),e.queries.track(new _a(t,n))}function jw(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function wl(e,t){return e.queries.getByIndex(t)}function Bw(e,t){let n=e[w],r=wl(n,t);return r.crossesNgTemplate?Ia(n,e,t,[]):jp(n,e,r,t)}function Hw(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(qw))}return i}return Vo.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(c=>{o.template=c}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let c=o.styles.length,u=o.styleUrls;o.styleUrls.forEach((d,p)=>{a.push(""),s.push(r(d).then(f=>{a[c+p]=f,u.splice(u.indexOf(d),1),u.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(c=>{a.push(c),o.styleUrl=void 0}));let l=Promise.all(s).then(()=>Gw(i));t.push(l)}),Uw(),Promise.all(t).then(()=>{})}var Vo=new Map,$w=new Set;function Uw(){let e=Vo;return Vo=new Map,e}function zw(){return Vo.size===0}function qw(e){return typeof e=="string"?e:e.text()}function Gw(e){$w.delete(e)}var Ct=class{},Ca=class{};var jo=class extends Ct{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Fo(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Nd(t);this._bootstrapComponents=lp(i.bootstrap),this._r3Injector=bf(t,n,[{provide:Ct,useValue:this},{provide:Sn,useValue:this.componentFactoryResolver},...r],me(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Bo=class extends Ca{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new jo(this.moduleType,t,[])}};function Ww(e,t,n){return new jo(e,t,n,!1)}var Ho=class extends Ct{injector;componentFactoryResolver=new Fo(this);instance=null;constructor(t){super();let n=new Kn([...t.providers,{provide:Ct,useValue:this},{provide:Sn,useValue:this.componentFactoryResolver}],t.parent||Qo(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Kw(e,t,n=null){return new Ho({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Qw=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Od(!1,n.type),o=r.length>0?Kw([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=V({token:e,providedIn:"environment",factory:()=>new e(Y(Dt))})}return e})();function $O(e){return nr(()=>{let t=Up(e),n=Le(ie({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Qw).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||bn.Emulated,styles:e.styles||ge,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&on("NgStandalone"),zp(n);let r=e.dependencies;return n.directiveDefs=nd(r,!1),n.pipeDefs=nd(r,!0),n.id=Jw(n),n})}function Zw(e){return Je(e)||Ad(e)}function Yw(e){return e!==null}function _l(e){return nr(()=>({type:e.type,bootstrap:e.bootstrap||ge,declarations:e.declarations||ge,imports:e.imports||ge,exports:e.exports||ge,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function td(e,t){if(e==null)return Dn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=It.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==It.None?[r,a]:r,t[i]=s):n[i]=r}return n}function fi(e){return nr(()=>{let t=Up(e);return zp(t),t})}function $p(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Up(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Dn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||ge,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:td(e.inputs,t),outputs:td(e.outputs),debugInfo:null}}function zp(e){e.features?.forEach(t=>t(e))}function nd(e,t){if(!e)return null;let n=t?xd:Zw;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Yw)}var Xw=new Map;function Jw(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function e_(e){return Object.getPrototypeOf(e.prototype).constructor}function t_(e){let t=e_(e.type),n=!0,r=[e];for(;t;){let o;if(wt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new m(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ro(e.inputs),s.inputTransforms=ro(e.inputTransforms),s.declaredInputs=ro(e.declaredInputs),s.outputs=ro(e.outputs);let a=o.hostBindings;a&&s_(e,a);let l=o.viewQuery,c=o.contentQueries;if(l&&o_(e,l),c&&i_(e,c),n_(e,o),Dm(e.outputs,o.outputs),wt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===t_&&(n=!1)}}t=Object.getPrototypeOf(t)}r_(r)}function n_(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function r_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Cn(o.hostAttrs,n=Cn(n,o.hostAttrs))}}function ro(e){return e===Dn?{}:e===ge?[]:e}function o_(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function i_(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function s_(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function a_(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}function qp(e){return c_(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function l_(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function c_(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Il(e,t,n){return e[t]=n}function u_(e,t){return e[t]}function et(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function d_(e,t,n,r){let o=et(e,t,n);return et(e,t+1,r)||o}function Gp(e){return(e.flags&32)===32}function f_(e,t,n,r,o,i,s,a,l){let c=t.consts,u=ur(t,e,4,s||null,a||null);fl(t,n,u,In(c,l)),ri(t,u);let d=u.tView=ul(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function p_(e,t,n,r,o,i,s,a,l,c){let u=n+U,d=t.firstCreatePass?f_(u,t,e,r,o,i,s,a,l):t.data[u];tn(d,!1);let p=g_(t,e,d,n);ti()&&ci(t,e,p,d),_t(p,e);let f=wp(p,e,p,d);return e[u]=f,li(e,f),Nw(f,d,e),Yo(d)&&ll(t,e,d),l!=null&&cl(e,d,c),d}function h_(e,t,n,r,o,i,s,a){let l=O(),c=X(),u=In(c.consts,i);return p_(l,c,e,t,n,r,o,u,s,a),h_}var g_=m_;function m_(e,t,n,r){return ni(!0),t[G].createComment("")}var UO=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var y_=new C(""),v_=new C(""),zO=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(n,r,o){this._ngZone=n,this.registry=r,Cl||(D_(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{fe.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||e)(Y(fe),Y(E_),Y(v_))};static \u0275prov=V({token:e,factory:e.\u0275fac})}return e})(),E_=(()=>{class e{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Cl?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function D_(e){Cl=e}var Cl;function Tl(e){return!!e&&typeof e.then=="function"}function Wp(e){return!!e&&typeof e.subscribe=="function"}var w_=new C("");var Kp=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=S(w_,{optional:!0})??[];injector=S(Ue);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Ld(this.injector,o);if(Tl(i))n.push(i);else if(Wp(i)){let s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),__=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>new Ta})}return e})(),Ta=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}},I_=new C("");function Qp(){Oc(()=>{throw new m(600,!1)})}function C_(e){return e.isBoundToModule}var T_=10;function b_(e,t,n){try{let r=n();return Tl(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Zp(e,t){return Array.isArray(t)?t.reduce(Zp,e):ie(ie({},e),t)}var Mn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=S(sv);afterRenderManager=S(Gf);zonelessEnabled=S(Ja);rootEffectScheduler=S(__);dirtyFlags=0;deferredDirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Qe;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=S(oi).hasPendingTasks.pipe(Ft(n=>!n));constructor(){S(ii,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=S(Dt);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Ro;if(!this._injector.get(Kp).done){let p=!o&&qm(n),f=!1;throw new m(405,f)}let s;o?s=n:s=this._injector.get(Sn).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=C_(s)?void 0:this._injector.get(Ct),l=r||s.selector,c=s.create(Ue.NULL,[],l,a),u=c.location.nativeElement,d=c.injector.get(y_,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),fo(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick=()=>{if(this.tracingSnapshot!==null){let r=this.tracingSnapshot;this.tracingSnapshot=null,r.run(nl.CHANGE_DETECTION,this._tick),r.dispose();return}if(this._runningTick)throw new m(101,!1);let n=P(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,P(n),this.afterTick.next()}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Po,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let n=0;for(;this.dirtyFlags!==0&&n++<T_;)this.synchronizeOnce()}synchronizeOnce(){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)S_(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Xo(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;fo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(I_,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>fo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new m(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function S_(e,t,n,r){if(!n&&!Xo(e))return;xp(e,t,n&&!r?0:1)}function M_(e,t,n,r){let o=O(),i=ar();if(et(o,i,t)){let s=X(),a=ei();rD(a,o,e,t,n,r)}return M_}function N_(e,t,n,r){return et(e,ar(),n)?t+rr(n)+r:tt}function oo(e,t){return e<<17|t<<2}function en(e){return e>>17&32767}function A_(e){return(e&2)==2}function x_(e,t){return e&131071|t<<17}function ba(e){return e|2}function Nn(e){return(e&131068)>>2}function Ts(e,t){return e&-131069|t<<2}function O_(e){return(e&1)===1}function Sa(e){return e|1}function R_(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=en(s),l=Nn(s);e[r]=n;let c=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||or(d,u)>0)&&(c=!0)}else u=n;if(o)if(l!==0){let p=en(e[a+1]);e[r+1]=oo(p,a),p!==0&&(e[p+1]=Ts(e[p+1],r)),e[a+1]=x_(e[a+1],r)}else e[r+1]=oo(a,0),a!==0&&(e[a+1]=Ts(e[a+1],r)),a=r;else e[r+1]=oo(l,0),a===0?a=r:e[l+1]=Ts(e[l+1],r),l=r;c&&(e[r+1]=ba(e[r+1])),rd(e,u,r,!0),rd(e,u,r,!1),P_(t,u,e,r,i),s=oo(a,l),i?t.classBindings=s:t.styleBindings=s}function P_(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&or(i,t)>=0&&(n[r+1]=Sa(n[r+1]))}function rd(e,t,n,r){let o=e[n+1],i=t===null,s=r?en(o):Nn(o),a=!1;for(;s!==0&&(a===!1||i);){let l=e[s],c=e[s+1];k_(l,t)&&(a=!0,e[s+1]=r?Sa(c):ba(c)),s=r?en(c):Nn(c)}a&&(e[n+1]=r?ba(o):Sa(o))}function k_(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?or(e,t)>=0:!1}var Be={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function F_(e){return e.substring(Be.key,Be.keyEnd)}function L_(e){return V_(e),Yp(e,Xp(e,0,Be.textEnd))}function Yp(e,t){let n=Be.textEnd;return n===t?-1:(t=Be.keyEnd=j_(e,Be.key=t,n),Xp(e,t,n))}function V_(e){Be.key=0,Be.keyEnd=0,Be.value=0,Be.valueEnd=0,Be.textEnd=e.length}function Xp(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function j_(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function B_(e,t,n){let r=O(),o=ar();if(et(r,o,t)){let i=X(),s=ei();dl(i,s,r,e,t,r[G],n,!1)}return B_}function Ma(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";pl(e,n,i[s],s,r)}function H_(e,t){return U_(e,t,null,!0),H_}function qO(e){z_(Z_,$_,e,!0)}function $_(e,t){for(let n=L_(t);n>=0;n=Yp(t,n))Ba(e,F_(t),!0)}function U_(e,t,n,r){let o=O(),i=X(),s=sf(2);if(i.firstUpdatePass&&eh(i,e,s,r),t!==tt&&et(o,s,t)){let a=i.data[nn()];th(i,a,o,o[G],e,o[s+1]=X_(t,n),r,s)}}function z_(e,t,n,r){let o=X(),i=sf(2);o.firstUpdatePass&&eh(o,null,i,r);let s=O();if(n!==tt&&et(s,i,n)){let a=o.data[nn()];if(nh(a,r)&&!Jp(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;l!==null&&(n=xs(l,n||"")),Ma(o,a,s,n,r)}else Y_(o,a,s,s[G],s[i+1],s[i+1]=Q_(e,t,n),r,i)}}function Jp(e,t){return t>=e.expandoStartIndex}function eh(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[nn()],s=Jp(e,n);nh(i,r)&&t===null&&!s&&(t=!1),t=q_(o,i,t,r),R_(o,i,t,n,s,r)}}function q_(e,t,n,r){let o=Ny(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=bs(null,e,t,n,r),n=tr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=bs(o,e,t,n,r),i===null){let l=G_(e,t,r);l!==void 0&&Array.isArray(l)&&(l=bs(null,e,t,l[1],r),l=tr(l,t.attrs,r),W_(e,t,r,l))}else i=K_(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function G_(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Nn(r)!==0)return e[en(r)]}function W_(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[en(o)]=r}function K_(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=tr(r,s,n)}return tr(r,t.attrs,n)}function bs(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=tr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function tr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Ba(e,s,n?!0:t[++i]))}return e===void 0?null:e}function Q_(e,t,n){if(n==null||n==="")return ge;let r=[],o=lr(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function Z_(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Ba(e,r,n)}function Y_(e,t,n,r,o,i,s,a){o===tt&&(o=ge);let l=0,c=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=l<o.length?o[l+1]:void 0,f=c<i.length?i[c+1]:void 0,h=null,g;u===d?(l+=2,c+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(l+=2,h=u):(c+=2,h=d,g=f),h!==null&&th(e,t,n,r,h,g,s,a),u=l<o.length?o[l]:null,d=c<i.length?i[c]:null}}function th(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let l=e.data,c=l[a+1],u=O_(c)?od(l,t,n,o,Nn(c),s):void 0;if(!$o(u)){$o(i)||A_(c)&&(i=od(l,null,n,o,a,s));let d=Kd(nn(),n);ID(r,s,d,o,i)}}function od(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let l=e[o],c=Array.isArray(l),u=c?l[1]:l,d=u===null,p=n[o+1];p===tt&&(p=d?ge:void 0);let f=d?hs(p,r):u===r?p:void 0;if(c&&!$o(f)&&(f=hs(l,r)),$o(f)&&(a=f,s))return a;let h=e[o+1];o=s?en(h):Nn(h)}if(t!==null){let l=i?t.residualClasses:t.residualStyles;l!=null&&(a=hs(l,r))}return a}function $o(e){return e!==void 0}function X_(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=me(lr(e)))),e}function nh(e,t){return(e.flags&(t?8:16))!==0}function J_(e,t,n,r,o,i){let s=t.consts,a=In(s,o),l=ur(t,e,2,r,a);return fl(t,n,l,In(s,i)),l.attrs!==null&&ko(l,l.attrs,!1),l.mergedAttrs!==null&&ko(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function rh(e,t,n,r){let o=O(),i=X(),s=U+e,a=o[G],l=i.firstCreatePass?J_(s,i,o,t,n,r):i.data[s],c=tI(i,o,l,a,t,e);o[s]=c;let u=Yo(l);return tn(l,!0),hp(a,c,l),!Gp(l)&&ti()&&ci(i,o,c,l),yy()===0&&_t(c,o),vy(),u&&(ll(i,o,l),il(i,l,o)),r!==null&&cl(o,l),rh}function oh(){let e=le();Ga()?rf():(e=e.parent,tn(e,!1));let t=e;wy(t)&&_y(),Ey();let n=X();return n.firstCreatePass&&(ri(n,e),Ua(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&Ly(t)&&Ma(n,t,O(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Vy(t)&&Ma(n,t,O(),t.stylesWithoutHost,!1),oh}function eI(e,t,n,r){return rh(e,t,n,r),oh(),eI}var tI=(e,t,n,r,o,i)=>(ni(!0),fp(r,o,Oy()));function nI(e,t,n,r,o){let i=t.consts,s=In(i,r),a=ur(t,e,8,"ng-container",s);s!==null&&ko(a,s,!0);let l=In(i,o);return fl(t,n,a,l),t.queries!==null&&t.queries.elementStart(t,a),a}function rI(e,t,n){let r=O(),o=X(),i=e+U,s=o.firstCreatePass?nI(i,o,r,t,n):o.data[i];tn(s,!0);let a=iI(o,r,s,e);return r[i]=a,ti()&&ci(o,r,a,s),_t(a,r),Yo(s)&&(ll(o,r,s),il(o,s,r)),n!=null&&cl(r,s),rI}function oI(){let e=le(),t=X();return Ga()?rf():(e=e.parent,tn(e,!1)),t.firstCreatePass&&(ri(t,e),Ua(e)&&t.queries.elementEnd(e)),oI}var iI=(e,t,n,r)=>(ni(!0),FE(t[G],""));function GO(){return O()}function sI(e,t,n){let r=O(),o=ar();if(et(r,o,t)){let i=X(),s=ei();dl(i,s,r,e,t,r[G],n,!0)}return sI}var jt=void 0;function aI(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var lI=["en",[["a","p"],["AM","PM"],jt],[["AM","PM"],jt,jt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],jt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],jt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",jt,"{1} 'at' {0}",jt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",aI],Ss={};function Pe(e){let t=cI(e),n=id(t);if(n)return n;let r=t.split("-")[0];if(n=id(r),n)return n;if(r==="en")return lI;throw new m(701,!1)}function id(e){return e in Ss||(Ss[e]=it.ng&&it.ng.common&&it.ng.common.locales&&it.ng.common.locales[e]),Ss[e]}var J=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(J||{});function cI(e){return e.toLowerCase().replace(/_/g,"-")}var Uo="en-US";var uI=Uo;function dI(e){typeof e=="string"&&(uI=e.toLowerCase().replace(/_/g,"-"))}var fI=(e,t,n)=>{};function pI(e,t,n,r){let o=O(),i=X(),s=le();return ih(i,o,o[G],s,e,t,r),pI}function hI(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Qn],l=o[i+2];return a.length>l?a[l]:null}typeof s=="string"&&(i+=2)}return null}function ih(e,t,n,r,o,i,s){let a=Yo(r),c=e.firstCreatePass&&ef(e),u=t[xe],d=Jd(t),p=!0;if(r.type&3||s){let g=Re(r,t),v=s?s(g):g,E=d.length,R=s?L=>s(q(L[r.index])):r.index,j=null;if(!s&&a&&(j=hI(e,t,o,r.index)),j!==null){let L=j.__ngLastListenerFn__||j;L.__ngNextListenerFn__=i,j.__ngLastListenerFn__=i,p=!1}else{i=ad(r,t,u,i),fI(g,o,i);let L=n.listen(v,o,i);d.push(i,L),c&&c.push(o,R,E,E+1)}}else i=ad(r,t,u,i);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let g=h.length;if(g)for(let v=0;v<g;v+=2){let E=h[v],R=h[v+1],ce=t[E][R].subscribe(i),W=d.length;d.push(i,ce),c&&c.push(o,r.index,W,-(W+1))}}}function sd(e,t,n,r){let o=P(null);try{return Ae(6,t,n),n(r)!==!1}catch(i){return _p(e,i),!1}finally{Ae(7,t,n),P(o)}}function ad(e,t,n,r){return function o(i){if(i===Function)return r;let s=e.componentOffset>-1?Tt(e.index,t):t;El(s,5);let a=sd(t,n,r,i),l=o.__ngNextListenerFn__;for(;l;)a=sd(t,n,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function WO(e=1){return xy(e)}function KO(e,t,n,r){Lw(e,t,n,r)}function QO(e,t,n){Fw(e,t,n)}function ZO(e){let t=O(),n=X(),r=af();Ka(r+1);let o=wl(n,r);if(e.dirty&&fy(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Bw(t,r);e.reset(i,cv),e.notifyOnChanges()}return!0}return!1}function YO(){return kw(O(),af())}function gI(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function XO(e){let t=Cy();return Zd(t,U+e)}function JO(e,t=""){let n=O(),r=X(),o=e+U,i=r.firstCreatePass?ur(r,o,1,t,null):r.data[o],s=mI(r,n,i,t,e);n[o]=s,ti()&&ci(r,n,s,i),tn(i,!1)}var mI=(e,t,n,r,o)=>(ni(!0),PE(t[G],r));function yI(e){return sh("",e,""),yI}function sh(e,t,n){let r=O(),o=N_(r,e,t,n);return o!==tt&&vI(r,nn(),o),sh}function vI(e,t,n){let r=Kd(t,e);kE(e[G],r,n)}function EI(e,t,n){Of(t)&&(t=t());let r=O(),o=ar();if(et(r,o,t)){let i=X(),s=ei();dl(i,s,r,e,t,r[G],n,!1)}return EI}function eR(e,t){let n=Of(e);return n&&e.set(t),n}function DI(e,t){let n=O(),r=X(),o=le();return ih(r,n,n[G],o,e,t),DI}function wI(e,t,n){let r=X();if(r.firstCreatePass){let o=wt(e);Na(n,r.data,r.blueprint,o,!0),Na(t,r.data,r.blueprint,o,!1)}}function Na(e,t,n,r,o){if(e=he(e),Array.isArray(e))for(let i=0;i<e.length;i++)Na(e[i],t,n,r,o);else{let i=X(),s=O(),a=le(),l=wn(e)?e:he(e.provide),c=Fd(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(wn(e)||!e.multi){let f=new Kt(c,o,ye),h=Ns(l,t,o?u:u+p,d);h===-1?(zs(Co(a,s),i,l),Ms(i,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=Ns(l,t,u+p,d),h=Ns(l,t,u,u+p),g=f>=0&&n[f],v=h>=0&&n[h];if(o&&!v||!o&&!g){zs(Co(a,s),i,l);let E=CI(o?II:_I,n.length,o,r,c);!o&&v&&(n[h].providerFactory=E),Ms(i,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(E),s.push(E)}else{let E=ah(n[o?h:f],c,!o&&r);Ms(i,e,f>-1?f:h,E)}!o&&r&&v&&n[h].componentProviders++}}}function Ms(e,t,n,r){let o=wn(t),i=Ym(t);if(o||i){let l=(i?he(t.useClass):t).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=c.indexOf(n);u===-1?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function ah(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Ns(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function _I(e,t,n,r){return Aa(this.multi,[])}function II(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Qt(n,n[w],this.providerFactory.index,r);i=a.slice(0,s),Aa(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Aa(o,i);return i}function Aa(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function CI(e,t,n,r,o){let i=new Kt(e,n,ye);return i.multi=[],i.index=t,i.componentProviders=0,ah(i,o,r&&!n),i}function tR(e,t=[]){return n=>{n.providersResolver=(r,o)=>wI(r,o?o(e):e,t)}}function nR(e,t,n){let r=Wa()+e,o=O();return o[r]===tt?Il(o,r,n?t.call(n):t()):u_(o,r)}function rR(e,t,n,r){return TI(O(),Wa(),e,t,n,r)}function lh(e,t){let n=e[t];return n===tt?void 0:n}function TI(e,t,n,r,o,i){let s=t+n;return et(e,s,o)?Il(e,s+1,i?r.call(i,o):r(o)):lh(e,s+1)}function bI(e,t,n,r,o,i,s){let a=t+n;return d_(e,a,o,i)?Il(e,a+2,s?r.call(s,o,i):r(o,i)):lh(e,a+2)}function oR(e,t){let n=X(),r,o=e+U;n.firstCreatePass?(r=SI(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=$t(r.type,!0)),s,a=De(ye);try{let l=Io(!1),c=i();return Io(l),gI(n,O(),o,c),c}finally{De(a)}}function SI(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function iR(e,t,n,r){let o=e+U,i=O(),s=Zd(i,o);return MI(i,o)?bI(i,Wa(),t,s.transform,n,r,s):s.transform(n,r)}function MI(e,t){return e[w].data[t].pure}function sR(e,t){return di(e,t)}var io=null;function NI(e){io!==null&&(e.defaultEncapsulation!==io.defaultEncapsulation||e.preserveWhitespaces!==io.preserveWhitespaces)||(io=e)}var AI=[];var xI=new WeakMap,OI=new WeakMap;function aR(){xI=new WeakMap,OI=new WeakMap,AI.length=0,Xw.clear()}var xa=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},lR=(()=>{class e{compileModuleSync(n){return new Bo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Nd(n),i=lp(o.declarations).reduce((s,a)=>{let l=Je(a);return l&&s.push(new Jt(l)),s},[]);return new xa(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),RI=new C("");function PI(e,t,n){let r=new Bo(n);return Promise.resolve(r)}function ld(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var kI=(()=>{class e{zone=S(fe);changeDetectionScheduler=S(Zt);applicationRef=S(Mn);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),FI=new C("",{factory:()=>!1});function bl({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new fe(Le(ie({},Sl()),{scheduleInRootZone:n})),[{provide:fe,useFactory:e},{provide:mo,multi:!0,useFactory:()=>{let r=S(kI,{optional:!0});return()=>r.initialize()}},{provide:mo,multi:!0,useFactory:()=>{let r=S(LI);return()=>{r.initialize()}}},t===!0?{provide:Mf,useValue:!0}:[],{provide:Nf,useValue:n??Sf}]}function cR(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=bl({ngZoneFactory:()=>{let o=Sl(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&on("NgZone_CoalesceEvent"),new fe(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Gm([{provide:FI,useValue:!0},{provide:Ja,useValue:!1},r])}function Sl(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var LI=(()=>{class e{subscription=new ee;initialized=!1;zone=S(fe);pendingTasks=S(oi);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{fe.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{fe.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ch=(()=>{class e{appRef=S(Mn);taskService=S(oi);ngZone=S(fe);zonelessEnabled=S(Ja);tracing=S(ii,{optional:!0});disableScheduling=S(Mf,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ee;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(bo):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(S(Nf,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof So||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 8:{this.appRef.deferredDirtyFlags|=8;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 13:{this.appRef.dirtyFlags|=16,r=!0;break}case 14:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{r=!0;break}case 10:case 9:case 7:case 11:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Tu:Af;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(bo+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Tu(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function VI(){return typeof $localize<"u"&&$localize.locale||Uo}var pi=new C("",{providedIn:"root",factory:()=>S(pi,x.Optional|x.SkipSelf)||VI()});var zo=new C(""),jI=new C("");function Un(e){return!e.moduleRef}function uh(e){let t=Un(e)?e.r3Injector:e.moduleRef.injector,n=t.get(fe);return n.run(()=>{Un(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Tn,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Un(e)){let i=()=>t.destroy(),s=e.platformInjector.get(zo);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(zo);s.add(i),e.moduleRef.onDestroy(()=>{fo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return b_(r,n,()=>{let i=t.get(Kp);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(pi,Uo);if(dI(s||Uo),!t.get(jI,!0))return Un(e)?t.get(Mn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Un(e)){let l=t.get(Mn);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return BI(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function BI(e,t){let n=e.injector.get(Mn);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new m(-403,!1);t.push(e)}var dh=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,r){let o=r?.scheduleInRootZone,i=()=>iv(r?.ngZone,Le(ie({},Sl({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[bl({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:Zt,useExisting:ch}],l=Ww(n.moduleType,this.injector,a);return uh({moduleRef:l,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let o=Zp({},r);return PI(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new m(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(zo,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(Y(Ue))};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),Et=null,fh=new C("");function HI(e){if(Et&&!Et.get(fh,!1))throw new m(400,!1);Qp(),Et=e;let t=e.get(dh);return gh(e),t}function $I(e,t,n=[]){let r=`Platform: ${t}`,o=new C(r);return(i=[])=>{let s=hh();if(!s||s.injector.get(fh,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):HI(ph(a,r))}return UI(o)}}function ph(e=[],t){return Ue.create({name:t,providers:[{provide:kd,useValue:"platform"},{provide:zo,useValue:new Set([()=>Et=null])},...e]})}function UI(e){let t=hh();if(!t)throw new m(401,!1);return t}function hh(){return Et?.get(dh)??null}function zI(e=[]){if(Et)return Et;let t=ph(e);return Et=t,Qp(),gh(t),t}function gh(e){let t=e.get(Ev,null);Ld(e,()=>{t?.forEach(n=>n())})}var mh=(()=>{class e{static __NG_ELEMENT_ID__=qI}return e})();function qI(e){return GI(le(),O(),(e&16)===16)}function GI(e,t,n){if(ir(e)&&!n){let r=Tt(e.index,t);return new Yt(r,r)}else if(e.type&175){let r=t[$e];return new Yt(r,t)}return null}var Oa=class{constructor(){}supports(t){return qp(t)}create(t){return new Ra(t)}},WI=(e,t)=>t,Ra=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||WI}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<cd(r,o,i)?n:r,a=cd(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,u=l-o;if(c!=u){for(let p=0;p<c;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;u<=h&&h<c&&(i[p]=f+1)}let d=s.previousIndex;i[d]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!qp(t))throw new m(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,l_(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Pa(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new qo),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new qo),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Pa=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},ka=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},qo=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ka,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function cd(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function ud(){return new Ml([new Oa])}var Ml=(()=>{class e{factories;static \u0275prov=V({token:e,providedIn:"root",factory:ud});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||ud()),deps:[[e,new Bm,new jm]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new m(901,!1)}}return e})();var uR=$I(null,"core",[]),dR=(()=>{class e{constructor(n){}static \u0275fac=function(r){return new(r||e)(Y(Mn))};static \u0275mod=_l({type:e});static \u0275inj=La({})}return e})();function fR(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=zI(r),i=[bl({}),{provide:Zt,useExisting:ch},...n||[]],s=new Ho({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return uh({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}}function KI(e,t,n){let r=new Map,o=t[Qn],i=e.cleanup;if(!i||!o)return r;for(let s=0;s<i.length;){let a=i[s++],l=i[s++];if(typeof a!="string")continue;let c=a;if(!du(c))continue;uu(c)?n.capture.add(c):n.regular.add(c);let u=q(t[l]);s++;let d=i[s++];(typeof d=="boolean"||d>=0)&&(r.has(u)?r.get(u).push(c):r.set(u,[c]))}return r}var Fa=class{views=[];indexByContent=new Map;add(t){let n=JSON.stringify(t);if(!this.indexByContent.has(n)){let r=this.views.length;return this.views.push(t),this.indexByContent.set(n,r),r}return this.indexByContent.get(n)}getAll(){return this.views}},QI=0;function yh(e){return e.ssrId||(e.ssrId=`t${QI++}`),e.ssrId}function vh(e,t,n){let r=[];return Jn(e,t,n,r),r.length}function ZI(e){let t=[];return vl(e,t),t.length}function Eh(e,t,n){let r=e[se];return r&&!r.hasAttribute(Mo)?Go(r,e,null,t):null}function Dh(e,t,n){let r=Wd(e[se]),o=Eh(r,t,n);if(o===null)return;let i=q(r[se]),s=e[ae],a=Go(i,s,null,t),l=r[G],c=`${o}|${a}`;l.setAttribute(i,Yf,c)}function pR(e,t){let n=e.injector,r=lw(n),o=Qv(n),i=new Fa,s=new Map,a=e._views,l=n.get(Nv,Av),c={regular:new Set,capture:new Set},u=new Map,d=e.injector.get(Bf);for(let h of a){let g=Kv(h);if(g!==null){let v={serializedViewCollection:i,corruptedTextNodes:s,isI18nHydrationEnabled:r,isIncrementalHydrationEnabled:o,i18nChildren:new Map,eventTypesToReplay:c,shouldReplayEvents:l,appId:d,deferBlocks:u};Oe(g)?Dh(g,v,n):Eh(g,v,n),tC(s,t)}}let p=i.getAll(),f=n.get($f);if(f.set(zv,p),u.size>0){let h={};for(let[g,v]of u.entries())h[g]=v;f.set(Gv,h)}return c}function YI(e,t,n,r,o){let i=[],s="";for(let a=we;a<e.length;a++){let l=e[a],c,u,d;if(Yn(l)&&(l=l[U],Oe(l))){u=ZI(l)+1,Dh(l,o,n[Ut]);let f=Wd(l[se]);d={[ku]:f[w].ssrId,[Ds]:u}}if(!d){let f=l[w];f.type===1?(c=f.ssrId,u=1):(c=yh(f),u=vh(f,l,f.firstChild)),d={[ku]:c,[Ds]:u};let h=!1;if(jv(n[w],t)){let g=Lv(n,t),v=Qf(n[w],t);if(o.isIncrementalHydrationEnabled&&v.hydrateTriggers!==null){let E=`d${o.deferBlocks.size}`;v.hydrateTriggers.has(7)&&(h=!0);let R=[];vl(e,R);let j={[Ds]:R.length,[Vu]:g[ju]},L=XI(v.hydrateTriggers);L.length>0&&(j[bv]=L),r!==null&&(j[Tv]=r),o.deferBlocks.set(E,j);let ce=q(e);ce!==void 0?ce.nodeType===Node.COMMENT_NODE&&dd(ce,E):dd(ce,E),h||rC(v,R,E,o),r=E,d[Cv]=E}d[Vu]=g[ju]}h||Object.assign(d,wh(e[a],r,o))}let p=JSON.stringify(d);if(i.length>0&&p===s){let f=i[i.length-1];f[Pu]??=1,f[Pu]++}else s=p,i.push(d)}return i}function XI(e){let t=new Set([0,1,2,5]),n=[];for(let[r,o]of e)t.has(r)&&(o===null?n.push(r):n.push({trigger:r,delay:o.delay}));return n}function Gn(e,t,n,r){let o=t.index-U;e[Fu]??={},e[Fu][o]??=iw(t,n,r)}function As(e,t){let n=typeof t=="number"?t:t.index-U;e[ws]??=[],e[ws].includes(n)||e[ws].push(n)}function wh(e,t=null,n){let r={},o=e[w],i=cw(o,n),s=n.shouldReplayEvents?KI(o,e,n.eventTypesToReplay):null;for(let a=U;a<o.bindingStartIndex;a++){let l=o.data[a],c=a-U,u=dw(e,a,n);if(u){r[Lu]??={},r[Lu][c]=u.caseQueue;for(let d of u.disconnectedNodes)As(r,d);for(let d of u.disjointNodes){let p=o.data[d+U];Gn(r,p,e,i)}continue}if(gf(l)&&!Gp(l)){if(er(l,e)&&nC(l)){As(r,l);continue}if(Array.isArray(l.projection)){for(let d of l.projection)if(d)if(!Array.isArray(d))!Hd(d)&&!No(d)&&(er(d,e)?As(r,d):Gn(r,d,e,i));else throw UD(q(e[a]))}if(JI(r,l,e,i),Oe(e[a])){let d=l.tView;d!==null&&(r[Ou]??={},r[Ou][c]=yh(d));let p=e[a][se];if(Array.isArray(p)){let f=q(p);f.hasAttribute(Mo)||Go(f,p,t,n)}r[Ru]??={},r[Ru][c]=YI(e[a],l,e,t,n)}else if(Array.isArray(e[a])&&!Fy(l)){let d=q(e[a][se]);d.hasAttribute(Mo)||Go(d,e[a],t,n)}else if(l.type&8)r[xu]??={},r[xu][c]=vh(o,e,l.child);else if(l.type&144){let d=l.next;for(;d!==null&&d.type&144;)d=d.next;d&&!No(d)&&Gn(r,d,e,i)}else if(l.type&1){let d=q(e[a]);Xf(n,d)}if(s&&l.type&2){let d=q(e[a]);s.has(d)&&Zf(d,s.get(d),t)}}}return r}function JI(e,t,n,r){Hd(t)||(t.projectionNext&&t.projectionNext!==t.next&&!No(t.projectionNext)&&Gn(e,t.projectionNext,n,r),t.prev===null&&t.parent!==null&&er(t.parent,n)&&!er(t,n)&&Gn(e,t,n,r))}function eC(e){let t=e[xe];return t?.constructor?Je(t.constructor)?.encapsulation===bn.ShadowDom:!1}function Go(e,t,n,r){let o=t[G];if(sy(t)&&!aw()||eC(t))return o.setAttribute(e,Mo,""),null;{let i=wh(t,n,r),s=r.serializedViewCollection.add(i);return o.setAttribute(e,Yf,s.toString()),s}}function dd(e,t){e.textContent=`ngh=${t}`}function tC(e,t){for(let[n,r]of e)n.after(t.createComment(r))}function nC(e){let t=e;for(;t!=null;){if(ir(t))return!0;t=t.parent}return!1}function rC(e,t,n,r){let o=Zv(e.hydrateTriggers);for(let i of o)r.eventTypesToReplay.regular.add(i);if(o.length>0){let i=t.filter(s=>s.nodeType===Node.ELEMENT_NODE);for(let s of i)Zf(s,o,n)}}function oC(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}var iC="\u{1F170}\uFE0F",_h=!1;function hR(e){if(!_h)return;let{startLabel:t}=Ih(e);performance.mark(t)}function gR(e){if(!_h)return;let{startLabel:t,labelName:n,endLabel:r}=Ih(e);performance.mark(r),performance.measure(n,t,r),performance.clearMarks(t),performance.clearMarks(r)}function Ih(e){let t=`${iC}:${e}`;return{labelName:t,startLabel:`start:${t}`,endLabel:`end:${t}`}}function mR(e,t){on("NgSignals");let n=Nc(e);return t?.equal&&(n[Ie].equal=t.equal),n}function sC(e){let t=P(null);try{return e()}finally{P(t)}}var fd=class{[Ie];constructor(t){this[Ie]=t}destroy(){this[Ie].destroy()}};function yR(e,t){let n=Je(e),r=t.elementInjector||Qo();return new Jt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function vR(e){let t=Je(e);if(!t)return null;let n=new Jt(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}function ER(...e){return e.reduce((t,n)=>Object.assign(t,n,{providers:[...t.providers,...n.providers]}),{providers:[]})}var DR=new C("",{providedIn:"platform",factory:()=>null}),wR=new C("",{providedIn:"platform",factory:()=>null}),_R=new C("",{providedIn:"platform",factory:()=>null});var Rh=null;function Nl(){return Rh}function QR(e){Rh??=e}var Th=class{};var Ph=new C(""),Ll=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:()=>S(aC),providedIn:"platform"})}return e})(),ZR=new C(""),aC=(()=>{class e extends Ll{_location;_history;_doc=S(Ph);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Nl().getBaseHref(this._doc)}onPopState(n){let r=Nl().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Nl().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Vl(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function bh(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function dt(e){return e&&e[0]!=="?"?"?"+e:e}var Ii=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:()=>S(lC),providedIn:"root"})}return e})(),kh=new C(""),lC=(()=>{class e extends Ii{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??S(Ph).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Vl(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+dt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+dt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+dt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(Y(Ll),Y(kh,8))};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),YR=(()=>{class e extends Ii{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Vl(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+dt(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+dt(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(Y(Ll),Y(kh,8))};static \u0275prov=V({token:e,factory:e.\u0275fac})}return e})(),cC=(()=>{class e{_subject=new Qe;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=fC(bh(Sh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+dt(r))}normalize(n){return e.stripTrailingSlash(dC(this._basePath,Sh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+dt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+dt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=dt;static joinWithSlash=Vl;static stripTrailingSlash=bh;static \u0275fac=function(r){return new(r||e)(Y(Ii))};static \u0275prov=V({token:e,factory:()=>uC(),providedIn:"root"})}return e})();function uC(){return new cC(Y(Ii))}function dC(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Sh(e){return e.replace(/\/index.html$/,"")}function fC(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var ve=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(ve||{}),H=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(H||{}),be=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(be||{}),bt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function pC(e){return Pe(e)[J.LocaleId]}function hC(e,t,n){let r=Pe(e),o=[r[J.DayPeriodsFormat],r[J.DayPeriodsStandalone]],i=ke(o,t);return ke(i,n)}function gC(e,t,n){let r=Pe(e),o=[r[J.DaysFormat],r[J.DaysStandalone]],i=ke(o,t);return ke(i,n)}function mC(e,t,n){let r=Pe(e),o=[r[J.MonthsFormat],r[J.MonthsStandalone]],i=ke(o,t);return ke(i,n)}function yC(e,t){let r=Pe(e)[J.Eras];return ke(r,t)}function hi(e,t){let n=Pe(e);return ke(n[J.DateFormat],t)}function gi(e,t){let n=Pe(e);return ke(n[J.TimeFormat],t)}function mi(e,t){let r=Pe(e)[J.DateTimeFormat];return ke(r,t)}function Ci(e,t){let n=Pe(e),r=n[J.NumberSymbols][t];if(typeof r>"u"){if(t===bt.CurrencyDecimal)return n[J.NumberSymbols][bt.Decimal];if(t===bt.CurrencyGroup)return n[J.NumberSymbols][bt.Group]}return r}function Fh(e){if(!e[J.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[J.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function vC(e){let t=Pe(e);return Fh(t),(t[J.ExtraData][2]||[]).map(r=>typeof r=="string"?Al(r):[Al(r[0]),Al(r[1])])}function EC(e,t,n){let r=Pe(e);Fh(r);let o=[r[J.ExtraData][0],r[J.ExtraData][1]],i=ke(o,t)||[];return ke(i,n)||[]}function ke(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Al(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var DC=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,yi={},wC=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function _C(e,t,n,r){let o=xC(e);t=ut(n,t)||t;let s=[],a;for(;t;)if(a=wC.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let l=o.getTimezoneOffset();r&&(l=Vh(r,l),o=AC(o,r,!0));let c="";return s.forEach(u=>{let d=MC(u);c+=d?d(o,n,l):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function _i(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function ut(e,t){let n=pC(e);if(yi[n]??={},yi[n][t])return yi[n][t];let r="";switch(t){case"shortDate":r=hi(e,be.Short);break;case"mediumDate":r=hi(e,be.Medium);break;case"longDate":r=hi(e,be.Long);break;case"fullDate":r=hi(e,be.Full);break;case"shortTime":r=gi(e,be.Short);break;case"mediumTime":r=gi(e,be.Medium);break;case"longTime":r=gi(e,be.Long);break;case"fullTime":r=gi(e,be.Full);break;case"short":let o=ut(e,"shortTime"),i=ut(e,"shortDate");r=vi(mi(e,be.Short),[o,i]);break;case"medium":let s=ut(e,"mediumTime"),a=ut(e,"mediumDate");r=vi(mi(e,be.Medium),[s,a]);break;case"long":let l=ut(e,"longTime"),c=ut(e,"longDate");r=vi(mi(e,be.Long),[l,c]);break;case"full":let u=ut(e,"fullTime"),d=ut(e,"fullDate");r=vi(mi(e,be.Full),[u,d]);break}return r&&(yi[n][t]=r),r}function vi(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function qe(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function IC(e,t){return qe(e,3).substring(0,t)}function te(e,t,n=0,r=!1,o=!1){return function(i,s){let a=CC(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return IC(a,t);let l=Ci(s,bt.MinusSign);return qe(a,t,l,r,o)}}function CC(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function $(e,t,n=ve.Format,r=!1){return function(o,i){return TC(o,i,e,t,n,r)}}function TC(e,t,n,r,o,i){switch(n){case 2:return mC(t,o,r)[e.getMonth()];case 1:return gC(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let c=vC(t),u=EC(t,o,r),d=c.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,g=s>=f.hours&&a>=f.minutes,v=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(g&&v)return!0}else if(g||v)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return hC(t,o,r)[s<12?0:1];case 3:return yC(t,r)[e.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function Ei(e){return function(t,n,r){let o=-1*r,i=Ci(n,bt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+qe(s,2,i)+qe(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+qe(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+qe(s,2,i)+":"+qe(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+qe(s,2,i)+":"+qe(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var bC=0,wi=4;function SC(e){let t=_i(e,bC,1).getDay();return _i(e,0,1+(t<=wi?wi:wi+7)-t)}function Lh(e){let t=e.getDay(),n=t===0?-3:wi-t;return _i(e.getFullYear(),e.getMonth(),e.getDate()+n)}function xl(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Lh(n),s=SC(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return qe(o,e,Ci(r,bt.MinusSign))}}function Di(e,t=!1){return function(n,r){let i=Lh(n).getFullYear();return qe(i,e,Ci(r,bt.MinusSign),t)}}var Ol={};function MC(e){if(Ol[e])return Ol[e];let t;switch(e){case"G":case"GG":case"GGG":t=$(3,H.Abbreviated);break;case"GGGG":t=$(3,H.Wide);break;case"GGGGG":t=$(3,H.Narrow);break;case"y":t=te(0,1,0,!1,!0);break;case"yy":t=te(0,2,0,!0,!0);break;case"yyy":t=te(0,3,0,!1,!0);break;case"yyyy":t=te(0,4,0,!1,!0);break;case"Y":t=Di(1);break;case"YY":t=Di(2,!0);break;case"YYY":t=Di(3);break;case"YYYY":t=Di(4);break;case"M":case"L":t=te(1,1,1);break;case"MM":case"LL":t=te(1,2,1);break;case"MMM":t=$(2,H.Abbreviated);break;case"MMMM":t=$(2,H.Wide);break;case"MMMMM":t=$(2,H.Narrow);break;case"LLL":t=$(2,H.Abbreviated,ve.Standalone);break;case"LLLL":t=$(2,H.Wide,ve.Standalone);break;case"LLLLL":t=$(2,H.Narrow,ve.Standalone);break;case"w":t=xl(1);break;case"ww":t=xl(2);break;case"W":t=xl(1,!0);break;case"d":t=te(2,1);break;case"dd":t=te(2,2);break;case"c":case"cc":t=te(7,1);break;case"ccc":t=$(1,H.Abbreviated,ve.Standalone);break;case"cccc":t=$(1,H.Wide,ve.Standalone);break;case"ccccc":t=$(1,H.Narrow,ve.Standalone);break;case"cccccc":t=$(1,H.Short,ve.Standalone);break;case"E":case"EE":case"EEE":t=$(1,H.Abbreviated);break;case"EEEE":t=$(1,H.Wide);break;case"EEEEE":t=$(1,H.Narrow);break;case"EEEEEE":t=$(1,H.Short);break;case"a":case"aa":case"aaa":t=$(0,H.Abbreviated);break;case"aaaa":t=$(0,H.Wide);break;case"aaaaa":t=$(0,H.Narrow);break;case"b":case"bb":case"bbb":t=$(0,H.Abbreviated,ve.Standalone,!0);break;case"bbbb":t=$(0,H.Wide,ve.Standalone,!0);break;case"bbbbb":t=$(0,H.Narrow,ve.Standalone,!0);break;case"B":case"BB":case"BBB":t=$(0,H.Abbreviated,ve.Format,!0);break;case"BBBB":t=$(0,H.Wide,ve.Format,!0);break;case"BBBBB":t=$(0,H.Narrow,ve.Format,!0);break;case"h":t=te(3,1,-12);break;case"hh":t=te(3,2,-12);break;case"H":t=te(3,1);break;case"HH":t=te(3,2);break;case"m":t=te(4,1);break;case"mm":t=te(4,2);break;case"s":t=te(5,1);break;case"ss":t=te(5,2);break;case"S":t=te(6,1);break;case"SS":t=te(6,2);break;case"SSS":t=te(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Ei(0);break;case"ZZZZZ":t=Ei(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Ei(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Ei(2);break;default:return null}return Ol[e]=t,t}function Vh(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function NC(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function AC(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=Vh(t,o);return NC(e,r*(i-o))}function xC(e){if(Mh(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return _i(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(DC))return OC(r)}let t=new Date(e);if(!Mh(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function OC(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,l,c),t}function Mh(e){return e instanceof Date&&!isNaN(e.valueOf())}function XR(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Rl=/\s+/,Nh=[],JR=(()=>{class e{_ngEl;_renderer;initialClasses=Nh;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(Rl):Nh}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(Rl):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(Rl).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(ye(rn),ye(Dl))};static \u0275dir=fi({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Pl=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},eP=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Pl(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Ah(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Ah(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(ye(On),ye(Xt),ye(Ml))};static \u0275dir=fi({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Ah(e,t){e.context.$implicit=t.item}var tP=(()=>{class e{_viewContainer;_context=new kl;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){xh("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){xh("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(ye(On),ye(Xt))};static \u0275dir=fi({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),kl=class{$implicit=null;ngIf=null};function xh(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${me(t)}'.`)}function RC(e,t){return new m(2100,!1)}var PC="mediumDate",kC=new C(""),FC=new C(""),nP=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??PC,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return _C(n,s,i||this.locale,a)}catch(s){throw RC(e,s.message)}}static \u0275fac=function(r){return new(r||e)(ye(pi,16),ye(kC,24),ye(FC,24))};static \u0275pipe=$p({name:"date",type:e,pure:!0})}return e})();var rP=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=_l({type:e});static \u0275inj=La({})}return e})(),LC="browser",VC="server";function oP(e){return e===LC}function iP(e){return e===VC}var sP=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>new Fl})}return e})();var Fl=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Oh=class{};var A=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(A||{}),nt="*";function jh(e,t=null){return{type:A.Sequence,steps:e,options:t}}function jl(e){return{type:A.Style,styles:e,offset:null}}var St=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,n=0){this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},dr=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Ti="!";function Bh(e){return new m(3e3,!1)}function jC(){return new m(3100,!1)}function BC(){return new m(3101,!1)}function HC(e){return new m(3001,!1)}function $C(e){return new m(3003,!1)}function UC(e){return new m(3004,!1)}function zC(e,t){return new m(3005,!1)}function qC(){return new m(3006,!1)}function GC(){return new m(3007,!1)}function WC(e,t){return new m(3008,!1)}function KC(e){return new m(3002,!1)}function QC(e,t,n,r,o){return new m(3010,!1)}function ZC(){return new m(3011,!1)}function YC(){return new m(3012,!1)}function XC(){return new m(3200,!1)}function JC(){return new m(3202,!1)}function eT(){return new m(3013,!1)}function tT(e){return new m(3014,!1)}function nT(e){return new m(3015,!1)}function rT(e){return new m(3016,!1)}function oT(e){return new m(3500,!1)}function iT(e){return new m(3501,!1)}function sT(e,t){return new m(3404,!1)}function aT(e){return new m(3502,!1)}function lT(e){return new m(3503,!1)}function cT(){return new m(3300,!1)}function uT(e){return new m(3504,!1)}function dT(e){return new m(3301,!1)}function fT(e,t){return new m(3302,!1)}function pT(e){return new m(3303,!1)}function hT(e,t){return new m(3400,!1)}function gT(e){return new m(3401,!1)}function mT(e){return new m(3402,!1)}function yT(e,t){return new m(3505,!1)}var vT=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function Mt(e){switch(e.length){case 0:return new St;case 1:return e[0];default:return new dr(e)}}function og(e,t,n=new Map,r=new Map){let o=[],i=[],s=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),u=c==s,d=u&&a||new Map;l.forEach((p,f)=>{let h=f,g=p;if(f!=="offset")switch(h=e.normalizePropertyName(h,o),g){case Ti:g=n.get(f);break;case nt:g=r.get(f);break;default:g=e.normalizeStyleValue(f,h,g,o);break}d.set(h,g)}),u||i.push(d),a=d,s=c}),o.length)throw aT(o);return i}function uc(e,t,n,r){switch(t){case"start":e.onStart(()=>r(n&&Bl(n,"start",e)));break;case"done":e.onDone(()=>r(n&&Bl(n,"done",e)));break;case"destroy":e.onDestroy(()=>r(n&&Bl(n,"destroy",e)));break}}function Bl(e,t,n){let r=n.totalTime,o=!!n.disabled,i=dc(e.element,e.triggerName,e.fromState,e.toState,t||e.phaseName,r??e.totalTime,o),s=e._data;return s!=null&&(i._data=s),i}function dc(e,t,n,r,o="",i=0,s){return{element:e,triggerName:t,fromState:n,toState:r,phaseName:o,totalTime:i,disabled:!!s}}function Me(e,t,n){let r=e.get(t);return r||e.set(t,r=n),r}function Hh(e){let t=e.indexOf(":"),n=e.substring(1,t),r=e.slice(t+1);return[n,r]}var ET=typeof document>"u"?null:document.documentElement;function fc(e){let t=e.parentNode||e.host||null;return t===ET?null:t}function DT(e){return e.substring(1,6)=="ebkit"}var sn=null,$h=!1;function wT(e){sn||(sn=_T()||{},$h=sn.style?"WebkitAppearance"in sn.style:!1);let t=!0;return sn.style&&!DT(e)&&(t=e in sn.style,!t&&$h&&(t="Webkit"+e.charAt(0).toUpperCase()+e.slice(1)in sn.style)),t}function pP(e){return vT.has(e)}function _T(){return typeof document<"u"?document.body:null}function ig(e,t){for(;t;){if(t===e)return!0;t=fc(t)}return!1}function sg(e,t,n){if(n)return Array.from(e.querySelectorAll(t));let r=e.querySelector(t);return r?[r]:[]}var ag=(()=>{class e{validateStyleProperty(n){return wT(n)}containsElement(n,r){return ig(n,r)}getParentElement(n){return fc(n)}query(n,r,o){return sg(n,r,o)}computeStyle(n,r,o){return o||""}animate(n,r,o,i,s,a=[],l){return new St(o,i)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac})}return e})(),Uh=class{static NOOP=new ag},Gl=class{},Wl=class{normalizePropertyName(t,n){return t}normalizeStyleValue(t,n,r,o){return r}},IT=1e3,lg="{{",CT="}}",pc="ng-enter",xi="ng-leave",bi="ng-trigger",Oi=".ng-trigger",zh="ng-animating",Kl=".ng-animating";function ft(e){if(typeof e=="number")return e;let t=e.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:Ql(parseFloat(t[1]),t[2])}function Ql(e,t){switch(t){case"s":return e*IT;default:return e}}function Ri(e,t,n){return e.hasOwnProperty("duration")?e:TT(e,t,n)}function TT(e,t,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,o,i=0,s="";if(typeof e=="string"){let a=e.match(r);if(a===null)return t.push(Bh(e)),{duration:0,delay:0,easing:""};o=Ql(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(i=Ql(parseFloat(l),a[4]));let c=a[5];c&&(s=c)}else o=e;if(!n){let a=!1,l=t.length;o<0&&(t.push(jC()),a=!0),i<0&&(t.push(BC()),a=!0),a&&t.splice(l,0,Bh(e))}return{duration:o,delay:i,easing:s}}function bT(e){return e.length?e[0]instanceof Map?e:e.map(t=>new Map(Object.entries(t))):[]}function qh(e){return Array.isArray(e)?new Map(...e):new Map(e)}function rt(e,t,n){t.forEach((r,o)=>{let i=hc(o);n&&!n.has(o)&&n.set(o,e.style[i]),e.style[i]=r})}function ln(e,t){t.forEach((n,r)=>{let o=hc(r);e.style[o]=""})}function fr(e){return Array.isArray(e)?e.length==1?e[0]:jh(e):e}function ST(e,t,n){let r=t.params||{},o=cg(e);o.length&&o.forEach(i=>{r.hasOwnProperty(i)||n.push(HC(i))})}var Zl=new RegExp(`${lg}\\s*(.+?)\\s*${CT}`,"g");function cg(e){let t=[];if(typeof e=="string"){let n;for(;n=Zl.exec(e);)t.push(n[1]);Zl.lastIndex=0}return t}function hr(e,t,n){let r=`${e}`,o=r.replace(Zl,(i,s)=>{let a=t[s];return a==null&&(n.push($C(s)),a=""),a.toString()});return o==r?e:o}var MT=/-+([a-z0-9])/g;function hc(e){return e.replace(MT,(...t)=>t[1].toUpperCase())}function hP(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function NT(e,t){return e===0||t===0}function AT(e,t,n){if(n.size&&t.length){let r=t[0],o=[];if(n.forEach((i,s)=>{r.has(s)||o.push(s),r.set(s,i)}),o.length)for(let i=1;i<t.length;i++){let s=t[i];o.forEach(a=>s.set(a,gc(e,a)))}}return t}function Se(e,t,n){switch(t.type){case A.Trigger:return e.visitTrigger(t,n);case A.State:return e.visitState(t,n);case A.Transition:return e.visitTransition(t,n);case A.Sequence:return e.visitSequence(t,n);case A.Group:return e.visitGroup(t,n);case A.Animate:return e.visitAnimate(t,n);case A.Keyframes:return e.visitKeyframes(t,n);case A.Style:return e.visitStyle(t,n);case A.Reference:return e.visitReference(t,n);case A.AnimateChild:return e.visitAnimateChild(t,n);case A.AnimateRef:return e.visitAnimateRef(t,n);case A.Query:return e.visitQuery(t,n);case A.Stagger:return e.visitStagger(t,n);default:throw UC(t.type)}}function gc(e,t){return window.getComputedStyle(e)[t]}var xT=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Yl=class extends Gl{normalizePropertyName(t,n){return hc(t)}normalizeStyleValue(t,n,r,o){let i="",s=r.toString().trim();if(xT.has(n)&&r!==0&&r!=="0")if(typeof r=="number")i="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&o.push(zC(t,r))}return s+i}};var Pi="*";function OT(e,t){let n=[];return typeof e=="string"?e.split(/\s*,\s*/).forEach(r=>RT(r,n,t)):n.push(e),n}function RT(e,t,n){if(e[0]==":"){let l=PT(e,n);if(typeof l=="function"){t.push(l);return}e=l}let r=e.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(nT(e)),t;let o=r[1],i=r[2],s=r[3];t.push(Gh(o,s));let a=o==Pi&&s==Pi;i[0]=="<"&&!a&&t.push(Gh(s,o))}function PT(e,t){switch(e){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return t.push(rT(e)),"* => *"}}var Si=new Set(["true","1"]),Mi=new Set(["false","0"]);function Gh(e,t){let n=Si.has(e)||Mi.has(e),r=Si.has(t)||Mi.has(t);return(o,i)=>{let s=e==Pi||e==o,a=t==Pi||t==i;return!s&&n&&typeof o=="boolean"&&(s=o?Si.has(e):Mi.has(e)),!a&&r&&typeof i=="boolean"&&(a=i?Si.has(t):Mi.has(t)),s&&a}}var ug=":self",kT=new RegExp(`s*${ug}s*,?`,"g");function mc(e,t,n,r){return new Xl(e).build(t,n,r)}var Wh="",Xl=class{_driver;constructor(t){this._driver=t}build(t,n,r){let o=new Jl(n);return this._resetContextStyleTimingState(o),Se(this,fr(t),o)}_resetContextStyleTimingState(t){t.currentQuerySelector=Wh,t.collectedStyles=new Map,t.collectedStyles.set(Wh,new Map),t.currentTime=0}visitTrigger(t,n){let r=n.queryCount=0,o=n.depCount=0,i=[],s=[];return t.name.charAt(0)=="@"&&n.errors.push(qC()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==A.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,i.push(this.visitState(l,n))}),l.name=c}else if(a.type==A.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,o+=l.depCount,s.push(l)}else n.errors.push(GC())}),{type:A.Trigger,name:t.name,states:i,transitions:s,queryCount:r,depCount:o,options:null}}visitState(t,n){let r=this.visitStyle(t.styles,n),o=t.options&&t.options.params||null;if(r.containsDynamicStyles){let i=new Set,s=o||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{cg(l).forEach(c=>{s.hasOwnProperty(c)||i.add(c)})})}),i.size&&n.errors.push(WC(t.name,[...i.values()]))}return{type:A.State,name:t.name,style:r,options:o?{params:o}:null}}visitTransition(t,n){n.queryCount=0,n.depCount=0;let r=Se(this,fr(t.animation),n),o=OT(t.expr,n.errors);return{type:A.Transition,matchers:o,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:an(t.options)}}visitSequence(t,n){return{type:A.Sequence,steps:t.steps.map(r=>Se(this,r,n)),options:an(t.options)}}visitGroup(t,n){let r=n.currentTime,o=0,i=t.steps.map(s=>{n.currentTime=r;let a=Se(this,s,n);return o=Math.max(o,n.currentTime),a});return n.currentTime=o,{type:A.Group,steps:i,options:an(t.options)}}visitAnimate(t,n){let r=jT(t.timings,n.errors);n.currentAnimateTimings=r;let o,i=t.styles?t.styles:jl({});if(i.type==A.Keyframes)o=this.visitKeyframes(i,n);else{let s=t.styles,a=!1;if(!s){a=!0;let c={};r.easing&&(c.easing=r.easing),s=jl(c)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,o=l}return n.currentAnimateTimings=null,{type:A.Animate,timings:r,style:o,options:null}}visitStyle(t,n){let r=this._makeStyleAst(t,n);return this._validateStyleAst(r,n),r}_makeStyleAst(t,n){let r=[],o=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of o)typeof a=="string"?a===nt?r.push(a):n.errors.push(KC(a)):r.push(new Map(Object.entries(a)));let i=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!i)){for(let l of a.values())if(l.toString().indexOf(lg)>=0){i=!0;break}}}),{type:A.Style,styles:r,easing:s,offset:t.offset,containsDynamicStyles:i,options:null}}_validateStyleAst(t,n){let r=n.currentAnimateTimings,o=n.currentTime,i=n.currentTime;r&&i>0&&(i-=r.duration+r.delay),t.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let c=n.collectedStyles.get(n.currentQuerySelector),u=c.get(l),d=!0;u&&(i!=o&&i>=u.startTime&&o<=u.endTime&&(n.errors.push(QC(l,u.startTime,u.endTime,i,o)),d=!1),i=u.startTime),d&&c.set(l,{startTime:i,endTime:o}),n.options&&ST(a,n.options,n.errors)})})}visitKeyframes(t,n){let r={type:A.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(ZC()),r;let o=1,i=0,s=[],a=!1,l=!1,c=0,u=t.steps.map(E=>{let R=this._makeStyleAst(E,n),j=R.offset!=null?R.offset:VT(R.styles),L=0;return j!=null&&(i++,L=R.offset=j),l=l||L<0||L>1,a=a||L<c,c=L,s.push(L),R});l&&n.errors.push(YC()),a&&n.errors.push(XC());let d=t.steps.length,p=0;i>0&&i<d?n.errors.push(JC()):i==0&&(p=o/(d-1));let f=d-1,h=n.currentTime,g=n.currentAnimateTimings,v=g.duration;return u.forEach((E,R)=>{let j=p>0?R==f?1:p*R:s[R],L=j*v;n.currentTime=h+g.delay+L,g.duration=L,this._validateStyleAst(E,n),E.offset=j,r.styles.push(E)}),r}visitReference(t,n){return{type:A.Reference,animation:Se(this,fr(t.animation),n),options:an(t.options)}}visitAnimateChild(t,n){return n.depCount++,{type:A.AnimateChild,options:an(t.options)}}visitAnimateRef(t,n){return{type:A.AnimateRef,animation:this.visitReference(t.animation,n),options:an(t.options)}}visitQuery(t,n){let r=n.currentQuerySelector,o=t.options||{};n.queryCount++,n.currentQuery=t;let[i,s]=FT(t.selector);n.currentQuerySelector=r.length?r+" "+i:i,Me(n.collectedStyles,n.currentQuerySelector,new Map);let a=Se(this,fr(t.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:A.Query,selector:i,limit:o.limit||0,optional:!!o.optional,includeSelf:s,animation:a,originalSelector:t.selector,options:an(t.options)}}visitStagger(t,n){n.currentQuery||n.errors.push(eT());let r=t.timings==="full"?{duration:0,delay:0,easing:"full"}:Ri(t.timings,n.errors,!0);return{type:A.Stagger,animation:Se(this,fr(t.animation),n),timings:r,options:null}}};function FT(e){let t=!!e.split(/\s*,\s*/).find(n=>n==ug);return t&&(e=e.replace(kT,"")),e=e.replace(/@\*/g,Oi).replace(/@\w+/g,n=>Oi+"-"+n.slice(1)).replace(/:animating/g,Kl),[e,t]}function LT(e){return e?ie({},e):null}var Jl=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(t){this.errors=t}};function VT(e){if(typeof e=="string")return null;let t=null;if(Array.isArray(e))e.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;t=parseFloat(r.get("offset")),r.delete("offset")}});else if(e instanceof Map&&e.has("offset")){let n=e;t=parseFloat(n.get("offset")),n.delete("offset")}return t}function jT(e,t){if(e.hasOwnProperty("duration"))return e;if(typeof e=="number"){let i=Ri(e,t).duration;return Hl(i,0,"")}let n=e;if(n.split(/\s+/).some(i=>i.charAt(0)=="{"&&i.charAt(1)=="{")){let i=Hl(0,0,"");return i.dynamic=!0,i.strValue=n,i}let o=Ri(n,t);return Hl(o.duration,o.delay,o.easing)}function an(e){return e?(e=ie({},e),e.params&&(e.params=LT(e.params))):e={},e}function Hl(e,t,n){return{duration:e,delay:t,easing:n}}function yc(e,t,n,r,o,i,s=null,a=!1){return{type:1,element:e,keyframes:t,preStyleProps:n,postStyleProps:r,duration:o,delay:i,totalTime:o+i,easing:s,subTimeline:a}}var Pn=class{_map=new Map;get(t){return this._map.get(t)||[]}append(t,n){let r=this._map.get(t);r||this._map.set(t,r=[]),r.push(...n)}has(t){return this._map.has(t)}clear(){this._map.clear()}},BT=1,HT=":enter",$T=new RegExp(HT,"g"),UT=":leave",zT=new RegExp(UT,"g");function vc(e,t,n,r,o,i=new Map,s=new Map,a,l,c=[]){return new ec().buildKeyframes(e,t,n,r,o,i,s,a,l,c)}var ec=class{buildKeyframes(t,n,r,o,i,s,a,l,c,u=[]){c=c||new Pn;let d=new tc(t,n,c,o,i,u,[]);d.options=l;let p=l.delay?ft(l.delay):0;d.currentTimeline.delayNextStep(p),d.currentTimeline.setStyles([s],null,d.errors,l),Se(this,r,d);let f=d.timelines.filter(h=>h.containsAnimation());if(f.length&&a.size){let h;for(let g=f.length-1;g>=0;g--){let v=f[g];if(v.element===n){h=v;break}}h&&!h.allowOnlyTimelineStyles()&&h.setStyles([a],null,d.errors,l)}return f.length?f.map(h=>h.buildKeyframes()):[yc(n,[],[],[],0,p,"",!1)]}visitTrigger(t,n){}visitState(t,n){}visitTransition(t,n){}visitAnimateChild(t,n){let r=n.subInstructions.get(n.element);if(r){let o=n.createSubContext(t.options),i=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,o,o.options);i!=s&&n.transformIntoNewTimeline(s)}n.previousNode=t}visitAnimateRef(t,n){let r=n.createSubContext(t.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],n,r),this.visitReference(t.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=t}_applyAnimationRefDelays(t,n,r){for(let o of t){let i=o?.delay;if(i){let s=typeof i=="number"?i:ft(hr(i,o?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(t,n,r){let i=n.currentTimeline.currentTime,s=r.duration!=null?ft(r.duration):null,a=r.delay!=null?ft(r.delay):null;return s!==0&&t.forEach(l=>{let c=n.appendInstructionToTimeline(l,s,a);i=Math.max(i,c.duration+c.delay)}),i}visitReference(t,n){n.updateOptions(t.options,!0),Se(this,t.animation,n),n.previousNode=t}visitSequence(t,n){let r=n.subContextCount,o=n,i=t.options;if(i&&(i.params||i.delay)&&(o=n.createSubContext(i),o.transformIntoNewTimeline(),i.delay!=null)){o.previousNode.type==A.Style&&(o.currentTimeline.snapshotCurrentStyles(),o.previousNode=ki);let s=ft(i.delay);o.delayNextStep(s)}t.steps.length&&(t.steps.forEach(s=>Se(this,s,o)),o.currentTimeline.applyStylesToKeyframe(),o.subContextCount>r&&o.transformIntoNewTimeline()),n.previousNode=t}visitGroup(t,n){let r=[],o=n.currentTimeline.currentTime,i=t.options&&t.options.delay?ft(t.options.delay):0;t.steps.forEach(s=>{let a=n.createSubContext(t.options);i&&a.delayNextStep(i),Se(this,s,a),o=Math.max(o,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(o),n.previousNode=t}_visitTiming(t,n){if(t.dynamic){let r=t.strValue,o=n.params?hr(r,n.params,n.errors):r;return Ri(o,n.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,n){let r=n.currentAnimateTimings=this._visitTiming(t.timings,n),o=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),o.snapshotCurrentStyles());let i=t.style;i.type==A.Keyframes?this.visitKeyframes(i,n):(n.incrementTime(r.duration),this.visitStyle(i,n),o.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=t}visitStyle(t,n){let r=n.currentTimeline,o=n.currentAnimateTimings;!o&&r.hasCurrentStyleProperties()&&r.forwardFrame();let i=o&&o.easing||t.easing;t.isEmptyStep?r.applyEmptyStep(i):r.setStyles(t.styles,i,n.errors,n.options),n.previousNode=t}visitKeyframes(t,n){let r=n.currentAnimateTimings,o=n.currentTimeline.duration,i=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*i),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(o+i),n.previousNode=t}visitQuery(t,n){let r=n.currentTimeline.currentTime,o=t.options||{},i=o.delay?ft(o.delay):0;i&&(n.previousNode.type===A.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=ki);let s=r,a=n.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!o.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{n.currentQueryIndex=u;let d=n.createSubContext(t.options,c);i&&d.delayNextStep(i),c===n.element&&(l=d.currentTimeline),Se(this,t.animation,d),d.currentTimeline.applyStylesToKeyframe();let p=d.currentTimeline.currentTime;s=Math.max(s,p)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=t}visitStagger(t,n){let r=n.parentContext,o=n.currentTimeline,i=t.timings,s=Math.abs(i.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(i.duration<0?"reverse":i.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let u=n.currentTimeline;l&&u.delayNextStep(l);let d=u.currentTime;Se(this,t.animation,n),n.previousNode=t,r.currentStaggerTime=o.currentTime-d+(o.startTime-r.currentTimeline.startTime)}},ki={},tc=class e{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=ki;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(t,n,r,o,i,s,a,l){this._driver=t,this.element=n,this.subInstructions=r,this._enterClassName=o,this._leaveClassName=i,this.errors=s,this.timelines=a,this.currentTimeline=l||new Fi(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,n){if(!t)return;let r=t,o=this.options;r.duration!=null&&(o.duration=ft(r.duration)),r.delay!=null&&(o.delay=ft(r.delay));let i=r.params;if(i){let s=o.params;s||(s=this.options.params={}),Object.keys(i).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=hr(i[a],s,this.errors))})}}_copyOptions(){let t={};if(this.options){let n=this.options.params;if(n){let r=t.params={};Object.keys(n).forEach(o=>{r[o]=n[o]})}}return t}createSubContext(t=null,n,r){let o=n||this.element,i=new e(this._driver,o,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(o,r||0));return i.previousNode=this.previousNode,i.currentAnimateTimings=this.currentAnimateTimings,i.options=this._copyOptions(),i.updateOptions(t),i.currentQueryIndex=this.currentQueryIndex,i.currentQueryTotal=this.currentQueryTotal,i.parentContext=this,this.subContextCount++,i}transformIntoNewTimeline(t){return this.previousNode=ki,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,n,r){let o={duration:n??t.duration,delay:this.currentTimeline.currentTime+(r??0)+t.delay,easing:""},i=new nc(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,o,t.stretchStartingKeyframe);return this.timelines.push(i),o}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,n,r,o,i,s){let a=[];if(o&&a.push(this.element),t.length>0){t=t.replace($T,"."+this._enterClassName),t=t.replace(zT,"."+this._leaveClassName);let l=r!=1,c=this._driver.query(this.element,t,l);r!==0&&(c=r<0?c.slice(c.length+r,c.length):c.slice(0,r)),a.push(...c)}return!i&&a.length==0&&s.push(tT(n)),a}},Fi=class e{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(t,n,r,o){this._driver=t,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=o,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+t),n&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,n){return this.applyStylesToKeyframe(),new e(this._driver,t,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=BT,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,n){this._localTimelineStyles.set(t,n),this._globalTimelineStyles.set(t,n),this._styleSummary.set(t,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||nt),this._currentKeyframe.set(n,nt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,n,r,o){n&&this._previousKeyframe.set("easing",n);let i=o&&o.params||{},s=qT(t,this._globalTimelineStyles);for(let[a,l]of s){let c=hr(l,i,r);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??nt),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,n)=>{this._currentKeyframe.set(n,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,t)}))}snapshotCurrentStyles(){for(let[t,n]of this._localTimelineStyles)this._pendingStyles.set(t,n),this._updateStyle(t,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let n in this._currentKeyframe)t.push(n);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((n,r)=>{let o=this._styleSummary.get(r);(!o||n.time>o.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,o=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,d)=>{u===Ti?t.add(d):u===nt&&n.add(d)}),r||c.set("offset",l/this.duration),o.push(c)});let i=[...t.values()],s=[...n.values()];if(r){let a=o[0],l=new Map(a);a.set("offset",0),l.set("offset",1),o=[a,l]}return yc(this.element,o,i,s,this.duration,this.startTime,this.easing,!1)}},nc=class extends Fi{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(t,n,r,o,i,s,a=!1){super(t,n,s.delay),this.keyframes=r,this.preStyleProps=o,this.postStyleProps=i,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:n,duration:r,easing:o}=this.timings;if(this._stretchStartingKeyframe&&n){let i=[],s=r+n,a=n/s,l=new Map(t[0]);l.set("offset",0),i.push(l);let c=new Map(t[0]);c.set("offset",Kh(a)),i.push(c);let u=t.length-1;for(let d=1;d<=u;d++){let p=new Map(t[d]),f=p.get("offset"),h=n+f*r;p.set("offset",Kh(h/s)),i.push(p)}r=s,n=0,o="",t=i}return yc(this.element,t,this.preStyleProps,this.postStyleProps,r,n,o,!0)}};function Kh(e,t=3){let n=Math.pow(10,t-1);return Math.round(e*n)/n}function qT(e,t){let n=new Map,r;return e.forEach(o=>{if(o==="*"){r??=t.keys();for(let i of r)n.set(i,nt)}else for(let[i,s]of o)n.set(i,s)}),n}function Qh(e,t,n,r,o,i,s,a,l,c,u,d,p){return{type:0,element:e,triggerName:t,isRemovalTransition:o,fromState:n,fromStyles:i,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:d,errors:p}}var $l={},Li=class{_triggerName;ast;_stateStyles;constructor(t,n,r){this._triggerName=t,this.ast=n,this._stateStyles=r}match(t,n,r,o){return GT(this.ast.matchers,t,n,r,o)}buildStyles(t,n,r){let o=this._stateStyles.get("*");return t!==void 0&&(o=this._stateStyles.get(t?.toString())||o),o?o.buildStyles(n,r):new Map}build(t,n,r,o,i,s,a,l,c,u){let d=[],p=this.ast.options&&this.ast.options.params||$l,f=a&&a.params||$l,h=this.buildStyles(r,f,d),g=l&&l.params||$l,v=this.buildStyles(o,g,d),E=new Set,R=new Map,j=new Map,L=o==="void",ce={params:dg(g,p),delay:this.ast.options?.delay},W=u?[]:vc(t,n,this.ast.animation,i,s,h,v,ce,c,d),K=0;return W.forEach(re=>{K=Math.max(re.duration+re.delay,K)}),d.length?Qh(n,this._triggerName,r,o,L,h,v,[],[],R,j,K,d):(W.forEach(re=>{let We=re.element,ue=Me(R,We,new Set);re.preStyleProps.forEach(Nt=>ue.add(Nt));let yr=Me(j,We,new Set);re.postStyleProps.forEach(Nt=>yr.add(Nt)),We!==n&&E.add(We)}),Qh(n,this._triggerName,r,o,L,h,v,W,[...E.values()],R,j,K))}};function GT(e,t,n,r,o){return e.some(i=>i(t,n,r,o))}function dg(e,t){let n=ie({},t);return Object.entries(e).forEach(([r,o])=>{o!=null&&(n[r]=o)}),n}var rc=class{styles;defaultParams;normalizer;constructor(t,n,r){this.styles=t,this.defaultParams=n,this.normalizer=r}buildStyles(t,n){let r=new Map,o=dg(t,this.defaultParams);return this.styles.styles.forEach(i=>{typeof i!="string"&&i.forEach((s,a)=>{s&&(s=hr(s,o,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function WT(e,t,n){return new oc(e,t,n)}var oc=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(t,n,r){this.name=t,this.ast=n,this._normalizer=r,n.states.forEach(o=>{let i=o.options&&o.options.params||{};this.states.set(o.name,new rc(o.style,i,r))}),Zh(this.states,"true","1"),Zh(this.states,"false","0"),n.transitions.forEach(o=>{this.transitionFactories.push(new Li(t,o,this.states))}),this.fallbackTransition=KT(t,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,n,r,o){return this.transitionFactories.find(s=>s.match(t,n,r,o))||null}matchStyles(t,n,r){return this.fallbackTransition.buildStyles(t,n,r)}};function KT(e,t,n){let r=[(s,a)=>!0],o={type:A.Sequence,steps:[],options:null},i={type:A.Transition,animation:o,matchers:r,options:null,queryCount:0,depCount:0};return new Li(e,i,t)}function Zh(e,t,n){e.has(t)?e.has(n)||e.set(n,e.get(t)):e.has(n)&&e.set(t,e.get(n))}var QT=new Pn,ic=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(t,n,r){this.bodyNode=t,this._driver=n,this._normalizer=r}register(t,n){let r=[],o=[],i=mc(this._driver,n,r,o);if(r.length)throw lT(r);this._animations.set(t,i)}_buildPlayer(t,n,r){let o=t.element,i=og(this._normalizer,t.keyframes,n,r);return this._driver.animate(o,i,t.duration,t.delay,t.easing,[],!0)}create(t,n,r={}){let o=[],i=this._animations.get(t),s,a=new Map;if(i?(s=vc(this._driver,n,i,pc,xi,new Map,new Map,r,QT,o),s.forEach(u=>{let d=Me(a,u.element,new Map);u.postStyleProps.forEach(p=>d.set(p,null))})):(o.push(cT()),s=[]),o.length)throw uT(o);a.forEach((u,d)=>{u.forEach((p,f)=>{u.set(f,this._driver.computeStyle(d,f,nt))})});let l=s.map(u=>{let d=a.get(u.element);return this._buildPlayer(u,new Map,d)}),c=Mt(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let n=this._getPlayer(t);n.destroy(),this._playersById.delete(t);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(t){let n=this._playersById.get(t);if(!n)throw dT(t);return n}listen(t,n,r,o){let i=dc(n,"","","");return uc(this._getPlayer(t),r,i,o),()=>{}}command(t,n,r,o){if(r=="register"){this.register(t,o[0]);return}if(r=="create"){let s=o[0]||{};this.create(t,n,s);return}let i=this._getPlayer(t);switch(r){case"play":i.play();break;case"pause":i.pause();break;case"reset":i.reset();break;case"restart":i.restart();break;case"finish":i.finish();break;case"init":i.init();break;case"setPosition":i.setPosition(parseFloat(o[0]));break;case"destroy":this.destroy(t);break}}},Yh="ng-animate-queued",ZT=".ng-animate-queued",Ul="ng-animate-disabled",YT=".ng-animate-disabled",XT="ng-star-inserted",JT=".ng-star-inserted",eb=[],fg={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},tb={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Ge="__ng_removed",gr=class{namespaceId;value;options;get params(){return this.options.params}constructor(t,n=""){this.namespaceId=n;let r=t&&t.hasOwnProperty("value"),o=r?t.value:t;if(this.value=rb(o),r){let i=t,{value:s}=i,a=_c(i,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let n=t.params;if(n){let r=this.options.params;Object.keys(n).forEach(o=>{r[o]==null&&(r[o]=n[o])})}}},pr="void",zl=new gr(pr),sc=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(t,n,r){this.id=t,this.hostElement=n,this._engine=r,this._hostClassName="ng-tns-"+t,Fe(n,this._hostClassName)}listen(t,n,r,o){if(!this._triggers.has(n))throw fT(r,n);if(r==null||r.length==0)throw pT(n);if(!ob(r))throw hT(r,n);let i=Me(this._elementListeners,t,[]),s={name:n,phase:r,callback:o};i.push(s);let a=Me(this._engine.statesByElement,t,new Map);return a.has(n)||(Fe(t,bi),Fe(t,bi+"-"+n),a.set(n,zl)),()=>{this._engine.afterFlush(()=>{let l=i.indexOf(s);l>=0&&i.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(t,n){return this._triggers.has(t)?!1:(this._triggers.set(t,n),!0)}_getTrigger(t){let n=this._triggers.get(t);if(!n)throw gT(t);return n}trigger(t,n,r,o=!0){let i=this._getTrigger(n),s=new mr(this.id,n,t),a=this._engine.statesByElement.get(t);a||(Fe(t,bi),Fe(t,bi+"-"+n),this._engine.statesByElement.set(t,a=new Map));let l=a.get(n),c=new gr(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(n,c),l||(l=zl),!(c.value===pr)&&l.value===c.value){if(!ab(l.params,c.params)){let g=[],v=i.matchStyles(l.value,l.params,g),E=i.matchStyles(c.value,c.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{ln(t,v),rt(t,E)})}return}let p=Me(this._engine.playersByElement,t,[]);p.forEach(g=>{g.namespaceId==this.id&&g.triggerName==n&&g.queued&&g.destroy()});let f=i.matchTransition(l.value,c.value,t,c.params),h=!1;if(!f){if(!o)return;f=i.fallbackTransition,h=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:n,transition:f,fromState:l,toState:c,player:s,isFallbackTransition:h}),h||(Fe(t,Yh),s.onStart(()=>{Rn(t,Yh)})),s.onDone(()=>{let g=this.players.indexOf(s);g>=0&&this.players.splice(g,1);let v=this._engine.playersByElement.get(t);if(v){let E=v.indexOf(s);E>=0&&v.splice(E,1)}}),this.players.push(s),p.push(s),s}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(n=>n.delete(t)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(o=>o.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let n=this._engine.playersByElement.get(t);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,n){let r=this._engine.driver.query(t,Oi,!0);r.forEach(o=>{if(o[Ge])return;let i=this._engine.fetchNamespacesByElement(o);i.size?i.forEach(s=>s.triggerLeaveAnimation(o,n,!1,!0)):this.clearElementCache(o)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(o=>this.clearElementCache(o)))}triggerLeaveAnimation(t,n,r,o){let i=this._engine.statesByElement.get(t),s=new Map;if(i){let a=[];if(i.forEach((l,c)=>{if(s.set(c,l.value),this._triggers.has(c)){let u=this.trigger(t,c,pr,o);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,n,s),r&&Mt(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let n=this._elementListeners.get(t),r=this._engine.statesByElement.get(t);if(n&&r){let o=new Set;n.forEach(i=>{let s=i.name;if(o.has(s))return;o.add(s);let l=this._triggers.get(s).fallbackTransition,c=r.get(s)||zl,u=new gr(pr),d=new mr(this.id,s,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:s,transition:l,fromState:c,toState:u,player:d,isFallbackTransition:!0})})}}removeNode(t,n){let r=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,n),this.triggerLeaveAnimation(t,n,!0))return;let o=!1;if(r.totalAnimations){let i=r.players.length?r.playersByQueriedElement.get(t):[];if(i&&i.length)o=!0;else{let s=t;for(;s=s.parentNode;)if(r.statesByElement.get(s)){o=!0;break}}}if(this.prepareLeaveAnimationListeners(t),o)r.markElementAsRemoved(this.id,t,!1,n);else{let i=t[Ge];(!i||i===fg)&&(r.afterFlush(()=>this.clearElementCache(t)),r.destroyInnerAnimations(t),r._onRemovalComplete(t,n))}}insertNode(t,n){Fe(t,this._hostClassName)}drainQueuedTransitions(t){let n=[];return this._queue.forEach(r=>{let o=r.player;if(o.destroyed)return;let i=r.element,s=this._elementListeners.get(i);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=dc(i,r.triggerName,r.fromState.value,r.toState.value);l._data=t,uc(r.player,a.phase,l,a.callback)}}),o.markedForDestroy?this._engine.afterFlush(()=>{o.destroy()}):n.push(r)}),this._queue=[],n.sort((r,o)=>{let i=r.transition.ast.depCount,s=o.transition.ast.depCount;return i==0||s==0?i-s:this._engine.driver.containsElement(r.element,o.element)?1:-1})}destroy(t){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},ac=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(t,n)=>{};_onRemovalComplete(t,n){this.onRemovalComplete(t,n)}constructor(t,n,r){this.bodyNode=t,this.driver=n,this._normalizer=r}get queuedPlayers(){let t=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&t.push(r)})}),t}createNamespace(t,n){let r=new sc(t,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[t]=r}_balanceNamespaceList(t,n){let r=this._namespaceList,o=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=o.get(a);if(l){let c=r.indexOf(l);r.splice(c+1,0,t),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(t)}else r.push(t);return o.set(n,t),t}register(t,n){let r=this._namespaceLookup[t];return r||(r=this.createNamespace(t,n)),r}registerTrigger(t,n,r){let o=this._namespaceLookup[t];o&&o.register(n,r)&&this.totalAnimations++}destroy(t,n){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(t);this.namespacesByHostElement.delete(r.hostElement);let o=this._namespaceList.indexOf(r);o>=0&&this._namespaceList.splice(o,1),r.destroy(n),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let n=new Set,r=this.statesByElement.get(t);if(r){for(let o of r.values())if(o.namespaceId){let i=this._fetchNamespace(o.namespaceId);i&&n.add(i)}}return n}trigger(t,n,r,o){if(Ni(n)){let i=this._fetchNamespace(t);if(i)return i.trigger(n,r,o),!0}return!1}insertNode(t,n,r,o){if(!Ni(n))return;let i=n[Ge];if(i&&i.setForRemoval){i.setForRemoval=!1,i.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(t){let s=this._fetchNamespace(t);s&&s.insertNode(n,r)}o&&this.collectEnterElement(n)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,n){n?this.disabledNodes.has(t)||(this.disabledNodes.add(t),Fe(t,Ul)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Rn(t,Ul))}removeNode(t,n,r){if(Ni(n)){let o=t?this._fetchNamespace(t):null;o?o.removeNode(n,r):this.markElementAsRemoved(t,n,!1,r);let i=this.namespacesByHostElement.get(n);i&&i.id!==t&&i.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(t,n,r,o,i){this.collectedLeaveElements.push(n),n[Ge]={namespaceId:t,setForRemoval:o,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:i}}listen(t,n,r,o,i){return Ni(n)?this._fetchNamespace(t).listen(n,r,o,i):()=>{}}_buildInstruction(t,n,r,o,i){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,r,o,t.fromState.options,t.toState.options,n,i)}destroyInnerAnimations(t){let n=this.driver.query(t,Oi,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(t,Kl,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(t){let n=this.playersByElement.get(t);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(t){let n=this.playersByQueriedElement.get(t);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return Mt(this.players).onDone(()=>t());t()})}processLeaveNode(t){let n=t[Ge];if(n&&n.setForRemoval){if(t[Ge]=fg,n.namespaceId){this.destroyInnerAnimations(t);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(t)}this._onRemovalComplete(t,n.setForRemoval)}t.classList?.contains(Ul)&&this.markElementAsDisabled(t,!1),this.driver.query(t,YT,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(t=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,o)=>this._balanceNamespaceList(r,o)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let o=this.collectedEnterElements[r];Fe(o,XT)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,t)}finally{for(let o=0;o<r.length;o++)r[o]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let o=this.collectedLeaveElements[r];this.processLeaveNode(o)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?Mt(n).onDone(()=>{r.forEach(o=>o())}):r.forEach(o=>o())}}reportError(t){throw mT(t)}_flushAnimations(t,n){let r=new Pn,o=[],i=new Map,s=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(y=>{u.add(y);let D=this.driver.query(y,ZT,!0);for(let _=0;_<D.length;_++)u.add(D[_])});let d=this.bodyNode,p=Array.from(this.statesByElement.keys()),f=eg(p,this.collectedEnterElements),h=new Map,g=0;f.forEach((y,D)=>{let _=pc+g++;h.set(D,_),y.forEach(k=>Fe(k,_))});let v=[],E=new Set,R=new Set;for(let y=0;y<this.collectedLeaveElements.length;y++){let D=this.collectedLeaveElements[y],_=D[Ge];_&&_.setForRemoval&&(v.push(D),E.add(D),_.hasAnimation?this.driver.query(D,JT,!0).forEach(k=>E.add(k)):R.add(D))}let j=new Map,L=eg(p,Array.from(E));L.forEach((y,D)=>{let _=xi+g++;j.set(D,_),y.forEach(k=>Fe(k,_))}),t.push(()=>{f.forEach((y,D)=>{let _=h.get(D);y.forEach(k=>Rn(k,_))}),L.forEach((y,D)=>{let _=j.get(D);y.forEach(k=>Rn(k,_))}),v.forEach(y=>{this.processLeaveNode(y)})});let ce=[],W=[];for(let y=this._namespaceList.length-1;y>=0;y--)this._namespaceList[y].drainQueuedTransitions(n).forEach(_=>{let k=_.player,oe=_.element;if(ce.push(k),this.collectedEnterElements.length){let de=oe[Ge];if(de&&de.setForMove){if(de.previousTriggersValues&&de.previousTriggersValues.has(_.triggerName)){let At=de.previousTriggersValues.get(_.triggerName),Ne=this.statesByElement.get(_.element);if(Ne&&Ne.has(_.triggerName)){let vr=Ne.get(_.triggerName);vr.value=At,Ne.set(_.triggerName,vr)}}k.destroy();return}}let Ke=!d||!this.driver.containsElement(d,oe),_e=j.get(oe),pt=h.get(oe),Q=this._buildInstruction(_,r,pt,_e,Ke);if(Q.errors&&Q.errors.length){W.push(Q);return}if(Ke){k.onStart(()=>ln(oe,Q.fromStyles)),k.onDestroy(()=>rt(oe,Q.toStyles)),o.push(k);return}if(_.isFallbackTransition){k.onStart(()=>ln(oe,Q.fromStyles)),k.onDestroy(()=>rt(oe,Q.toStyles)),o.push(k);return}let wc=[];Q.timelines.forEach(de=>{de.stretchStartingKeyframe=!0,this.disabledNodes.has(de.element)||wc.push(de)}),Q.timelines=wc,r.append(oe,Q.timelines);let gg={instruction:Q,player:k,element:oe};s.push(gg),Q.queriedElements.forEach(de=>Me(a,de,[]).push(k)),Q.preStyleProps.forEach((de,At)=>{if(de.size){let Ne=l.get(At);Ne||l.set(At,Ne=new Set),de.forEach((vr,$i)=>Ne.add($i))}}),Q.postStyleProps.forEach((de,At)=>{let Ne=c.get(At);Ne||c.set(At,Ne=new Set),de.forEach((vr,$i)=>Ne.add($i))})});if(W.length){let y=[];W.forEach(D=>{y.push(yT(D.triggerName,D.errors))}),ce.forEach(D=>D.destroy()),this.reportError(y)}let K=new Map,re=new Map;s.forEach(y=>{let D=y.element;r.has(D)&&(re.set(D,D),this._beforeAnimationBuild(y.player.namespaceId,y.instruction,K))}),o.forEach(y=>{let D=y.element;this._getPreviousPlayers(D,!1,y.namespaceId,y.triggerName,null).forEach(k=>{Me(K,D,[]).push(k),k.destroy()})});let We=v.filter(y=>tg(y,l,c)),ue=new Map;Jh(ue,this.driver,R,c,nt).forEach(y=>{tg(y,l,c)&&We.push(y)});let Nt=new Map;f.forEach((y,D)=>{Jh(Nt,this.driver,new Set(y),l,Ti)}),We.forEach(y=>{let D=ue.get(y),_=Nt.get(y);ue.set(y,new Map([...D?.entries()??[],..._?.entries()??[]]))});let Hi=[],Ec=[],Dc={};s.forEach(y=>{let{element:D,player:_,instruction:k}=y;if(r.has(D)){if(u.has(D)){_.onDestroy(()=>rt(D,k.toStyles)),_.disabled=!0,_.overrideTotalTime(k.totalTime),o.push(_);return}let oe=Dc;if(re.size>1){let _e=D,pt=[];for(;_e=_e.parentNode;){let Q=re.get(_e);if(Q){oe=Q;break}pt.push(_e)}pt.forEach(Q=>re.set(Q,oe))}let Ke=this._buildAnimation(_.namespaceId,k,K,i,Nt,ue);if(_.setRealPlayer(Ke),oe===Dc)Hi.push(_);else{let _e=this.playersByElement.get(oe);_e&&_e.length&&(_.parentPlayer=Mt(_e)),o.push(_)}}else ln(D,k.fromStyles),_.onDestroy(()=>rt(D,k.toStyles)),Ec.push(_),u.has(D)&&o.push(_)}),Ec.forEach(y=>{let D=i.get(y.element);if(D&&D.length){let _=Mt(D);y.setRealPlayer(_)}}),o.forEach(y=>{y.parentPlayer?y.syncPlayerEvents(y.parentPlayer):y.destroy()});for(let y=0;y<v.length;y++){let D=v[y],_=D[Ge];if(Rn(D,xi),_&&_.hasAnimation)continue;let k=[];if(a.size){let Ke=a.get(D);Ke&&Ke.length&&k.push(...Ke);let _e=this.driver.query(D,Kl,!0);for(let pt=0;pt<_e.length;pt++){let Q=a.get(_e[pt]);Q&&Q.length&&k.push(...Q)}}let oe=k.filter(Ke=>!Ke.destroyed);oe.length?ib(this,D,oe):this.processLeaveNode(D)}return v.length=0,Hi.forEach(y=>{this.players.push(y),y.onDone(()=>{y.destroy();let D=this.players.indexOf(y);this.players.splice(D,1)}),y.play()}),Hi}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,n,r,o,i){let s=[];if(n){let a=this.playersByQueriedElement.get(t);a&&(s=a)}else{let a=this.playersByElement.get(t);if(a){let l=!i||i==pr;a.forEach(c=>{c.queued||!l&&c.triggerName!=o||s.push(c)})}}return(r||o)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||o&&o!=a.triggerName))),s}_beforeAnimationBuild(t,n,r){let o=n.triggerName,i=n.element,s=n.isRemovalTransition?void 0:t,a=n.isRemovalTransition?void 0:o;for(let l of n.timelines){let c=l.element,u=c!==i,d=Me(r,c,[]);this._getPreviousPlayers(c,u,s,a,n.toState).forEach(f=>{let h=f.getRealPlayer();h.beforeDestroy&&h.beforeDestroy(),f.destroy(),d.push(f)})}ln(i,n.fromStyles)}_buildAnimation(t,n,r,o,i,s){let a=n.triggerName,l=n.element,c=[],u=new Set,d=new Set,p=n.timelines.map(h=>{let g=h.element;u.add(g);let v=g[Ge];if(v&&v.removedBeforeQueried)return new St(h.duration,h.delay);let E=g!==l,R=sb((r.get(g)||eb).map(K=>K.getRealPlayer())).filter(K=>{let re=K;return re.element?re.element===g:!1}),j=i.get(g),L=s.get(g),ce=og(this._normalizer,h.keyframes,j,L),W=this._buildPlayer(h,ce,R);if(h.subTimeline&&o&&d.add(g),E){let K=new mr(t,a,g);K.setRealPlayer(W),c.push(K)}return W});c.forEach(h=>{Me(this.playersByQueriedElement,h.element,[]).push(h),h.onDone(()=>nb(this.playersByQueriedElement,h.element,h))}),u.forEach(h=>Fe(h,zh));let f=Mt(p);return f.onDestroy(()=>{u.forEach(h=>Rn(h,zh)),rt(l,n.toStyles)}),d.forEach(h=>{Me(o,h,[]).push(f)}),f}_buildPlayer(t,n,r){return n.length>0?this.driver.animate(t.element,n,t.duration,t.delay,t.easing,r):new St(t.duration,t.delay)}},mr=class{namespaceId;triggerName;element;_player=new St;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(t,n,r){this.namespaceId=t,this.triggerName=n,this.element=r}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((n,r)=>{n.forEach(o=>uc(t,r,void 0,o))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let n=this._player;n.triggerCallback&&t.onStart(()=>n.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,n){Me(this._queuedCallbacks,t,[]).push(n)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let n=this._player;n.triggerCallback&&n.triggerCallback(t)}};function nb(e,t,n){let r=e.get(t);if(r){if(r.length){let o=r.indexOf(n);r.splice(o,1)}r.length==0&&e.delete(t)}return r}function rb(e){return e??null}function Ni(e){return e&&e.nodeType===1}function ob(e){return e=="start"||e=="done"}function Xh(e,t){let n=e.style.display;return e.style.display=t??"none",n}function Jh(e,t,n,r,o){let i=[];n.forEach(l=>i.push(Xh(l)));let s=[];r.forEach((l,c)=>{let u=new Map;l.forEach(d=>{let p=t.computeStyle(c,d,o);u.set(d,p),(!p||p.length==0)&&(c[Ge]=tb,s.push(c))}),e.set(c,u)});let a=0;return n.forEach(l=>Xh(l,i[a++])),s}function eg(e,t){let n=new Map;if(e.forEach(a=>n.set(a,[])),t.length==0)return n;let r=1,o=new Set(t),i=new Map;function s(a){if(!a)return r;let l=i.get(a);if(l)return l;let c=a.parentNode;return n.has(c)?l=c:o.has(c)?l=r:l=s(c),i.set(a,l),l}return t.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function Fe(e,t){e.classList?.add(t)}function Rn(e,t){e.classList?.remove(t)}function ib(e,t,n){Mt(n).onDone(()=>e.processLeaveNode(t))}function sb(e){let t=[];return pg(e,t),t}function pg(e,t){for(let n=0;n<e.length;n++){let r=e[n];r instanceof dr?pg(r.players,t):t.push(r)}}function ab(e,t){let n=Object.keys(e),r=Object.keys(t);if(n.length!=r.length)return!1;for(let o=0;o<n.length;o++){let i=n[o];if(!t.hasOwnProperty(i)||e[i]!==t[i])return!1}return!0}function tg(e,t,n){let r=n.get(e);if(!r)return!1;let o=t.get(e);return o?r.forEach(i=>o.add(i)):t.set(e,r),n.delete(e),!0}var Vi=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(t,n)=>{};constructor(t,n,r){this._driver=n,this._normalizer=r,this._transitionEngine=new ac(t.body,n,r),this._timelineEngine=new ic(t.body,n,r),this._transitionEngine.onRemovalComplete=(o,i)=>this.onRemovalComplete(o,i)}registerTrigger(t,n,r,o,i){let s=t+"-"+o,a=this._triggerCache[s];if(!a){let l=[],c=[],u=mc(this._driver,i,l,c);if(l.length)throw sT(o,l);a=WT(o,u,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,o,a)}register(t,n){this._transitionEngine.register(t,n)}destroy(t,n){this._transitionEngine.destroy(t,n)}onInsert(t,n,r,o){this._transitionEngine.insertNode(t,n,r,o)}onRemove(t,n,r){this._transitionEngine.removeNode(t,n,r)}disableAnimations(t,n){this._transitionEngine.markElementAsDisabled(t,n)}process(t,n,r,o){if(r.charAt(0)=="@"){let[i,s]=Hh(r),a=o;this._timelineEngine.command(i,n,s,a)}else this._transitionEngine.trigger(t,n,r,o)}listen(t,n,r,o,i){if(r.charAt(0)=="@"){let[s,a]=Hh(r);return this._timelineEngine.listen(s,n,a,i)}return this._transitionEngine.listen(t,n,r,o,i)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function lb(e,t){let n=null,r=null;return Array.isArray(t)&&t.length?(n=ql(t[0]),t.length>1&&(r=ql(t[t.length-1]))):t instanceof Map&&(n=ql(t)),n||r?new cb(e,n,r):null}var cb=(()=>{class e{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(n,r,o){this._element=n,this._startStyles=r,this._endStyles=o;let i=e.initialStylesByElement.get(n);i||e.initialStylesByElement.set(n,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&rt(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(rt(this._element,this._initialStyles),this._endStyles&&(rt(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(e.initialStylesByElement.delete(this._element),this._startStyles&&(ln(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(ln(this._element,this._endStyles),this._endStyles=null),rt(this._element,this._initialStyles),this._state=3)}}return e})();function ql(e){let t=null;return e.forEach((n,r)=>{ub(r)&&(t=t||new Map,t.set(r,n))}),t}function ub(e){return e==="display"||e==="position"}var ji=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(t,n,r,o){this.element=t,this.keyframes=n,this.options=r,this._specialStyles=o,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let n=[];return t.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(t,n,r){return t.animate(this._convertKeyframesToObject(n),r)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,o)=>{o!=="offset"&&t.set(o,this._finished?r:gc(this.element,o))}),this.currentSnapshot=t}triggerCallback(t){let n=t==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},lc=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,n){return ig(t,n)}getParentElement(t){return fc(t)}query(t,n,r){return sg(t,n,r)}computeStyle(t,n,r){return gc(t,n)}animate(t,n,r,o,i,s=[]){let a=o==0?"both":"forwards",l={duration:r,delay:o,fill:a};i&&(l.easing=i);let c=new Map,u=s.filter(f=>f instanceof ji);NT(r,o)&&u.forEach(f=>{f.currentSnapshot.forEach((h,g)=>c.set(g,h))});let d=bT(n).map(f=>new Map(f));d=AT(t,d,c);let p=lb(t,d);return new ji(t,d,l,p)}};function gP(e,t){return e==="noop"?new Vi(t,new ag,new Wl):new Vi(t,new lc,new Yl)}var ng=class{_driver;_animationAst;constructor(t,n){this._driver=t;let r=[],i=mc(t,n,r,[]);if(r.length)throw oT(r);this._animationAst=i}buildTimelines(t,n,r,o,i){let s=Array.isArray(n)?qh(n):n,a=Array.isArray(r)?qh(r):r,l=[];i=i||new Pn;let c=vc(this._driver,t,this._animationAst,pc,xi,s,a,o,i,l);if(l.length)throw iT(l);return c}},Ai="@",hg="@.disabled",Bi=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(t,n,r,o){this.namespaceId=t,this.delegate=n,this.engine=r,this._onDestroy=o}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,n){return this.delegate.createElement(t,n)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,n){this.delegate.appendChild(t,n),this.engine.onInsert(this.namespaceId,n,t,!1)}insertBefore(t,n,r,o=!0){this.delegate.insertBefore(t,n,r),this.engine.onInsert(this.namespaceId,n,t,o)}removeChild(t,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(t,n){return this.delegate.selectRootElement(t,n)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,n,r,o){this.delegate.setAttribute(t,n,r,o)}removeAttribute(t,n,r){this.delegate.removeAttribute(t,n,r)}addClass(t,n){this.delegate.addClass(t,n)}removeClass(t,n){this.delegate.removeClass(t,n)}setStyle(t,n,r,o){this.delegate.setStyle(t,n,r,o)}removeStyle(t,n,r){this.delegate.removeStyle(t,n,r)}setProperty(t,n,r){n.charAt(0)==Ai&&n==hg?this.disableAnimations(t,!!r):this.delegate.setProperty(t,n,r)}setValue(t,n){this.delegate.setValue(t,n)}listen(t,n,r,o){return this.delegate.listen(t,n,r,o)}disableAnimations(t,n){this.engine.disableAnimations(t,n)}},cc=class extends Bi{factory;constructor(t,n,r,o,i){super(n,r,o,i),this.factory=t,this.namespaceId=n}setProperty(t,n,r){n.charAt(0)==Ai?n.charAt(1)=="."&&n==hg?(r=r===void 0?!0:!!r,this.disableAnimations(t,r)):this.engine.process(this.namespaceId,t,n.slice(1),r):this.delegate.setProperty(t,n,r)}listen(t,n,r,o){if(n.charAt(0)==Ai){let i=db(t),s=n.slice(1),a="";return s.charAt(0)!=Ai&&([s,a]=fb(s)),this.engine.listen(this.namespaceId,i,s,a,l=>{let c=l._data||-1;this.factory.scheduleListenerCallback(c,r,l)})}return this.delegate.listen(t,n,r,o)}};function db(e){switch(e){case"body":return document.body;case"document":return document;case"window":return window;default:return e}}function fb(e){let t=e.indexOf("."),n=e.substring(0,t),r=e.slice(t+1);return[n,r]}var rg=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(t,n,r){this.delegate=t,this.engine=n,this._zone=r,n.onRemovalComplete=(o,i)=>{i?.removeChild(null,o)}}createRenderer(t,n){let r="",o=this.delegate.createRenderer(t,n);if(!t||!n?.data?.animation){let c=this._rendererCache,u=c.get(o);if(!u){let d=()=>c.delete(o);u=new Bi(r,o,this.engine,d),c.set(o,u)}return u}let i=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(i,s,t,c.name,c)};return n.data.animation.forEach(a),new cc(this,s,o,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,n,r){if(t>=0&&t<this._microtaskId){this._zone.run(()=>n(r));return}let o=this._animationCallbacksBuffer;o.length==0&&queueMicrotask(()=>{this._zone.run(()=>{o.forEach(i=>{let[s,a]=i;s(a)}),this._animationCallbacksBuffer=[]})}),o.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(t){this.engine.flush(),this.delegate.componentReplaced?.(t)}};export{ee as a,Tg as b,F as c,as as d,ls as e,Qe as f,Vn as g,Pt as h,Ye as i,kg as j,Fg as k,Lg as l,ot as m,Vg as n,Ft as o,Gg as p,Lt as q,Hn as r,Yr as s,Kg as t,Qg as u,Zg as v,Vt as w,Yg as x,au as y,Xg as z,Jg as A,$n as B,ds as C,em as D,rm as E,om as F,fs as G,im as H,sm as I,am as J,lm as K,cm as L,um as M,dm as N,m as O,it as P,vd as Q,V as R,La as S,yO as T,C as U,x as V,Y as W,S as X,jm as Y,Gm as Z,kd as _,Dt as $,Ld as aa,ay as ba,vO as ca,EO as da,DO as ea,wO as fa,Zy as ga,Ue as ha,Zt as ia,oi as ja,mt as ka,fe as la,Tn as ma,_O as na,rn as oa,on as pa,IO as qa,CO as ra,Bf as sa,Ev as ta,Hf as ua,TO as va,bO as wa,$f as xa,SO as ya,ii as za,Pv as Aa,kv as Ba,MO as Ca,bn as Da,lr as Ea,ep as Fa,NO as Ga,AO as Ha,xO as Ia,OO as Ja,RO as Ka,tp as La,PO as Ma,sl as Na,yE as Oa,kO as Pa,FO as Qa,LO as Ra,ye as Sa,VO as Ta,xo as Ua,Xt as Va,Po as Wa,Dl as Xa,On as Ya,Ct as Za,Ca as _a,Kw as $a,$O as ab,_l as bb,fi as cb,$p as db,t_ as eb,a_ as fb,h_ as gb,UO as hb,y_ as ib,v_ as jb,zO as kb,E_ as lb,Tl as mb,w_ as nb,I_ as ob,Mn as pb,M_ as qb,B_ as rb,H_ as sb,qO as tb,rh as ub,oh as vb,eI as wb,rI as xb,oI as yb,GO as zb,sI as Ab,pI as Bb,WO as Cb,KO as Db,QO as Eb,ZO as Fb,YO as Gb,XO as Hb,JO as Ib,yI as Jb,sh as Kb,EI as Lb,eR as Mb,DI as Nb,tR as Ob,nR as Pb,rR as Qb,oR as Rb,iR as Sb,sR as Tb,aR as Ub,lR as Vb,cR as Wb,pi as Xb,jI as Yb,fh as Zb,$I as _b,mh as $b,uR as ac,dR as bc,fR as cc,pR as dc,oC as ec,hR as fc,gR as gc,mR as hc,sC as ic,yR as jc,vR as kc,ER as lc,DR as mc,wR as nc,_R as oc,Nl as pc,QR as qc,Th as rc,Ph as sc,Ll as tc,ZR as uc,Ii as vc,kh as wc,lC as xc,YR as yc,cC as zc,XR as Ac,JR as Bc,eP as Cc,tP as Dc,nP as Ec,rP as Fc,LC as Gc,VC as Hc,oP as Ic,iP as Jc,sP as Kc,Fl as Lc,Oh as Mc,fc as Nc,wT as Oc,pP as Pc,ig as Qc,sg as Rc,ag as Sc,Uh as Tc,Gl as Uc,Wl as Vc,bT as Wc,hP as Xc,NT as Yc,Yl as Zc,Vi as _c,ji as $c,lc as ad,gP as bd,ng as cd,Bi as dd,cc as ed,rg as fd};
