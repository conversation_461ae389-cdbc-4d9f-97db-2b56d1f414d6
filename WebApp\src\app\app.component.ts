import { Component, OnInit, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from './header/header.component';
import { ServiceProxyModule } from '../service-proxies/service-proxy.module';
import { PlatformService } from './services/platform.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, HeaderComponent, ServiceProxyModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'ExamEase';
  private platformService = inject(PlatformService);

  ngOnInit(): void {
    if (this.platformService.isBrowser) {
      console.log('[App] Client-side hydration complete');
      this.initializeClientFeatures();
    } else {
      console.log('[App] Server-side rendering');
    }
  }

  private initializeClientFeatures(): void {
    // Initialize client-side only features
  }
}
