import { Component } from '@angular/core';
import { RouterLink, RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { SubjectServiceProxy } from '../../service-proxies/service-proxies';
import { Subject } from '../../service-proxies/service-proxies';
import { CustomDeleteDialogComponent } from '../dialogs/custom-delete-dialog/custom-delete-dialog.component';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { getRemoteServiceBaseUrl } from '../app.config';
import { WindowService } from '../services/window.service';
import { BaseSSRComponent } from '../shared/base-ssr.component';

@Component({
  selector: 'app-subject-list',
  templateUrl: './subject-list.component.html',
  providers: [SubjectServiceProxy],
  imports: [ReactiveFormsModule, CommonModule, RouterModule, DatePipe]
})
export class SubjectListComponent extends BaseSSRComponent {
  subjects: Subject[] = [];
  isDeleting = false;

  constructor(
    private subjectService: SubjectServiceProxy,
    private dialog: MatDialog,
    private windowService: WindowService
  ) {
    super();
  }

  protected setSEOData(): void {
    this.seoService.setSubjectsPageSEO();
  }

  protected initializeComponent(): void {
    this.loadSubjects();
  }

  protected override handleClientSideAuth(): void {
    if (!this.authService.isAuthenticated()) {
      console.log('[SubjectList] User not authenticated, redirecting to login');
      this.router.navigate(['/login']);
    }
  }

  loadSubjects(): void {
    this.setLoadingState(true);

    this.safeSubscribe(
      this.subjectService.getAllSubject().pipe(
        finalize(() => this.setLoadingState(false))
      ),
      (result: Subject[]) => {
        this.subjects = result;
        console.log('[SubjectList] Loaded subjects:', result.length);

        // Store in transfer state for future use
        if (this.platformService.isServer) {
          this.transferStateService.setSubjects(result);
        }
      },
      (error: any) => {
        console.error('[SubjectList] Error loading subjects:', error);
      },
      'subjects' // Transfer state key
    );
  }

  addSubject(): void {
    this.router.navigate(['/subjects/create']);
  }

  editSubject(id: number): void {
    this.router.navigate(['/subjects/edit', id]);
  }

  deleteSubject(subject: Subject): void {
    const dialogRef = this.dialog.open(CustomDeleteDialogComponent, {
      width: '400px',
      data: { title: 'Delete Subject', message: `Are you sure you want to delete Subject ${subject.name}?` }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isDeleting = true;
        this.subjectService.delete(subject.id)
          .pipe(finalize(() => this.isDeleting = false))
          .subscribe(
            () => {
              this.loadSubjects();
            },
            error => {
              console.error('Error deleting subject:', error);
            }
          );
      }
    });
  }

  openPdf(fileName: string): void {
    const baseUrl = getRemoteServiceBaseUrl();
    this.windowService.open(`${baseUrl}/static/subject/${fileName}`, '_blank');
  }

  viewSubject(id: number): void {
    this.router.navigate(['/subjects/view', id]);
  }
}
