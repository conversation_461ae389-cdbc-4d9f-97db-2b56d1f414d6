import { Injectable } from '@angular/core';
import { PlatformService } from './platform.service';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private memoryStorage: Map<string, string> = new Map();

  constructor(private platformService: PlatformService) {}

  setItem(key: string, value: string): void {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      localStorage.setItem(key, value);
    } else {
      // Fallback to memory storage on server
      this.memoryStorage.set(key, value);
    }
  }

  getItem(key: string): string | null {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      return localStorage.getItem(key);
    } else {
      // Fallback to memory storage on server
      return this.memoryStorage.get(key) || null;
    }
  }

  removeItem(key: string): void {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      localStorage.removeItem(key);
    } else {
      // Fallback to memory storage on server
      this.memoryStorage.delete(key);
    }
  }

  clear(): void {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      localStorage.clear();
    } else {
      // Fallback to memory storage on server
      this.memoryStorage.clear();
    }
  }

  key(index: number): string | null {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      return localStorage.key(index);
    } else {
      // Fallback to memory storage on server
      const keys = Array.from(this.memoryStorage.keys());
      return keys[index] || null;
    }
  }

  get length(): number {
    const localStorage = this.platformService.getLocalStorage();
    if (localStorage) {
      return localStorage.length;
    } else {
      // Fallback to memory storage on server
      return this.memoryStorage.size;
    }
  }
}
