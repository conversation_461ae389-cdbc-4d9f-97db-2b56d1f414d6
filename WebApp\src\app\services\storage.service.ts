import { Injectable, inject } from '@angular/core';
import { PlatformService } from './platform.service';

export interface StorageItem {
  value: any;
  expiry?: number;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private platformService = inject(PlatformService);
  private serverStorage = new Map<string, StorageItem>();

  constructor() {
    if (this.platformService.isServer) {
      console.log('[StorageService] Initialized for server-side rendering');
    } else {
      console.log('[StorageService] Initialized for client-side');
    }
  }

  setItem(key: string, value: any, expiryMinutes?: number): boolean {
    try {
      const item: StorageItem = {
        value: value,
        timestamp: Date.now(),
        expiry: expiryMinutes ? Date.now() + (expiryMinutes * 60 * 1000) : undefined
      };

      if (this.platformService.isBrowser) {
        const localStorage = this.platformService.safeLocalStorage();
        if (localStorage) {
          localStorage.setItem(key, JSON.stringify(item));
          return true;
        }
      } else {
        this.serverStorage.set(key, item);
        return true;
      }
    } catch (error) {
      console.error(`[StorageService] Error storing item ${key}:`, error);
    }
    return false;
  }

  getItem<T = any>(key: string): T | null {
    try {
      let itemStr: string | null = null;

      if (this.platformService.isBrowser) {
        const localStorage = this.platformService.safeLocalStorage();
        itemStr = localStorage?.getItem(key) || null;
      } else {
        const item = this.serverStorage.get(key);
        if (item) {
          itemStr = JSON.stringify(item);
        }
      }

      if (!itemStr) {
        return null;
      }

      const item: StorageItem = JSON.parse(itemStr);

      if (item.expiry && Date.now() > item.expiry) {
        this.removeItem(key);
        return null;
      }

      return item.value as T;
    } catch (error) {
      console.error(`[StorageService] Error retrieving item ${key}:`, error);
      return null;
    }
  }

  removeItem(key: string): boolean {
    try {
      if (this.platformService.isBrowser) {
        const localStorage = this.platformService.safeLocalStorage();
        if (localStorage) {
          localStorage.removeItem(key);
          return true;
        }
      } else {
        return this.serverStorage.delete(key);
      }
    } catch (error) {
      console.error(`[StorageService] Error removing item ${key}:`, error);
    }
    return false;
  }

  clear(): boolean {
    try {
      if (this.platformService.isBrowser) {
        const localStorage = this.platformService.safeLocalStorage();
        if (localStorage) {
          localStorage.clear();
          return true;
        }
      } else {
        this.serverStorage.clear();
        return true;
      }
    } catch (error) {
      console.error('[StorageService] Error clearing storage:', error);
    }
    return false;
  }

  setSessionItem(key: string, value: any): boolean {
    try {
      const item: StorageItem = {
        value: value,
        timestamp: Date.now()
      };

      if (this.platformService.isBrowser) {
        const sessionStorage = this.platformService.safeSessionStorage();
        if (sessionStorage) {
          sessionStorage.setItem(key, JSON.stringify(item));
          return true;
        }
      } else {
        this.serverStorage.set(`session_${key}`, item);
        return true;
      }
    } catch (error) {
      console.error(`[StorageService] Error storing session item ${key}:`, error);
    }
    return false;
  }

  getSessionItem<T = any>(key: string): T | null {
    try {
      let itemStr: string | null = null;

      if (this.platformService.isBrowser) {
        const sessionStorage = this.platformService.safeSessionStorage();
        itemStr = sessionStorage?.getItem(key) || null;
      } else {
        const item = this.serverStorage.get(`session_${key}`);
        if (item) {
          itemStr = JSON.stringify(item);
        }
      }

      if (!itemStr) {
        return null;
      }

      const item: StorageItem = JSON.parse(itemStr);
      return item.value as T;
    } catch (error) {
      console.error(`[StorageService] Error retrieving session item ${key}:`, error);
      return null;
    }
  }

  removeSessionItem(key: string): boolean {
    try {
      if (this.platformService.isBrowser) {
        const sessionStorage = this.platformService.safeSessionStorage();
        if (sessionStorage) {
          sessionStorage.removeItem(key);
          return true;
        }
      } else {
        return this.serverStorage.delete(`session_${key}`);
      }
    } catch (error) {
      console.error(`[StorageService] Error removing session item ${key}:`, error);
    }
    return false;
  }

  hasItem(key: string): boolean {
    return this.getItem(key) !== null;
  }

  hasSessionItem(key: string): boolean {
    return this.getSessionItem(key) !== null;
  }

  getAllKeys(): string[] {
    if (this.platformService.isBrowser) {
      const localStorage = this.platformService.safeLocalStorage();
      if (localStorage) {
        return Object.keys(localStorage);
      }
    } else {
      return Array.from(this.serverStorage.keys());
    }
    return [];
  }

  key(index: number): string | null {
    const keys = this.getAllKeys();
    return keys[index] || null;
  }

  get length(): number {
    return this.getAllKeys().length;
  }
}
