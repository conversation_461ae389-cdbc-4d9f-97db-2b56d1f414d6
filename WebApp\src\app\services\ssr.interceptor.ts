import { inject } from '@angular/core';
import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { PlatformService } from './platform.service';

export const ssrInterceptor: HttpInterceptorFn = (req, next) => {
  const platformService = inject(PlatformService);

  // On server-side, return mock responses to prevent API calls
  if (platformService.isServer) {
    console.log(`[SSR Interceptor] Intercepting ${req.method} ${req.url}`);

    // Return appropriate mock responses based on the endpoint
    return getMockResponse(req);
  }

  // On client-side, proceed with normal HTTP requests
  return next(req);
};

function getMockResponse(req: any): any {
    const url = req.url.toLowerCase();

    // Mock responses for different endpoints
    if (url.includes('/subject') && req.method === 'GET') {
      return of(new HttpResponse({
        status: 200,
        body: [] // Empty subjects array for SSR
      }));
    }

    if (url.includes('/examschedule') && req.method === 'GET') {
      return of(new HttpResponse({
        status: 200,
        body: [] // Empty exams array for SSR
      }));
    }

    if (url.includes('/studyplan') && req.method === 'GET') {
      return of(new HttpResponse({
        status: 200,
        body: {
          studyTasks: [],
          studyPlan: null
        }
      }));
    }

    // For authentication endpoints, return unauthorized on server
    if (url.includes('/auth') || url.includes('/login')) {
      return throwError(() => new Error('Authentication not available on server'));
    }

    // Default mock response
    return of(new HttpResponse({
      status: 200,
      body: {}
    }));
}
