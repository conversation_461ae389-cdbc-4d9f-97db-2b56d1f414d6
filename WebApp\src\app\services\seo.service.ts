import { Injectable, Inject } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { DOCUMENT } from '@angular/common';
import { filter, map } from 'rxjs/operators';
import { PlatformService } from './platform.service';

export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SEOService {
  private defaultSEO: SEOData = {
    title: 'ExamEase - AI-Powered Study Planning Platform',
    description: 'Transform your exam preparation with AI-powered study plans, intelligent scheduling, and personalized learning support. Join thousands of students achieving academic success.',
    keywords: 'exam preparation, study planning, AI tutoring, academic success, student tools, exam scheduling',
    image: '/assets/images/examease-og.png',
    type: 'website',
    author: 'ExamEase Team'
  };

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private platformService: PlatformService,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.initializeRouteBasedSEO();
  }

  private initializeRouteBasedSEO(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        map(() => {
          let route = this.activatedRoute;
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        })
      )
      .subscribe(route => {
        const routeData = route.snapshot.data;
        const routeTitle = route.snapshot.title;
        
        this.updateSEO({
          title: routeTitle || this.defaultSEO.title,
          description: routeData['description'] || this.defaultSEO.description,
          keywords: routeData['keywords'] || this.defaultSEO.keywords,
          ...routeData['seo']
        });
      });
  }

  updateSEO(seoData: SEOData): void {
    const data = { ...this.defaultSEO, ...seoData };
    
    // Update title
    this.title.setTitle(data.title!);
    
    // Update meta tags
    this.updateMetaTag('description', data.description!);
    this.updateMetaTag('keywords', data.keywords!);
    this.updateMetaTag('author', data.author!);
    
    // Open Graph tags
    this.updateMetaProperty('og:title', data.title!);
    this.updateMetaProperty('og:description', data.description!);
    this.updateMetaProperty('og:type', data.type!);
    this.updateMetaProperty('og:image', data.image!);
    
    if (data.url) {
      this.updateMetaProperty('og:url', data.url);
    }
    
    if (data.publishedTime) {
      this.updateMetaProperty('article:published_time', data.publishedTime);
    }
    
    if (data.modifiedTime) {
      this.updateMetaProperty('article:modified_time', data.modifiedTime);
    }
    
    // Twitter Card tags
    this.updateMetaName('twitter:card', 'summary_large_image');
    this.updateMetaName('twitter:title', data.title!);
    this.updateMetaName('twitter:description', data.description!);
    this.updateMetaName('twitter:image', data.image!);
    
    // Canonical URL
    this.updateCanonicalUrl(data.url);
    
    // Structured data
    this.updateStructuredData(data);
  }

  private updateMetaTag(name: string, content: string): void {
    this.meta.updateTag({ name, content });
  }

  private updateMetaProperty(property: string, content: string): void {
    this.meta.updateTag({ property, content });
  }

  private updateMetaName(name: string, content: string): void {
    this.meta.updateTag({ name, content });
  }

  private updateCanonicalUrl(url?: string): void {
    if (!this.platformService.isBrowser) return;
    
    const head = this.document.getElementsByTagName('head')[0];
    let canonical = this.document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    
    if (!canonical) {
      canonical = this.document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      head.appendChild(canonical);
    }
    
    const currentUrl = url || this.document.location.href;
    canonical.setAttribute('href', currentUrl);
  }

  private updateStructuredData(seoData: SEOData): void {
    if (!this.platformService.isBrowser) return;
    
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "ExamEase",
      "description": seoData.description,
      "url": seoData.url || this.document.location.href,
      "applicationCategory": "EducationalApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "author": {
        "@type": "Organization",
        "name": "ExamEase Team"
      }
    };
    
    let script = this.document.querySelector('script[type="application/ld+json"]');
    if (!script) {
      script = this.document.createElement('script');
      script.setAttribute('type', 'application/ld+json');
      this.document.head.appendChild(script);
    }
    
    script.textContent = JSON.stringify(structuredData);
  }

  // Page-specific SEO methods
  setLandingPageSEO(): void {
    this.updateSEO({
      title: 'ExamEase - AI-Powered Study Planning Platform',
      description: 'Transform your exam preparation with AI-powered study plans, intelligent scheduling, and personalized learning support. Join thousands of students achieving academic success.',
      keywords: 'exam preparation, study planning, AI tutoring, academic success, student tools',
      type: 'website'
    });
  }

  setLoginPageSEO(): void {
    this.updateSEO({
      title: 'Login - ExamEase',
      description: 'Sign in to your ExamEase account to access your personalized study plans and AI-powered exam preparation tools.',
      keywords: 'login, sign in, student account, exam preparation',
      type: 'website'
    });
  }

  setRegisterPageSEO(): void {
    this.updateSEO({
      title: 'Create Account - ExamEase',
      description: 'Join ExamEase today and start your journey to academic success with AI-powered study planning and personalized exam preparation.',
      keywords: 'register, sign up, create account, student registration',
      type: 'website'
    });
  }

  setSubjectsPageSEO(): void {
    this.updateSEO({
      title: 'My Subjects - ExamEase',
      description: 'Manage your study subjects, upload materials, and organize your academic content with ExamEase\'s intelligent subject management system.',
      keywords: 'subjects, study materials, academic organization, course management',
      type: 'website'
    });
  }

  setExamPageSEO(): void {
    this.updateSEO({
      title: 'Exam Schedule - ExamEase',
      description: 'Plan and track your upcoming exams with ExamEase\'s intelligent scheduling system. Never miss an important exam date again.',
      keywords: 'exam schedule, exam planning, academic calendar, exam dates',
      type: 'website'
    });
  }

  setStudyPlanSEO(): void {
    this.updateSEO({
      title: 'AI Study Plans - ExamEase',
      description: 'Get personalized, AI-generated study plans tailored to your exam dates, learning style, and academic goals.',
      keywords: 'study plans, AI tutoring, personalized learning, exam preparation strategy',
      type: 'website'
    });
  }
}
